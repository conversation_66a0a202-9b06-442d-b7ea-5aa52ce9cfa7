package com.gclife.common.rabbitmq.configuration;

public final class EndorseEffectMQDefine {
    private EndorseEffectMQDefine(){
    }

    //exchange name
    public static final String DEFAULT_DELAY_EXCHANGE = "ENDORSE_EFFECT_DELAY";

    //延时队列前缀
    public static final String DEAD_LETTER_QUEUE_NAME_PREFIX = "endorse.effect.delay.dead.letter.queue-";

    //DLX DEFAULT QUEUE
    public static final String DEFAULT_DEAD_LETTER_QUEUE_NAME = "endorse.effect.delay.dead.letter.queue";

    //DLX repeat QUEUE 死信转发队列
    public static final String DEFAULT_REPEAT_TRADE_QUEUE_NAME = "endorse.effect.delay.repeat.trade.queue";

    //DLX repeat QUEUE 死信转发队列
    public static final String ENDORSE_EFFECT_DELAY_QUEUE_NAME = "endorse-effect-delay-message";

    //消息发送结果通知队列
    public static final String ENDORSE_EFFECT_DELAY_RESULT_NOTIFICATION_QUEUE_NAME = "endorse-effect-delay-message-result-notification";


}
