package com.gclife.common.rabbitmq;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.amqp.rabbit.support.CorrelationData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * <AUTHOR>
 * create 17-11-7
 * description: 同步发送消息到Rabbitmq
 */
@Component
public class SyncSenderImpl implements RabbitTemplate.ConfirmCallback,RabbitTemplate.ReturnCallback{

    private RabbitTemplate rabbitTemplate;

    @Autowired
    public void setRabbitTemplate(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
        rabbitTemplate.setMandatory(true);
        rabbitTemplate.setReplyTimeout(10000);
        rabbitTemplate.setConfirmCallback(this);
        rabbitTemplate.setReturnCallback(this);
    }

    public void sendMsg(String queueName, String msg)
    {
        CorrelationData correlationId = new CorrelationData(UUID.randomUUID().toString());
        rabbitTemplate.convertAndSend(queueName, (Object) msg, correlationId);
    }

    @Override
    public void confirm(CorrelationData correlationData, boolean b, String s) {
    }

    @Override
    public void returnedMessage(Message message, int i, String s, String s1, String s2) {
    }
}
