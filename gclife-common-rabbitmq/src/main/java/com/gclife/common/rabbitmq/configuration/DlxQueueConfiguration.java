package com.gclife.common.rabbitmq.configuration;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class DlxQueueConfiguration {

    //初始化转发队列
    @Bean
    public Queue repeatTradeQueue() {
        Queue queue = new Queue(MqEnum.DEFAULT_REPEAT_TRADE_QUEUE_NAME,true,false,false);
        return queue;
    }

    //kshop.repeat.trade.queue 转发队列绑定到交换机上
    @Bean
    public Binding  drepeatTradeBinding() {
        return BindingBuilder.bind(repeatTradeQueue()).to(defaultExchange()).with(MqEnum.DEFAULT_REPEAT_TRADE_QUEUE_NAME);
    }



    //定义默认死信队列
    @Bean
    public Queue deadLetterQueue() {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("x-dead-letter-exchange", MqEnum.DEFAULT_DELAY_EXCHANGE);
        arguments.put("x-dead-letter-routing-key", MqEnum.DEFAULT_REPEAT_TRADE_QUEUE_NAME);
        Queue queue = new Queue(MqEnum.DEFAULT_DEAD_LETTER_QUEUE_NAME,true,false,false,arguments);
        System.out.println("arguments :" + queue.getArguments());
        return queue;
    }

    //kshop.dead.letter.queue队列绑定到交换机上
    @Bean
    public Binding  deadLetterBinding() {
        return BindingBuilder.bind(deadLetterQueue()).to(defaultExchange()).with(MqEnum.DEFAULT_DEAD_LETTER_QUEUE_NAME);
    }


    //第三方发送用户消息队列
    @Bean
    public Queue thirdCustomerMessageQueue() {
        return new Queue(MqEnum.THIRD_SEND_USER_MESSAGE_QUEUE_NAME,true,false,false);
    }

    //第三方发送用户消息队列绑定
    @Bean
    public Binding  thirdCustomerMessageQueueBinding() {
        return BindingBuilder.bind(thirdCustomerMessageQueue()).to(defaultExchange()).with(MqEnum.THIRD_SEND_USER_MESSAGE_QUEUE_NAME);
    }

    //消息交换机信道配置
    @Bean
    public DirectExchange defaultExchange() {
        return new DirectExchange(MqEnum.DEFAULT_DELAY_EXCHANGE, true, false);
    }




    //第三方发送消息结果通知
    @Bean
    public Queue thirdSendResultNotificationQueue() {
        Queue queue = new Queue(MqEnum.THIRD_SEND_USER_MESSAGE_RESULT_NOTIFICATION_QUEUE_NAME,true,false,false);
        return queue;
    }

    //第三方发送消息结果通知
    @Bean
    public Binding  thirdSendResultNotificationBinding() {
        return BindingBuilder.bind(repeatTradeQueue()).to(defaultExchange()).with(MqEnum.THIRD_SEND_USER_MESSAGE_RESULT_NOTIFICATION_QUEUE_NAME);
    }

    //销售APP电子学习考试交卷队列
    @Bean
    public Queue agentSendUserSubmitExamQueueName() {
        return new Queue(MqEnum.AGENT_SEND_USER_SUBMIT_EXAM_QUEUE_NAME, true, false, false);
    }

    //销售APP电子学习考试交卷队列绑定
    @Bean
    public Binding agentSendUserSubmitExamQueueNameBinding() {
        return BindingBuilder.bind(repeatTradeQueue()).to(defaultExchange()).with(MqEnum.AGENT_SEND_USER_SUBMIT_EXAM_QUEUE_NAME);
    }
}
