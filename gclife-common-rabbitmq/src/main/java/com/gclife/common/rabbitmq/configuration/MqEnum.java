package com.gclife.common.rabbitmq.configuration;

public final class MqEnum {
    private MqEnum(){
    }

    //exchange name
    public static final String DEFAULT_DELAY_EXCHANGE = "DELAY";

    //延时队列前缀
    public static final String DEAD_LETTER_QUEUE_NAME_PREFIX = "delay.dead.letter.queue-";

    //DLX DEFAULT QUEUE
    public static final String DEFAULT_DEAD_LETTER_QUEUE_NAME = "delay.dead.letter.queue";

    //DLX repeat QUEUE 死信转发队列
    public static final String DEFAULT_REPEAT_TRADE_QUEUE_NAME = "delay.repeat.trade.queue";

    //DLX repeat QUEUE 死信转发队列
    public static final String THIRD_SEND_USER_MESSAGE_QUEUE_NAME = "third-user-message";

    //消息发送结果通知队列
    public static final String THIRD_SEND_USER_MESSAGE_RESULT_NOTIFICATION_QUEUE_NAME = "third-user-message-result-notification";

    //销售APP电子学习考试交卷 死信转发队列
    public static final String AGENT_SEND_USER_SUBMIT_EXAM_QUEUE_NAME = "agent-user-submit-exam";


}
