package com.gclife.common.rabbitmq;

import com.gclife.common.rabbitmq.configuration.*;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * create 17-11-7
 * description:异步发送消息到Rabbitmq
 */
@Component
public class AsyncSenderImpl implements RabbitTemplate.ConfirmCallback,RabbitTemplate.ReturnCallback,MsgAsynProducer{

    private RabbitTemplate rabbitTemplate;
    @Autowired
    private RabbitAdmin rabbitAdmin;
    @Autowired
    private DlxQueueConfiguration dlxQueueConfiguration;
    @Autowired
    private PolicyExpireDlxQueueConfiguration policyExpireDlxQueueConfiguration;
    @Autowired
    private EndorseEffectDlxQueueConfiguration endorseEffectDlxQueueConfiguration;

    @Autowired
    public void setRabbitTemplate(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
        //rabbitTemplate如果为单例的话，那回调就是最后设置的内容
        rabbitTemplate.setConfirmCallback(this);
        rabbitTemplate.setReturnCallback(this);
        rabbitTemplate.setMandatory(true);
        rabbitTemplate.setReplyTimeout(10000);
    }

    /**
     * 发送消息
     * @param queueName 队列名称
     * @param msg 消息载体
     */
    @Override
    public void sendMsg(String queueName, String msg)
    {
        CorrelationData correlationId = new CorrelationData(UUID.randomUUID().toString());
        rabbitTemplate.convertAndSend(queueName, (Object) msg, correlationId);
    }

    /**
     * 发送延迟消息
     * @param msg 消息载体
     * @param times 过期时间
     */
    @Override
    public void sendDelayMsg(String queueName,String msg, long times) {
        //创建队列
        Queue queue = createDelayQueue(queueName, times,false);
        //发送消息
        CorrelationData correlationId = new CorrelationData(UUID.randomUUID().toString());
        MessagePostProcessor processor = new MessagePostProcessor(){
            @Override
            public Message postProcessMessage(Message message) throws AmqpException {
                message.getMessageProperties().setExpiration(times + "");
                return message;
            }
        };
        rabbitTemplate.convertAndSend(MqEnum.DEFAULT_DELAY_EXCHANGE, queue.getName(), msg, processor,correlationId);
    }

    /**
     * 发送延迟消息
     * @param queueName 发送队列 延时后转发的队列
     * @param msg 消息体
     * @param times 延迟时间
     * @param precise 延时队列精确标识(到秒)
     */
    @Override
    public void sendDelayMsg(String queueName, String msg, long times, boolean precise) {
        //创建队列
        Queue queue = createDelayQueue(queueName,times,precise);
        CorrelationData correlationId = new CorrelationData(UUID.randomUUID().toString());
        MessagePostProcessor processor = new MessagePostProcessor(){
            @Override
            public Message postProcessMessage(Message message) throws AmqpException {
                message.getMessageProperties().setExpiration(times + "");
                return message;
            }
        };
        rabbitTemplate.convertAndSend(MqEnum.DEFAULT_DELAY_EXCHANGE, queue.getName(), msg, processor,correlationId);
    }

    @Override
    public void confirm(CorrelationData correlationData, boolean b, String s) {
    }

    @Override
    public void returnedMessage(Message message, int i, String s, String s1, String s2) {
        System.out.println(message);
    }


    /**
     * 创建延迟消息队列
     * @param queueName 转发消息队列名称
     * @param times 延迟时间
     * @param precise 精确到秒
     * @return Queue
     */
    private Queue createDelayQueue(String queueName, long times,boolean precise) {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("x-dead-letter-exchange", MqEnum.DEFAULT_DELAY_EXCHANGE);
        if(queueName==null || "".equals(queueName)){
            arguments.put("x-dead-letter-routing-key", MqEnum.DEFAULT_REPEAT_TRADE_QUEUE_NAME);
        }else {
            arguments.put("x-dead-letter-routing-key", queueName);
        }
        //获取死信队列的名称
        Long score = System.currentTimeMillis()+times;
        Date date = new Date(score);
        String suffix="";
        if(times>24*60*60*1000){
            //跨天
            suffix =new SimpleDateFormat("DDHH0000").format(date);
        }else {
            if(precise){
                suffix=new SimpleDateFormat("00HHmmss").format(date);
            }else {
                suffix= new SimpleDateFormat("00HH0000").format(date);
            }
        }
        Queue queue = new Queue(MqEnum.DEAD_LETTER_QUEUE_NAME_PREFIX+suffix,true,false,false,arguments);
        //添加队列
        rabbitAdmin.declareQueue(queue);
        Binding binding = BindingBuilder.bind(queue).to(dlxQueueConfiguration.defaultExchange()).withQueueName();
        //添加交换机绑定
        rabbitAdmin.declareBinding(binding);
        return queue;
    }

    /**
     * 创建延迟消息队列
     * @param routingQueueName 转发消息队列名称
     * @param times 延迟时间
     * @param precise 精确到秒
     * @return Queue
     */
    private Queue createDelayQueue(String defaultRoutingQueueName, String routingQueueName, String exchangeName,
                                   String delayQueuePrefix, DirectExchange directExchange,
                                   long times,boolean precise) {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("x-dead-letter-exchange", exchangeName);
        if(routingQueueName==null || "".equals(routingQueueName)){
            arguments.put("x-dead-letter-routing-key", defaultRoutingQueueName);
        }else {
            arguments.put("x-dead-letter-routing-key", routingQueueName);
        }
        //获取死信队列的名称
        Queue queue = new Queue(delayQueuePrefix + getDeadLetterQueueSuffix(times, precise),
                true,false,false, arguments);
        //添加队列
        rabbitAdmin.declareQueue(queue);
        Binding binding = BindingBuilder.bind(queue).to(directExchange).withQueueName();
        //添加交换机绑定
        rabbitAdmin.declareBinding(binding);
        return queue;
    }

    private String getDeadLetterQueueSuffix(long times, boolean precise)
    {
        Long score = System.currentTimeMillis()+times;
        Date date = new Date(score);
        String suffix="";
        if(times>24*60*60*1000){
            //跨天
            suffix =new SimpleDateFormat("DDHH0000").format(date);
        }else {
            if(precise){
                suffix=new SimpleDateFormat("00HHmmss").format(date);
            }else {
                suffix= new SimpleDateFormat("00HH0000").format(date);
            }
        }

        return suffix;
    }

    @Override
    public void sendDelayMsgByType(String businessType, String msg, long times) {
        sendDelayMsgByType(businessType, msg, times, false);
    }

    @Override
    public void sendDelayMsgByType(String businessType, String msg, long times, boolean precise) {
        String defaultRoutingQueueName = null;
        String routingQueueName = null;
        String exchangeName = null;
        String delayQueuePrefix = null;
        DirectExchange directExchange = null;

        //获取对应的队列名
        if (MQTermEnum.QUEUE_BUSINESS_TYPE.BUSINESS_TYPE_POLICY_EXPIRE.name().equals(businessType))
        {
            defaultRoutingQueueName = PolicyExpireMQDefine.DEFAULT_REPEAT_TRADE_QUEUE_NAME;
            routingQueueName = PolicyExpireMQDefine.POLICY_EXPIRE_DELAY_QUEUE_NAME;
            exchangeName = PolicyExpireMQDefine.DEFAULT_DELAY_EXCHANGE;
            delayQueuePrefix = PolicyExpireMQDefine.DEAD_LETTER_QUEUE_NAME_PREFIX;
            directExchange = policyExpireDlxQueueConfiguration.policyExpireDefaultExchange();
        } else if(MQTermEnum.QUEUE_BUSINESS_TYPE.BUSINESS_TYPE_ENDORSE_EFFECT.name().equals(businessType))
        {
            defaultRoutingQueueName = EndorseEffectMQDefine.DEFAULT_REPEAT_TRADE_QUEUE_NAME;
            routingQueueName = EndorseEffectMQDefine.ENDORSE_EFFECT_DELAY_QUEUE_NAME;
            exchangeName = EndorseEffectMQDefine.DEFAULT_DELAY_EXCHANGE;
            delayQueuePrefix = EndorseEffectMQDefine.DEAD_LETTER_QUEUE_NAME_PREFIX;
            directExchange = endorseEffectDlxQueueConfiguration.endorseEffectDefaultExchange();
        }

        else {
            return;
        }

        //创建队列
        Queue queue = createDelayQueue(defaultRoutingQueueName, routingQueueName,
                exchangeName, delayQueuePrefix, directExchange,
                times, precise);

        //发送消息
        CorrelationData correlationId = new CorrelationData(UUID.randomUUID().toString());
        MessagePostProcessor processor = new MessagePostProcessor(){
            @Override
            public Message postProcessMessage(Message message) throws AmqpException {
                message.getMessageProperties().setExpiration(times + "");
                return message;
            }
        };
        rabbitTemplate.convertAndSend(exchangeName, queue.getName(), msg, processor,correlationId);
    }

    /**
     * 创建延迟消息队列
     * @param times 延迟时间
     * @param precise 精确到秒
     * @return Queue
     */
    private Queue createDelayQueue(DelayQueue delayQueue, long times,boolean precise) {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("x-dead-letter-exchange", delayQueue.getProperty().getExchange());
        arguments.put("x-dead-letter-routing-key", delayQueue.getProperty().getBizProcessQueue());

        //获取死信队列的名称
        Long score = System.currentTimeMillis()+times;
        Date date = new Date(score);
        String suffix="";
        if(times>24*60*60*1000){
            //跨天
            suffix =new SimpleDateFormat("DDHH0000").format(date);
        }else {
            if(precise){
                suffix=new SimpleDateFormat("00HHmmss").format(date);
            }else {
                suffix= new SimpleDateFormat("00HH0000").format(date);
            }
        }
        Queue queue = new Queue(delayQueue.getProperty().getDeadLetterQueue() + suffix,true,false,false,arguments);
        //添加队列
        rabbitAdmin.declareQueue(queue);
        Binding binding = BindingBuilder.bind(queue).to(delayQueue.getExchange()).withQueueName();
        //添加交换机绑定
        rabbitAdmin.declareBinding(binding);
        return queue;
    }

    @Override
    public void sendToDelayQueue(String queueName, String msg, long times) {
        sendToDelayQueue(queueName, msg, times, false);
    }

    @Override
    public void sendToDelayQueue(String queueName, String msg, long times, boolean precise) {
        //创建队列
        DelayQueue delayQueue = DelayQueueConfiguration.getDelayQueue(queueName);
        Queue queue = createDelayQueue(delayQueue, times, precise);
        CorrelationData correlationId = new CorrelationData(UUID.randomUUID().toString());
        MessagePostProcessor processor = new MessagePostProcessor(){
            @Override
            public Message postProcessMessage(Message message) throws AmqpException {
                message.getMessageProperties().setExpiration(times + "");
                return message;
            }
        };
        rabbitTemplate.convertAndSend(delayQueue.getProperty().getExchange(), queue.getName(), msg, processor,correlationId);
    }
}
