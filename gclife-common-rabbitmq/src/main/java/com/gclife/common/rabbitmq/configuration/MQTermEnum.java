package com.gclife.common.rabbitmq.configuration;

/**
 * 术语 枚举
 *
 * <AUTHOR>
 */
public class MQTermEnum {

    private MQTermEnum() {
        throw new AssertionError();
    }

    public interface ConstantType {
    }

    /**
     * 佣金业务类型
     */
    public enum QUEUE_BUSINESS_TYPE implements ConstantType {
        BUSINESS_TYPE_POLICY_EXPIRE("保单满期"),
        BUSINESS_TYPE_ENDORSE_EFFECT("保全生效");


        private String code;
        private String desc;

        QUEUE_BUSINESS_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

}
