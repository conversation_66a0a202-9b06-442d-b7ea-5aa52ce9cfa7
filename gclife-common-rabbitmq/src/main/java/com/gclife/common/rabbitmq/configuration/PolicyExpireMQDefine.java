package com.gclife.common.rabbitmq.configuration;

public final class PolicyExpireMQDefine {
    private PolicyExpireMQDefine(){
    }

    //exchange name
    public static final String DEFAULT_DELAY_EXCHANGE = "POLICY_EXPIRE_DELAY";

    //延时队列前缀
    public static final String DEAD_LETTER_QUEUE_NAME_PREFIX = "policy.expire.delay.dead.letter.queue-";

    //DLX DEFAULT QUEUE
    public static final String DEFAULT_DEAD_LETTER_QUEUE_NAME = "policy.expire.delay.dead.letter.queue";

    //DLX repeat QUEUE 死信转发队列
    public static final String DEFAULT_REPEAT_TRADE_QUEUE_NAME = "policy.expire.delay.repeat.trade.queue";

    //DLX repeat QUEUE 死信转发队列
    public static final String POLICY_EXPIRE_DELAY_QUEUE_NAME = "policy-expire-delay-message";

    //消息发送结果通知队列
    public static final String POLICY_EXPIRE_DELAY_RESULT_NOTIFICATION_QUEUE_NAME = "policy-expire-delay-message-result-notification";


}
