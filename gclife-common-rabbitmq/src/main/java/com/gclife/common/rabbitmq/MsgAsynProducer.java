package com.gclife.common.rabbitmq;

/**
 * <AUTHOR>
 * create 17-11-7
 * description:消息异步发送者
 */

public interface MsgAsynProducer {

    /**
     * 发送消息
     * @param queueName 消息队列名称
     * @param msg 消息体
     */
    public void sendMsg(String queueName, String msg);

    /**
     * 发送延迟消息
     * @param queueName 发送队列 延时后转发的队列
     * @param msg 消息体
     * @param times 延迟时间
     */
    public void sendDelayMsg(String queueName,String msg, long times);

    /**
     * 发送延迟消息
     * @param queueName 发送队列 延时后转发的队列
     * @param msg 消息体
     * @param times 延迟时间
     * @param precise 延时队列精确标识(到秒)
     */
    public void sendDelayMsg(String queueName,String msg, long times,boolean precise);

    /**
     * 发送延迟消息
     * @param businessType 业务类型
     * @param msg 消息体
     * @param times 延迟时间
     */
    public void sendDelayMsgByType(String businessType,String msg, long times);

    /**
     * 发送延迟消息
     * @param businessType 业务类型
     * @param msg 消息体
     * @param times 延迟时间
     * @param precise 延时队列精确标识(到秒)
     */
    public void sendDelayMsgByType(String businessType,String msg, long times,boolean precise);

    /**
     * 发送延迟消息
     * @param msg 消息体
     * @param times 延迟时间
     */
    public void sendToDelayQueue(String queueName, String msg, long times);

    /**
     * 发送延迟消息
     * @param msg 消息体
     * @param times 延迟时间
     * @param precise 延时队列精确标识(到秒)
     */
    public void sendToDelayQueue(String queueName, String msg, long times, boolean precise);

}
