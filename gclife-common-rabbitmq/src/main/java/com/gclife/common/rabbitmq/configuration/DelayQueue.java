package com.gclife.common.rabbitmq.configuration;

import lombok.Data;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Data
public class DelayQueue {

    private DelayQueueConfigProperty property;

    //转发队列
    private Queue repeatTradeQueue;

    //交换机
    private DirectExchange exchange;

    //缺省死信队列
    private Queue deadLetterQueue;

    //业务处理队列
    private Queue bizProcessQueue;

    //消息通知队列
    private Queue notificationQueue;

    public void createQueueAndExchange(RabbitAdmin rabbitAdmin) {

        //创建交换机
        exchange = new DirectExchange(property.getExchange(), true, false);
        rabbitAdmin.declareExchange(exchange);

        //转发队列
        repeatTradeQueue = new Queue(property.getRepeatTradeQueue(), true,false,false);
        rabbitAdmin.declareQueue(repeatTradeQueue);

        Binding repeatTrade2Exchange = BindingBuilder.bind(repeatTradeQueue).to(exchange).with(property.getRepeatTradeQueue());
        rabbitAdmin.declareBinding(repeatTrade2Exchange);

        //死信队列
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("x-dead-letter-exchange", property.getExchange());
        arguments.put("x-dead-letter-routing-key", property.getRepeatTradeQueue());
        deadLetterQueue = new Queue(property.getDeadLetterQueue(), true,false,false,arguments);
        rabbitAdmin.declareQueue(deadLetterQueue);

        Binding deadLetter2Exchange = BindingBuilder.bind(deadLetterQueue).to(exchange).with(property.getDeadLetterQueue());
        rabbitAdmin.declareBinding(deadLetter2Exchange);

        //业务处理队列
        bizProcessQueue = new Queue(property.getBizProcessQueue(), true,false,false);
        rabbitAdmin.declareQueue(bizProcessQueue);

        Binding bizProcess2Exchange = BindingBuilder.bind(bizProcessQueue).to(exchange).with(property.getBizProcessQueue());
        rabbitAdmin.declareBinding(bizProcess2Exchange);

        //消息通知队列
        notificationQueue = new Queue(property.getNotificationQueue(), true,false,false);
        rabbitAdmin.declareQueue(notificationQueue);

        Binding notification2Exchange = BindingBuilder.bind(notificationQueue).to(exchange).with(property.getNotificationQueue());
        rabbitAdmin.declareBinding(notification2Exchange);
    }

}
