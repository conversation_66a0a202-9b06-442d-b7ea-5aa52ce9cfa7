package com.gclife.common.rabbitmq.configuration;

import com.gclife.common.rabbitmq.Receiver;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.core.annotation.Order;

import java.util.Map;
import java.util.stream.Collectors;

@Configuration
@EnableConfigurationProperties(DelayQueryConfigProperties.class)
public class DelayQueueConfiguration {

    private DelayQueryConfigProperties delayQueryConfigProperties;
    private static Map<String, DelayQueue> delayQueueMap;

    @Autowired
    public DelayQueueConfiguration(DelayQueryConfigProperties delayQueryConfigProperties) {
        this.delayQueryConfigProperties = delayQueryConfigProperties;
    }

    public static DelayQueue getDelayQueue(String bizProcessQueue) {
        DelayQueue delayQueue = delayQueueMap.get(bizProcessQueue);
        if (delayQueue == null) {
            throw new IllegalArgumentException(String.format("未找到对应bizProcessQueue=[%s]的配置，请核实！", bizProcessQueue));
        }

        return delayQueue;
    }

    @Bean
    public Object initQueueAndExchange(RabbitAdmin rabbitAdmin) {
        if(this.delayQueryConfigProperties != null &&
                this.delayQueryConfigProperties.getConfigs() != null &&
                this.delayQueryConfigProperties.getConfigs().size() > 0) {
            delayQueueMap = this.delayQueryConfigProperties.getConfigs()
                    .stream()
                    .map(a -> {
                        DelayQueue delayQueue = new DelayQueue();
                        DelayQueueConfigProperty delayQueueConfigProperty = new DelayQueueConfigProperty();
                        delayQueueConfigProperty.setBizProcessQueue(a.getBizProcessQueue());
                        delayQueueConfigProperty.setDeadLetterQueue(a.getDeadLetterQueue());
                        delayQueueConfigProperty.setExchange(a.getExchange());
                        delayQueueConfigProperty.setNotificationQueue(a.getNotificationQueue());
                        delayQueueConfigProperty.setRepeatTradeQueue(a.getRepeatTradeQueue());

                        delayQueue.setProperty(delayQueueConfigProperty);
                        //创建各种队列
                        delayQueue.createQueueAndExchange(rabbitAdmin);

                        return delayQueue;
                    }).collect(Collectors.toMap(a -> a.getProperty().getBizProcessQueue(), a -> a));
        }
        return Boolean.TRUE;
    }

//    @Override
//    public void afterPropertiesSet() throws Exception {
//        RabbitmqConfiguration rabbitmqConfiguration;
//
//    }
}
