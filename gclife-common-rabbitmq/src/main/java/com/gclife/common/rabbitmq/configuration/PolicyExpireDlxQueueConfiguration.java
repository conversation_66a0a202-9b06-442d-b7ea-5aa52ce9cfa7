package com.gclife.common.rabbitmq.configuration;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class PolicyExpireDlxQueueConfiguration {

    //初始化转发队列
    @Bean
    public Queue policyExpireRepeatTradeQueue() {
        Queue queue = new Queue(PolicyExpireMQDefine.DEFAULT_REPEAT_TRADE_QUEUE_NAME,true,false,false);
        return queue;
    }

    //kshop.repeat.trade.queue 转发队列绑定到交换机上
    @Bean
    public Binding policyExpireRepeatTradeBinding() {
        return BindingBuilder.bind(policyExpireRepeatTradeQueue()).to(policyExpireDefaultExchange()).with(PolicyExpireMQDefine.DEFAULT_REPEAT_TRADE_QUEUE_NAME);
    }



    //定义默认死信队列
    @Bean
    public Queue policyExpireDeadLetterQueue() {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("x-dead-letter-exchange", PolicyExpireMQDefine.DEFAULT_DELAY_EXCHANGE);
        arguments.put("x-dead-letter-routing-key", PolicyExpireMQDefine.DEFAULT_REPEAT_TRADE_QUEUE_NAME);
        Queue queue = new Queue(PolicyExpireMQDefine.DEFAULT_DEAD_LETTER_QUEUE_NAME,true,false,false,arguments);
        System.out.println("arguments :" + queue.getArguments());
        return queue;
    }

    //kshop.dead.letter.queue队列绑定到交换机上
    @Bean
    public Binding  deadLetterBinding() {
        return BindingBuilder.bind(policyExpireDeadLetterQueue()).to(policyExpireDefaultExchange()).with(PolicyExpireMQDefine.DEFAULT_DEAD_LETTER_QUEUE_NAME);
    }


    //第三方发送用户消息队列
    @Bean
    public Queue policyExpireDelayMessageQueue() {
        return new Queue(PolicyExpireMQDefine.POLICY_EXPIRE_DELAY_QUEUE_NAME,true,false,false);
    }

    //第三方发送用户消息队列绑定
    @Bean
    public Binding policyExpireDelayMessageQueueBinding() {
        return BindingBuilder.bind(policyExpireDelayMessageQueue()).to(policyExpireDefaultExchange()).with(PolicyExpireMQDefine.POLICY_EXPIRE_DELAY_QUEUE_NAME);
    }

    //消息交换机信道配置
    @Bean
    public DirectExchange policyExpireDefaultExchange() {
        return new DirectExchange(PolicyExpireMQDefine.DEFAULT_DELAY_EXCHANGE, true, false);
    }




    //第三方发送消息结果通知
    @Bean
    public Queue PolicyDelayResultNotificationQueue() {
        Queue queue = new Queue(PolicyExpireMQDefine.POLICY_EXPIRE_DELAY_RESULT_NOTIFICATION_QUEUE_NAME,true,false,false);
        return queue;
    }

    //第三方发送消息结果通知
    @Bean
    public Binding PolicyDelayResultNotificationBinding() {
        return BindingBuilder.bind(policyExpireRepeatTradeQueue()).to(policyExpireDefaultExchange()).with(PolicyExpireMQDefine.POLICY_EXPIRE_DELAY_RESULT_NOTIFICATION_QUEUE_NAME);
    }

}
