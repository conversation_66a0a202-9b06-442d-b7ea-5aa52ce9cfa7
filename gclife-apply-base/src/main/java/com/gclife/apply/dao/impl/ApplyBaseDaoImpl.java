package com.gclife.apply.dao.impl;

import com.gclife.apply.core.jooq.tables.pojos.*;
import com.gclife.apply.core.jooq.tables.records.*;
import com.gclife.apply.dao.ApplyBaseDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.vo.ApplyInsuredPrintVo;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.pojo.BasePojo;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.api.model.response.SaleApplyPolicyBo;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.modelmapper.TypeToken;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.apply.core.jooq.Tables.APPLY;
import static com.gclife.apply.core.jooq.Tables.*;
import static com.gclife.apply.core.jooq.tables.ApplyAttachment.APPLY_ATTACHMENT;
import static com.gclife.apply.core.jooq.tables.ApplyBeneficiary.APPLY_BENEFICIARY;
import static com.gclife.apply.model.config.ApplyTermEnum.APPLY_STATUS_FLAG.*;
import static com.gclife.apply.model.config.ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP;
import static com.gclife.apply.model.config.ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_PERSONAL;
import static com.gclife.apply.model.config.ApplyTermEnum.CHANNEL_TYPE.BANK;
import static com.gclife.apply.model.config.ApplyTermEnum.VALID_FLAG.effective;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 投保单基础DAO
 * @date 18-4-27
 */
@Component
public class ApplyBaseDaoImpl extends BaseDaoImpl implements ApplyBaseDao {

    /**
     * 查询投保单基本信息
     *
     * @param applyId 投保单号
     * @return
     */
    @Override
    public ApplyPo getApply(String applyId) {
        return this.getDslContext()
                .select(APPLY.fields())
                .from(APPLY)
                .where(APPLY.APPLY_ID.eq(applyId))
                .and(APPLY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchOneInto(ApplyPo.class);
    }

    @Override
    public ApplyPo getApplyByApplyNo(String applyNo) {
        return this.getDslContext()
                .select(APPLY.fields())
                .from(APPLY)
                .where(APPLY.APPLY_NO.eq(applyNo))
                .and(APPLY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchOneInto(ApplyPo.class);
    }

    /**
     * 根据代理人ID查询投保单数据
     *
     * @param agentIds    代理人ID
     * @param applyStatus 投保单状态
     * @return 投保单list
     */
    @Override
    public List<ApplyPo> getApplyByAgentId(List<String> agentIds, String applyStatus) {
        List<ApplyPo> applyPos;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(APPLY_AGENT.AGENT_ID.in(agentIds));
            if (AssertUtils.isNotEmpty(applyStatus)) {
                conditions.add(APPLY.APPLY_STATUS.eq(applyStatus));
            } else {
                List<String> applyStatusList = new ArrayList<>();
                applyStatusList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL.name());
                applyStatusList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL_COMPLETE.name());
                conditions.add(APPLY.APPLY_STATUS.isNotNull());
                conditions.add(APPLY.APPLY_STATUS.notIn(applyStatusList));
            }
            applyPos = this.getDslContext()
                    .select(APPLY.fields())
                    .from(APPLY)
                    .innerJoin(APPLY_AGENT).on(APPLY.APPLY_ID.eq(APPLY_AGENT.APPLY_ID))
                    .where(conditions)
                    .fetchInto(ApplyPo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_ERROR);
        }
        return applyPos;
    }

    @Override
    public List<ApplyPo> getApplyByApplyId(List<String> applyIds) {
        List<ApplyPo> applyPos;
        applyPos = this.getDslContext()
                .select(APPLY.fields())
                .from(APPLY)
                .where(APPLY.APPLY_ID.in(applyIds)).and(APPLY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(ApplyPo.class);
        return applyPos;
    }

    /**
     * 根据投保单ID查询投保人信息
     *
     * @param applyId 投保单ID
     * @return ApplyApplicantPo
     */
    @Override
    public ApplyApplicantPo getApplyApplicant(String applyId) {
        ApplyApplicantPo applyApplicantPo;
        try {
            applyApplicantPo = this.getDslContext()
                    .select(APPLY_APPLICANT.fields())
                    .from(APPLY_APPLICANT)
                    .where(APPLY_APPLICANT.APPLY_ID.eq(applyId))
                    .fetchOneInto(ApplyApplicantPo.class);
        } catch (Exception e) {
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_APPLICANT_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_APPLICANT_ERROR);
        }
        return applyApplicantPo;
    }

    /**
     * 查询投保单附件信息列表
     *
     * @param applyId            投保单ID
     * @param attachmentTypeCode 投保单附件类形编码
     * @return list
     */
    @Override
    public List<ApplyAttachmentPo> getApplyAttachmentList(String applyId, String attachmentTypeCode) {
        List<ApplyAttachmentPo> applyAttachmentPos;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(APPLY_ATTACHMENT.APPLY_ID.eq(applyId));
            if (AssertUtils.isNotEmpty(attachmentTypeCode)) {
                conditions.add(APPLY_ATTACHMENT.ATTACHMENT_TYPE_CODE.eq(attachmentTypeCode));
            }
            SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                    .select(APPLY_ATTACHMENT.fields())
                    .from(APPLY_ATTACHMENT)
                    .where(conditions);
            applyAttachmentPos = selectConditionStep.fetchInto(ApplyAttachmentPo.class);
            getLogger().info("getApplyAttachmentListSQL：{} applyId：{}, attachmentTypeCode：{}", selectConditionStep, applyId, attachmentTypeCode);
        } catch (Exception e) {
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_ATTACHMENT_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_ATTACHMENT_ERROR);
        }
        return applyAttachmentPos;
    }

    /**
     * 查询投保单附件信息列表
     *
     * @param applyId             投保单ID
     * @param attachmentTypeCodes 投保单附件类形编码
     * @return list
     */
    @Override
    public List<ApplyAttachmentPo> getApplyAttachmentList(String applyId, List<String> attachmentTypeCodes) {
        List<ApplyAttachmentPo> applyAttachmentPos;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(APPLY_ATTACHMENT.APPLY_ID.eq(applyId));
            if (AssertUtils.isNotEmpty(attachmentTypeCodes)) {
                conditions.add(APPLY_ATTACHMENT.ATTACHMENT_TYPE_CODE.in(attachmentTypeCodes));
            }
            applyAttachmentPos = this.getDslContext()
                    .select(APPLY_ATTACHMENT.fields())
                    .from(APPLY_ATTACHMENT)
                    .where(conditions)
                    .fetchInto(ApplyAttachmentPo.class);
        } catch (Exception e) {
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_ATTACHMENT_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_ATTACHMENT_ERROR);
        }
        return applyAttachmentPos;
    }

    /**
     * 查询有效的投保单支付附件
     *
     * @param applyId            投保单ID
     * @param attachmentTypeCode 附件类型
     * @return list
     */
    @Override
    public List<ApplyPaymentAttachmentPo> getApplyPaymentAttachmentList(String applyId, String attachmentTypeCode) {
        List<ApplyPaymentAttachmentPo> applyPaymentAttachmentPos;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(APPLY_PAYMENT_ATTACHMENT.APPLY_ID.eq(applyId));
            conditions.add(APPLY_PAYMENT_ATTACHMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
            if (AssertUtils.isNotEmpty(attachmentTypeCode)) {
                conditions.add(APPLY_PAYMENT_ATTACHMENT.ATTACHMENT_TYPE_CODE.eq(attachmentTypeCode));
            }
            applyPaymentAttachmentPos = this.getDslContext()
                    .select(APPLY_PAYMENT_ATTACHMENT.fields())
                    .from(APPLY_PAYMENT_ATTACHMENT)
                    .where(conditions)
                    .fetchInto(ApplyPaymentAttachmentPo.class);
        } catch (Exception e) {
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_PAYMENT_ATTACHMENT_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_PAYMENT_ATTACHMENT_ERROR);
        }
        return applyPaymentAttachmentPos;
    }

    /**
     * 查询团险投保单险种列表(无被保人)
     *
     * @param applyId 投保单ID
     * @return list
     */
    @Override
    public List<ApplyCoveragePo> getApplyCoverageList(String applyId) {
        List<ApplyCoveragePo> applyCoveragePos;
        try {
            applyCoveragePos = this.getDslContext()
                    .select(APPLY_COVERAGE.fields())
                    .from(APPLY_COVERAGE)
                    .where(APPLY_COVERAGE.APPLY_ID.eq(applyId))
                    .and(APPLY_COVERAGE.INSURED_ID.isNull())
                    .orderBy(APPLY_COVERAGE.PRIMARY_FLAG.sortAsc(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                    .fetchInto(ApplyCoveragePo.class);
        } catch (Exception e) {
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_COVERAGE_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_COVERAGE_ERROR);
        }
        return applyCoveragePos;
    }

    /**
     * 团险查询投保单险种信息列表
     *
     * @param applyId 投保单ID
     * @return list
     */
    @Override
    public List<ApplyCoveragePo> getGroupApplyCoverageList(String applyId, String insuredId) {
        List<ApplyCoveragePo> applyCoveragePos;
        try {
            applyCoveragePos = this.getDslContext()
                    .select(APPLY_COVERAGE.fields())
                    .from(APPLY_COVERAGE)
                    .where(APPLY_COVERAGE.APPLY_ID.eq(applyId).and(APPLY_COVERAGE.INSURED_ID.eq(insuredId)))
                    .orderBy(APPLY_COVERAGE.PRIMARY_FLAG.sortAsc(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                    .fetchInto(ApplyCoveragePo.class);
        } catch (Exception e) {
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_COVERAGE_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_COVERAGE_ERROR);
        }
        return applyCoveragePos;
    }

    /**
     * 仅仅根据applyId查询投保单主险险种信息
     *
     * @param applyId 投保单ID
     * @return list
     */
    @Override
    public List<ApplyCoveragePo> getOnlyApplyCoverageList(String applyId) {
        List<ApplyCoveragePo> applyCoveragePos;
        try {
            applyCoveragePos = this.getDslContext()
                    .select(APPLY_COVERAGE.fields())
                    .from(APPLY_COVERAGE)
                    .where(APPLY_COVERAGE.APPLY_ID.eq(applyId))
                    .and(APPLY_COVERAGE.PRIMARY_FLAG.eq("MAIN"))
                    .fetchInto(ApplyCoveragePo.class);
        } catch (Exception e) {
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_COVERAGE_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_COVERAGE_ERROR);
        }
        return applyCoveragePos;
    }

    /**
     * 查询被保人险种信息列表
     *
     * @param applyId   投保单ID
     * @param insuredId 被保人ID
     * @return list
     */
    @Override
    public List<ApplyCoveragePo> getApplyCoverageList(String applyId, String insuredId) {
        List<ApplyCoveragePo> applyCoveragePos;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(APPLY_COVERAGE.APPLY_ID.eq(applyId));
            if (AssertUtils.isNotEmpty(insuredId)) {
                conditions.add(APPLY_COVERAGE.INSURED_ID.eq(insuredId));
            } else {
                conditions.add(APPLY_COVERAGE.INSURED_ID.isNotNull());
            }
            applyCoveragePos = this.getDslContext()
                    .select(APPLY_COVERAGE.fields())
                    .from(APPLY_COVERAGE)
                    .where(conditions).orderBy(APPLY_COVERAGE.PRIMARY_FLAG.desc())
                    .fetchInto(ApplyCoveragePo.class);
        } catch (Exception e) {
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_COVERAGE_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_COVERAGE_ERROR);
        }
        return applyCoveragePos;
    }

    @Override
    public int getApplyCoverageNum(String applyId) {
        int a = this.getDslContext()
                .selectCount()
                .from(APPLY_COVERAGE)
                .where(APPLY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(APPLY_COVERAGE.APPLY_ID.eq(applyId))
                .and(APPLY_COVERAGE.INSURED_ID.isNotNull())
                .and(APPLY_COVERAGE.PRIMARY_FLAG.eq("MAIN"))
                .fetchOneInto(int.class);
        return a;
    }

    /**
     * 查询投保单被保人列表
     *
     * @param applyId 投保单ID
     * @param keyword 搜索关键字
     * @return list
     */
    @Override
    public List<ApplyInsuredBo> getApplyInsuredList(String applyId, String keyword) {
        List<ApplyInsuredBo> applyInsuredBos;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(APPLY_INSURED.APPLY_ID.eq(applyId));
            conditions.add(APPLY_INSURED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
            if (AssertUtils.isNotEmpty(keyword)) {
                conditions.add(APPLY_INSURED.NAME.like("%" + keyword + "%"));
            }
            applyInsuredBos = this.getDslContext()
                    .select(APPLY_INSURED.fields())
                    .select(APPLY_INSURED_EXTEND.INSURED_STATUS)
                    .select(APPLY_INSURED_EXTEND.ADD_DATE)
                    .select(APPLY_INSURED_EXTEND.EFFECTIVE_DATE)
                    .select(APPLY_INSURED_EXTEND.INVALID_DATE)
                    .select(APPLY_INSURED_EXTEND.INSURED_EXTEND_ID)
                    .from(APPLY_INSURED)
                    .leftJoin(APPLY_INSURED_EXTEND).on(APPLY_INSURED.APPLY_ID.eq(APPLY_INSURED_EXTEND.APPLY_ID))
                    .and(APPLY_INSURED.INSURED_ID.eq(APPLY_INSURED_EXTEND.INSURED_ID))
                    .where(conditions)
                    .orderBy(APPLY_INSURED.CREATED_DATE, APPLY_INSURED.INSURED_ID)
                    .fetchInto(ApplyInsuredBo.class);
        } catch (Exception e) {
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_INSURED_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_INSURED_ERROR);
        }
        return applyInsuredBos;
    }

    /**
     * 查询被保人拓展信息列表
     *
     * @param insuredIds 被保人IDS
     * @return list
     */
    @Override
    public List<ApplyInsuredExtendPo> getApplyInsuredExtendList(List<String> insuredIds) {
        List<ApplyInsuredExtendPo> applyInsuredExtendPos;
        try {
            applyInsuredExtendPos = this.getDslContext()
                    .select(APPLY_INSURED_EXTEND.fields())
                    .from(APPLY_INSURED_EXTEND)
                    .where(APPLY_INSURED_EXTEND.INSURED_ID.in(insuredIds))
                    .fetchInto(ApplyInsuredExtendPo.class);
        } catch (Exception e) {
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_INSURED_EXTEND_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_INSURED_EXTEND_ERROR);
        }
        return applyInsuredExtendPos;
    }

    /**
     * 查询投保单代理人信息
     *
     * @param applyId 投保单ID
     * @return ApplyAgentPo
     */
    @Override
    public ApplyAgentPo getApplyAgent(String applyId) {
        ApplyAgentPo applyAgentPo;
        try {
            applyAgentPo = this.getDslContext()
                    .select(APPLY_AGENT.fields())
                    .from(APPLY_AGENT)
                    .where(APPLY_AGENT.APPLY_ID.eq(applyId))
                    .fetchOneInto(ApplyAgentPo.class);
        } catch (Exception e) {
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_AGENT_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_AGENT_ERROR);
        }
        return applyAgentPo;
    }

    /**
     * 根据投保单ID查询被保人统计信息
     *
     * @param applyId 投保单ID
     * @return ApplyInsuredCollectPo
     */
    @Override
    public ApplyInsuredCollectPo getApplyInsuredCollect(String applyId) {
        ApplyInsuredCollectPo applyInsuredCollectPo;
        try {
            applyInsuredCollectPo = this.getDslContext()
                    .select(APPLY_INSURED_COLLECT.fields())
                    .from(APPLY_INSURED_COLLECT)
                    .where(APPLY_INSURED_COLLECT.APPLY_ID.eq(applyId))
                    .and(APPLY_INSURED_COLLECT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .fetchOneInto(ApplyInsuredCollectPo.class);
        } catch (Exception e) {
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_INSURED_COLLECT_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_INSURED_COLLECT_ERROR);
        }
        return applyInsuredCollectPo;
    }

    /**
     * 根据投保单ID查询投保单联系信息
     *
     * @param applyId 投保单ID
     * @return
     */
    @Override
    public ApplyContactInfoPo getApplyContactInfo(String applyId) {
        ApplyContactInfoPo applyContactInfoPo;
        try {
            applyContactInfoPo = this.getDslContext()
                    .select(APPLY_CONTACT_INFO.fields())
                    .from(APPLY_CONTACT_INFO)
                    .where(APPLY_CONTACT_INFO.APPLY_ID.eq(applyId))
                    .and(APPLY_CONTACT_INFO.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .fetchOneInto(ApplyContactInfoPo.class);
        } catch (Exception e) {
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_CONTACT_INFO_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_CONTACT_INFO_ERROR);
        }
        return applyContactInfoPo;
    }

    /**
     * 根据投保单ID查询投保单问题件信息
     *
     * @param applyId      投保单ID
     * @param optionStatus 问题件操作状态(INIT"初始化",COMPLETE"已经提交") 为空查所有
     * @return List<ApplyQuestionFlowPo>
     */
    @Override
    public List<ApplyQuestionFlowPo> getApplyQuestionFlowBos(String applyId, String optionStatus) {
        List<ApplyQuestionFlowPo> applyQuestionFlowPos = new ArrayList<>();
        try {
            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(APPLY_QUESTION_FLOW.fields()).from(APPLY_QUESTION_FLOW);
            List<Condition> conditions = new ArrayList<>();
            conditions.add(APPLY_QUESTION_FLOW.APPLY_ID.eq(applyId));
            if (AssertUtils.isNotEmpty(optionStatus)) {
                conditions.add(APPLY_QUESTION_FLOW.OPTION_STATUS.eq(optionStatus));
            }
            conditions.add(APPLY_QUESTION_FLOW.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
            selectJoinStep.where(conditions).orderBy(APPLY_QUESTION_FLOW.CREATED_DATE.asc(), APPLY_QUESTION_FLOW.FLOW_INDEX.asc());
            System.out.println(selectJoinStep.toString());
            applyQuestionFlowPos = selectJoinStep.fetchInto(ApplyQuestionFlowPo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_QUESTION_FLOW_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_QUESTION_FLOW_ERROR);
        }
        return applyQuestionFlowPos;
    }

    /**
     * 根据投保单ID查询投保单特别约定信息
     *
     * @param applyId 投保单ID
     * @return List<ApplySpecialContractPo>
     */
    @Override
    public List<ApplySpecialContractPo> getApplySpecialContract(String applyId) {
        List<ApplySpecialContractPo> applySpecialContractPos = new ArrayList<>();
        try {
            applySpecialContractPos = this.getDslContext()
                    .select(APPLY_SPECIAL_CONTRACT.fields()).from(APPLY_SPECIAL_CONTRACT).where(APPLY_SPECIAL_CONTRACT.APPLY_ID.eq(applyId))
                    .and(APPLY_SPECIAL_CONTRACT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .orderBy(APPLY_SPECIAL_CONTRACT.CREATED_DATE.asc())
                    .fetchInto(ApplySpecialContractPo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_SPECIAL_CONTRACT_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_SPECIAL_CONTRACT_ERROR);
        }
        return applySpecialContractPos;
    }

    /**
     * 个险根据投保单ID查询投保单特别约定信息
     *
     * @param applyId 投保单ID
     * @return ApplySpecialContractPo
     */
    @Override
    public ApplySpecialContractPo queryApplySpecialContract(String applyId) {
        ApplySpecialContractPo applySpecialContractPo = new ApplySpecialContractPo();
        try {
            applySpecialContractPo = this.getDslContext()
                    .select(APPLY_SPECIAL_CONTRACT.fields()).from(APPLY_SPECIAL_CONTRACT).where(APPLY_SPECIAL_CONTRACT.APPLY_ID.eq(applyId))
                    .and(APPLY_SPECIAL_CONTRACT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(APPLY_SPECIAL_CONTRACT.SPECIAL_CONTRACT_TYPE_CODE.eq(ApplyTermEnum.SPECIAL_CONTRACT_TYPE.OTHER.name()))
                    .orderBy(APPLY_SPECIAL_CONTRACT.CREATED_DATE.asc())
                    .fetchOneInto(ApplySpecialContractPo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_SPECIAL_CONTRACT_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_SPECIAL_CONTRACT_ERROR);
        }
        return applySpecialContractPo;
    }

    @Override
    public List<ApplyRemarkBo> getApplyRemark(String applyId) {
        List<ApplyRemarkBo> applyRemarkBos = null;
        try {
            applyRemarkBos = this.getDslContext()
                    .select(APPLY_REMARK.fields())
                    .select(APPLY_REMARK_HEAD.APPLY_ID, APPLY_REMARK_HEAD.APPLY_NO)
                    .from(APPLY_REMARK)
                    .innerJoin(APPLY_REMARK_HEAD).on(APPLY_REMARK_HEAD.APPLY_REMARK_HEAD_ID.eq(APPLY_REMARK.APPLY_REMARK_HEAD_ID))
                    .where(APPLY_REMARK_HEAD.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(APPLY_REMARK_HEAD.APPLY_ID.eq(applyId))
                    .fetchInto(ApplyRemarkBo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_REMARK_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_REMARK_ERROR);
        }
        return applyRemarkBos;
    }

    @Override
    public ApplyInsuredBatchPo getApplyInsuredBatch(String applyId) {
        ApplyInsuredBatchPo applyInsuredBatchPo = null;
        try {
            applyInsuredBatchPo = this.getDslContext()
                    .select(APPLY_INSURED_BATCH.fields())
                    .from(APPLY_INSURED_BATCH)
                    .where(APPLY_INSURED_BATCH.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(APPLY_INSURED_BATCH.APPLY_ID.eq(applyId))
                    .orderBy(APPLY_INSURED_BATCH.CREATED_DATE.desc())
                    .limit(1)
                    .fetchOneInto(ApplyInsuredBatchPo.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return applyInsuredBatchPo;
    }

    @Override
    public List<ApplyInsuredUploadBo> getApplyInsuredUpload(String insuredBatchId) {
        List<ApplyInsuredUploadBo> applyInsuredUploadBos = null;
        try {
            applyInsuredUploadBos = this.getDslContext()
                    .select(APPLY_INSURED_BATCH.ATTACHMENT_ID)
                    .select(APPLY_INSURED_UPLOAD.fields())
                    .from(APPLY_INSURED_UPLOAD)
                    .innerJoin(APPLY_INSURED_BATCH).on(APPLY_INSURED_BATCH.INSURED_BATCH_ID.eq(APPLY_INSURED_UPLOAD.INSURED_BATCH_ID))
                    .where(APPLY_INSURED_UPLOAD.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(APPLY_INSURED_UPLOAD.INSURED_BATCH_ID.eq(insuredBatchId))
                    .fetchInto(ApplyInsuredUploadBo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_INSURED_UPLOAD_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_INSURED_UPLOAD_ERROR);
        }
        return applyInsuredUploadBos;
    }

    @Override
    public ApplyPremiumBo getApplyPremium(String applyId) {
        ApplyPremiumBo applyPremiumBo = null;
        try {
            List<ApplyPremiumBo> applyPremiumBos = new ArrayList<>();
            Map<String, List<ApplyPaymentAttachmentPo>> listPaymentAttachmentMap = new HashMap<>();

            this.getDslContext()
                    .select(APPLY_PREMIUM.fields())
                    .select(APPLY_PAYMENT_ATTACHMENT.fields())
                    .from(APPLY_PREMIUM)
                    //支付附件
                    .leftJoin(APPLY_PAYMENT_ATTACHMENT).on(APPLY_PREMIUM.APPLY_ID.eq(APPLY_PAYMENT_ATTACHMENT.APPLY_ID).and(APPLY_PREMIUM.PREMIUM_ID.eq(APPLY_PAYMENT_ATTACHMENT.PREMIUM_ID)).and(APPLY_PAYMENT_ATTACHMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                    .where(APPLY_PREMIUM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(APPLY_PREMIUM.APPLY_ID.eq(applyId))
                    .fetch().map(record -> {

                ApplyPremiumBo applyPremiumBoExt = BasePojo.getInstance(ApplyPremiumBo.class, record.into(ApplyPremiumRecord.class));
                ApplyPaymentAttachmentPo applyPaymentAttachmentPo = BasePojo.getInstance(ApplyPaymentAttachmentPo.class, record.into(ApplyPaymentAttachmentRecord.class));

                //支付附件
                if (AssertUtils.isNotNull(applyPaymentAttachmentPo)) {
                    Optional.ofNullable(applyPaymentAttachmentPo.getPaymentAttachmentId()).ifPresent(r -> {
                        List<ApplyPaymentAttachmentPo> listApplyPaymentAttachmentPo = listPaymentAttachmentMap.get(applyPremiumBoExt.getPremiumId());
                        listApplyPaymentAttachmentPo = Optional.ofNullable(listApplyPaymentAttachmentPo).orElseGet(() -> {
                            return new ArrayList<ApplyPaymentAttachmentPo>();
                        });
                        if (!listApplyPaymentAttachmentPo.contains(applyPaymentAttachmentPo)) {
                            listApplyPaymentAttachmentPo.add(applyPaymentAttachmentPo);
                        }
                        listPaymentAttachmentMap.put(applyPremiumBoExt.getPremiumId(), listApplyPaymentAttachmentPo);
                        applyPremiumBoExt.setListApplyPaymentAttachment(listApplyPaymentAttachmentPo);
                    });
                }

                if (!applyPremiumBos.contains(applyPremiumBoExt)) {
                    applyPremiumBos.add(applyPremiumBoExt);
                }

                return null;
            });

            if (AssertUtils.isNotEmpty(applyPremiumBos)) {
                applyPremiumBo = applyPremiumBos.get(0);
            }

        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_PREMIUM_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_PREMIUM_ERROR);
        }
        return applyPremiumBo;
    }

    @Override
    public List<ApplyAccountBo> getApplyAccountList(String applyId) {
        List<ApplyAccountBo> applyAccountBos;
        try {
            applyAccountBos = this.getDslContext()
                    .select(APPLY_ACCOUNT.fields())
                    .from(APPLY_ACCOUNT)
                    .where(APPLY_ACCOUNT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(APPLY_ACCOUNT.APPLY_ID.eq(applyId))
                    .fetchInto(ApplyAccountBo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_ACCOUNT_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_ACCOUNT_ERROR);
        }
        return applyAccountBos;
    }

    /**
     * 根据投保单ID查询账户信息
     *
     * @param applyId 投保单ID
     * @return
     */
    @Override
    public ApplyAccountPo queryApplyAccount(String applyId) {
        return this.getDslContext()
                .selectFrom(APPLY_ACCOUNT)
                .where(APPLY_ACCOUNT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(APPLY_ACCOUNT.APPLY_ID.eq(applyId))
                .limit(1)
                .fetchOneInto(ApplyAccountPo.class);
    }

    @Override
    public List<ApplyAddPremiumPo> getApplyAddPremium(String applyId) {
        List<ApplyAddPremiumPo> applyAddPremiumPos;
        try {
            applyAddPremiumPos = this.getDslContext()
                    .select(APPLY_ADD_PREMIUM.fields())
                    .from(APPLY_ADD_PREMIUM)
                    .where(APPLY_ADD_PREMIUM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(APPLY_ADD_PREMIUM.APPLY_ID.eq(applyId))
                    .fetchInto(ApplyAddPremiumPo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_ADD_PREMIUM_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_ADD_PREMIUM_ERROR);
        }
        return applyAddPremiumPos;
    }

    @Override
    public List<ApplyAddPremiumPo> getApplyAddPremium(String applyId, String insuredId, String coverageId) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(APPLY_ADD_PREMIUM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        conditions.add(APPLY_ADD_PREMIUM.APPLY_ID.eq(applyId));

        if (AssertUtils.isNotEmpty(insuredId)) {
            conditions.add(APPLY_ADD_PREMIUM.INSURED_ID.eq(insuredId));
        } else {
            conditions.add(APPLY_ADD_PREMIUM.INSURED_ID.isNull());
        }

        if (AssertUtils.isNotEmpty(coverageId)) {
            conditions.add(APPLY_ADD_PREMIUM.COVERAGE_ID.eq(coverageId));
        } else {
            conditions.add(APPLY_ADD_PREMIUM.COVERAGE_ID.isNull());
        }
        return this.getDslContext()
                .select(APPLY_ADD_PREMIUM.fields())
                .from(APPLY_ADD_PREMIUM)
                .where(conditions)
                .fetchInto(ApplyAddPremiumPo.class);
    }

    @Override
    public ApplyUnderwriteTaskPo getApplyUnderwriteTask(String applyId) {
        ApplyUnderwriteTaskPo applyUnderwriteTaskPo = new ApplyUnderwriteTaskPo();
        try {
            applyUnderwriteTaskPo = this.getDslContext()
                    .selectFrom(APPLY_UNDERWRITE_TASK)
                    .where(APPLY_UNDERWRITE_TASK.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(APPLY_UNDERWRITE_TASK.CONTROL_NO.eq(applyId))
                    .orderBy(APPLY_UNDERWRITE_TASK.CREATED_DATE.desc())
                    .limit(1)
                    .fetchOneInto(ApplyUnderwriteTaskPo.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return applyUnderwriteTaskPo;
    }

    @Override
    public ApplyUnderwriteDecisionPo getApplyUnderwriteDecision(String underwriteTaskId, String underwriteDecisionId) {
        ApplyUnderwriteDecisionPo applyUnderwriteDecisionPo = new ApplyUnderwriteDecisionPo();
        try {
            applyUnderwriteDecisionPo = this.getDslContext()
                    .selectFrom(APPLY_UNDERWRITE_DECISION)
                    .where(APPLY_UNDERWRITE_DECISION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(APPLY_UNDERWRITE_DECISION.UNDERWRITE_TASK_ID.eq(underwriteTaskId))
                    .and(APPLY_UNDERWRITE_DECISION.UNDERWRITE_DECISION_ID.eq(underwriteDecisionId))
                    .and(APPLY_UNDERWRITE_DECISION.UNDERWRITE_CUSTOMER_TASK_ID.isNull())
                    .orderBy(APPLY_UNDERWRITE_DECISION.CREATED_DATE.desc())
                    .limit(1)
                    .fetchOneInto(ApplyUnderwriteDecisionPo.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return applyUnderwriteDecisionPo;
    }

    @Override
    public ApplyUnderwriteDecisionPo getApplyUnderwriteDecision(String applyId) {
        ApplyUnderwriteDecisionPo applyUnderwriteDecisionPo = new ApplyUnderwriteDecisionPo();
        try {
            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(APPLY_UNDERWRITE_TASK.CONTROL_NO)
                    .select(APPLY_UNDERWRITE_DECISION.fields())
                    .from(APPLY_UNDERWRITE_TASK)
                    .leftJoin(APPLY_UNDERWRITE_DECISION).on(APPLY_UNDERWRITE_TASK.UNDERWRITE_TASK_ID.eq(APPLY_UNDERWRITE_DECISION.UNDERWRITE_TASK_ID));
            List<Condition> conditions = new ArrayList<>();
            conditions.add(APPLY_UNDERWRITE_TASK.CONTROL_NO.eq(applyId));
            conditions.add(APPLY_UNDERWRITE_DECISION.UNDERWRITE_TASK_ID.eq(APPLY_UNDERWRITE_TASK.UNDERWRITE_TASK_ID).and(APPLY_UNDERWRITE_DECISION.UNDERWRITE_CUSTOMER_TASK_ID.isNull()));
            selectJoinStep.where(conditions);
            applyUnderwriteDecisionPo = (ApplyUnderwriteDecisionPo) selectJoinStep.fetchOneInto(ApplyUnderwriteDecisionPo.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return applyUnderwriteDecisionPo;
    }

    @Override
    public BaseUnderwriteDecisionPo getBaseUnderwriteDecisionPo(String underwriteDecisionCode) {
        BaseUnderwriteDecisionPo baseUnderwriteDecisionPo = new BaseUnderwriteDecisionPo();
        try {
            baseUnderwriteDecisionPo = this.getDslContext()
                    .selectFrom(BASE_UNDERWRITE_DECISION)
                    .where(BASE_UNDERWRITE_DECISION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(BASE_UNDERWRITE_DECISION.UNDERWRITE_DECISION_CODE.eq(underwriteDecisionCode))
                    .limit(1)
                    .fetchOneInto(BaseUnderwriteDecisionPo.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return baseUnderwriteDecisionPo;
    }

    @Override
    public List<BaseUnderwriteDecisionPo> getBaseUnderwriteDecisionPo() {
        List<BaseUnderwriteDecisionPo> baseUnderwriteDecisionPos = new ArrayList<>();
        try {
            baseUnderwriteDecisionPos = this.getDslContext()
                    .selectFrom(BASE_UNDERWRITE_DECISION)
                    .where(BASE_UNDERWRITE_DECISION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .fetchInto(BaseUnderwriteDecisionPo.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return baseUnderwriteDecisionPos;
    }

    /**
     * 查询投保单支付事务
     *
     * @param applyId       投保单ID
     * @param paymentStatus 支付状态
     * @param paymentType
     * @param feeType
     * @return ApplyPaymentTransactionBo
     */
    @Override
    public ApplyPaymentTransactionBo getApplyPaymentTransaction(String applyId, String paymentStatus, String paymentType, String feeType) {
        ApplyPaymentTransactionBo applyPaymentTransactionBo;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(APPLY_PAYMENT_TRANSACTION.APPLY_ID.eq(applyId));
            conditions.add(APPLY_PAYMENT_TRANSACTION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
            if (AssertUtils.isNotEmpty(paymentStatus)) {
                conditions.add(APPLY_PAYMENT_TRANSACTION.PAYMENT_STATUS.eq(paymentStatus));
            } else {
                conditions.add(APPLY_PAYMENT_TRANSACTION.PAYMENT_STATUS.ne(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name()));
            }
            if (AssertUtils.isNotEmpty(paymentType)) {
                conditions.add(APPLY_PAYMENT_TRANSACTION.PAYMENT_TYPE.eq(paymentType));
            }
            if (AssertUtils.isNotEmpty(feeType)) {
                conditions.add(APPLY_PAYMENT_TRANSACTION.FEE_TYPE.eq(feeType));
            }
            applyPaymentTransactionBo = this.getDslContext()
                    .select(APPLY_PAYMENT_TRANSACTION.fields())
                    .from(APPLY_PAYMENT_TRANSACTION)
                    .where(conditions)
                    .orderBy(APPLY_PAYMENT_TRANSACTION.PAYMENT_DATE.desc())
                    .limit(1)
                    .fetchOneInto(ApplyPaymentTransactionBo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_PAYMENT_TRANSACTION_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_PAYMENT_TRANSACTION_ERROR);
        }
        return applyPaymentTransactionBo;
    }

    /**
     * 查询投保单支付事务项目
     *
     * @param paymentTransactionId 支付事务ID
     * @param paymentStatus        支付状态
     * @return list
     */
    @Override
    public List<ApplyPaymentTransactionItemPo> getApplyPaymentTransactionItemList(String paymentTransactionId, String paymentStatus) {
        List<ApplyPaymentTransactionItemPo> applyPaymentTransactionItemPos;
        try {
            List<Condition> conditions = new ArrayList<>();
            conditions.add(APPLY_PAYMENT_TRANSACTION_ITEM.PAYMENT_TRANSACTION_ID.eq(paymentTransactionId));
            if (AssertUtils.isNotEmpty(paymentStatus)) {
                conditions.add(APPLY_PAYMENT_TRANSACTION_ITEM.PAYMENT_STATUS.eq(paymentStatus));
            }
            applyPaymentTransactionItemPos = this.getDslContext()
                    .select(APPLY_PAYMENT_TRANSACTION_ITEM.fields())
                    .from(APPLY_PAYMENT_TRANSACTION_ITEM)
                    .where(conditions)
                    .fetchInto(ApplyPaymentTransactionItemPo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_PAYMENT_TRANSACTION_ITEM_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_PAYMENT_TRANSACTION_ITEM_ERROR);
        }
        return applyPaymentTransactionItemPos;
    }

    @Override
    public List<ApplyCoverageAcceptPo> getApplyCoverageAccepts(String applyId) {
        List<ApplyCoverageAcceptPo> applyCoverageAcceptPos;
        try {
            applyCoverageAcceptPos = this.getDslContext()
                    .selectFrom(APPLY_COVERAGE_ACCEPT)
                    .where(APPLY_COVERAGE_ACCEPT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(APPLY_COVERAGE_ACCEPT.APPLY_ID.eq(applyId))
                    .fetchInto(ApplyCoverageAcceptPo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_COVERAGE_ACCEPT_ERROR.getValue());
            throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_COVERAGE_ACCEPT_ERROR);
        }
        return applyCoverageAcceptPos;
    }

    @Override
    public ApplyAbandonedPo queryApplyAbandoned(String applyId) {
        return this.getDslContext().selectFrom(APPLY_ABANDONED).where(APPLY_ABANDONED.APPLY_ID.eq(applyId).and(APPLY_ABANDONED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))).fetchOneInto(ApplyAbandonedPo.class);
    }

    @Override
    public ApplyInsuredPo querySokSanCustomer(String applyId, String name, String sex, Long birthday, String idNo) {
        Condition a1 = APPLY_INSURED.BIRTHDAY.eq(birthday).and(APPLY_INSURED.SEX.ne(sex));
        Condition a2 = APPLY_INSURED.BIRTHDAY.ne(birthday).and(APPLY_INSURED.SEX.eq(sex));
        Condition a3 = APPLY_INSURED.BIRTHDAY.ne(birthday).and(APPLY_INSURED.SEX.ne(sex));
        Condition a4 = APPLY_INSURED.BIRTHDAY.eq(birthday).and(APPLY_INSURED.SEX.eq(sex));
        Field<String> tableField = DSL.regexpReplaceAll(APPLY_INSURED.NAME," ", "");
        String nameTrim = name.replaceAll(" ", "");
        Condition a = tableField.equalIgnoreCase(nameTrim).and(APPLY_INSURED.ID_NO.eq(idNo))
                .and(a1.or(a2).or(a3));
        Condition b = tableField.notEqualIgnoreCase(nameTrim).and(APPLY_INSURED.ID_NO.eq(idNo))
                .and(a1.or(a2).or(a3).or(a4));
        Condition c = tableField.equalIgnoreCase(nameTrim).and(APPLY_INSURED.ID_NO.ne(idNo))
                .and(a4);
        Condition d = tableField.notEqualIgnoreCase(nameTrim).and(APPLY_INSURED.ID_NO.eq(idNo))
                .and(a4);
        Condition e = tableField.equalIgnoreCase(nameTrim).and(APPLY_INSURED.ID_NO.eq(idNo))
                .and(a4);

        SelectOnConditionStep<Record> form = this.getDslContext()
                .select(APPLY_INSURED.fields())
                .from(APPLY_INSURED)
                .innerJoin(APPLY).on(APPLY_INSURED.APPLY_ID.eq(APPLY.APPLY_ID),
                        APPLY.APPLY_ID.ne(applyId),
                        APPLY.APPLY_STATUS.in(Arrays.stream(ApplyTermEnum.APPLY_STATUS_PROCESS.values()).map(ApplyTermEnum.APPLY_STATUS_PROCESS::name).collect(Collectors.toList()))
                )
                .leftJoin(APPLY_COVERAGE).on(APPLY_INSURED.APPLY_ID.eq(APPLY_COVERAGE.APPLY_ID));

        form.where(a.or(b).or(c).or(d).or(e))
                .and(APPLY_COVERAGE.PRIMARY_FLAG.eq(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())
                        .and(APPLY_COVERAGE.PRODUCT_ID.eq(ProductTermEnum.PRODUCT.PRODUCT_34.id())))
                .limit(1);
        System.out.println(form);
        return form.fetchOneInto(ApplyInsuredPo.class);
    }

    @Override
    public ApplyHealthQuestionnaireRemarkPo getApplyHealthQuestionnaireRemark(String applyId, String customerType) {
        return this.getDslContext().selectFrom(APPLY_HEALTH_QUESTIONNAIRE_REMARK)
                .where(APPLY_HEALTH_QUESTIONNAIRE_REMARK.APPLY_ID.eq(applyId)
                        .and(APPLY_HEALTH_QUESTIONNAIRE_REMARK.CUSTOMER_TYPE.eq(customerType))
                        .and(APPLY_HEALTH_QUESTIONNAIRE_REMARK.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))).fetchOneInto(ApplyHealthQuestionnaireRemarkPo.class);
    }

    @Override
    public List<ApplyHealthQuestionnaireRemarkPo> listApplyHealthQuestionnaireRemark(String applyId) {
        return this.getDslContext().selectFrom(APPLY_HEALTH_QUESTIONNAIRE_REMARK)
                .where(APPLY_HEALTH_QUESTIONNAIRE_REMARK.APPLY_ID.eq(applyId)
                        .and(APPLY_HEALTH_QUESTIONNAIRE_REMARK.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .orderBy(APPLY_HEALTH_QUESTIONNAIRE_REMARK.CREATED_DATE, APPLY_HEALTH_QUESTIONNAIRE_REMARK.HEALTH_QUESTIONNAIRE_REMARK_ID)
                .fetchInto(ApplyHealthQuestionnaireRemarkPo.class);
    }

    @Override
    public List<ApplyReportBo> queryApplyReport(List<String> businessId) {
        Field<Long> stringField = DSL.field("row_number() OVER (PARTITION BY {0} ORDER BY {1} DESC)", SQLDataType.BIGINT,
                APPLY_INSURED.APPLY_ID, APPLY_INSURED.CREATED_DATE);
        Table recordTable = this.getDslContext()
                .select(APPLY.fields())
                .select(APPLY_PREMIUM.SPECIAL_DISCOUNT, APPLY_PREMIUM.PROMOTION_TYPE, APPLY_PREMIUM.DISCOUNT_TYPE, APPLY_PREMIUM.DISCOUNT_MODEL)
                .select(APPLY_COVERAGE.PRODUCT_NAME, APPLY_COVERAGE.PRODUCT_CODE, APPLY_COVERAGE.PRODUCT_LEVEL, APPLY_COVERAGE.PREMIUM_PERIOD, APPLY_COVERAGE.ORIGINAL_PREMIUM)
                .select(APPLY_APPLICANT.NAME.as("applicantName"))
                .select(APPLY_INSURED.NAME.as("insuredName"), APPLY_INSURED.BIRTHDAY.as("insuredBirthday"))
                .select(stringField.as("rowNum"))
                .from(APPLY)
                .join(APPLY_COVERAGE).on(APPLY.APPLY_ID.eq(APPLY_COVERAGE.APPLY_ID))
                .join(APPLY_APPLICANT).on(APPLY.APPLY_ID.eq(APPLY_APPLICANT.APPLY_ID))
                .join(APPLY_INSURED).on(APPLY.APPLY_ID.eq(APPLY_INSURED.APPLY_ID))
                .join(APPLY_PREMIUM).on(APPLY.APPLY_ID.eq(APPLY_PREMIUM.APPLY_ID))
                .where(APPLY.APPLY_ID.in(businessId).and(APPLY_COVERAGE.PRIMARY_FLAG.eq(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))).asTable();

        SelectConditionStep rowNum = this.getDslContext().select(recordTable.fields()).from(recordTable).where(recordTable.field("rowNum").eq(1));
        return rowNum.fetchInto(ApplyReportBo.class);
    }

    /**
     * 查询保单受益人
     *
     * @param applyId 保单ID
     * @return List<PolicyInsuredBo>
     */
    @Override
    public List<ApplyBeneficiaryInfoBo> queryApplyBeneficiaryListByApplyId(String applyId) {
        List<ApplyBeneficiaryInfoBo> applyBeneficiaryInfoBos = new ArrayList<>();
        applyBeneficiaryInfoBos = this.getDslContext()
                .select(APPLY_BENEFICIARY.fields())
                .select(APPLY_BENEFICIARY_INFO.fields())
                .from(APPLY_BENEFICIARY)
                .innerJoin(APPLY_BENEFICIARY_INFO).on(APPLY_BENEFICIARY.BENEFICIARY_ID.eq(APPLY_BENEFICIARY_INFO.BENEFICIARY_ID))
                .where(APPLY_BENEFICIARY.APPLY_ID.eq(applyId))
                .orderBy(APPLY_BENEFICIARY_INFO.BENEFICIARY_NO_ORDER.sortAsc(Arrays.stream(ApplyTermEnum.BENEFICIARY_NO.values()).map(ApplyTermEnum.BENEFICIARY_NO::name).collect(Collectors.toList())))
                .fetch().stream().map(record -> {
                    //受益人
                    ApplyBeneficiaryBo policyBeneficiaryBo = BasePojo.getInstance(ApplyBeneficiaryBo.class, record.into(ApplyBeneficiaryRecord.class));
                    ApplyBeneficiaryInfoBo policyBeneficiaryInfoBo = BasePojo.getInstance(ApplyBeneficiaryInfoBo.class, record.into(ApplyBeneficiaryInfoRecord.class));
                    policyBeneficiaryInfoBo.setApplyBeneficiaryBo(policyBeneficiaryBo);
                    return policyBeneficiaryInfoBo;
                }).collect(Collectors.toList());
        if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBos)) {
            applyBeneficiaryInfoBos.forEach(applyBeneficiaryInfoBo -> {
                List<ApplyBeneficiaryAttachmentBo> applyBeneficiaryAttachmentBos = this.queryApplyBeneficiaryAttachment(applyBeneficiaryInfoBo.getBeneficiaryId());
                applyBeneficiaryInfoBo.setListBeneficiaryAttachment(applyBeneficiaryAttachmentBos);
            });
        }
        return applyBeneficiaryInfoBos;
    }

    /**
     * 查询受益人下的附件
     *
     * @param beneficiaryId 投保单ID
     * @return ApplyBeneficiaryAttachmentBos
     */
    @Override
    public List<ApplyBeneficiaryAttachmentBo> queryApplyBeneficiaryAttachment(String beneficiaryId) {
        return this.getDslContext().select(APPLY_BENEFICIARY_ATTACHMENT.APPLY_BENEFICIARY_ATTACHMENT_ID, APPLY_BENEFICIARY_ATTACHMENT.CUSTOMER_ID)
                .select(APPLY_ATTACHMENT.fields())
                .from(APPLY_BENEFICIARY_ATTACHMENT)
                .leftJoin(APPLY_ATTACHMENT).on(APPLY_ATTACHMENT.ATTACHMENT_ID.eq(APPLY_BENEFICIARY_ATTACHMENT.ATTACHMENT_ID))
                .leftJoin(APPLY_BENEFICIARY).on(APPLY_BENEFICIARY.CUSTOMER_ID.eq(APPLY_BENEFICIARY_ATTACHMENT.CUSTOMER_ID))
                .where(APPLY_BENEFICIARY.BENEFICIARY_ID.eq(beneficiaryId))
                .orderBy(APPLY_ATTACHMENT.ATTACHMENT_TYPE_CODE, APPLY_ATTACHMENT.ATTACHMENT_SEQ)
                .fetchInto(ApplyBeneficiaryAttachmentBo.class);
    }

    @Override
    public ApplyPlanPo queryApplyPlan(String applyId) {
        return this.getDslContext().selectFrom(APPLY_PLAN).where(APPLY_PLAN.APPLY_ID.eq(applyId)).limit(1)
                .fetchOneInto(ApplyPlanPo.class);
    }

    /**
     * 查询计划书追踪信息
     *
     * @param applyPlanId 计划书id
     * @return ApplyPlanTracePos
     */
    @Override
    public List<ApplyPlanTracePo> queryApplyPlanTracePo(String applyPlanId) {
        return this.getDslContext().selectFrom(APPLY_PLAN_TRACE).where(APPLY_PLAN_TRACE.APPLY_PLAN_ID.eq(applyPlanId))
                .orderBy(APPLY_PLAN_TRACE.CREATED_DATE.desc().nullsLast())
                .fetchInto(ApplyPlanTracePo.class);
    }

    /**
     * 查询计划书追踪信息
     *
     * @param applyPlanId     计划书id
     * @param basePageRequest
     * @return ApplyPlanTracePos
     */
    @Override
    public List<ApplyPlanTraceBo> queryApplyPlanTraceByPage(String applyPlanId, BasePageRequest basePageRequest) {
        return this.getDslContext().select(APPLY_PLAN_TRACE.fields())
                .select(APPLY_PLAN_TRACE.PLAN_TRACE_ID.countOver().as("totalLine"))
                .from(APPLY_PLAN_TRACE)
                .where(APPLY_PLAN_TRACE.APPLY_PLAN_ID.eq(applyPlanId))
                .orderBy(APPLY_PLAN_TRACE.CREATED_DATE.desc().nullsLast())
                .offset(basePageRequest.getOffset())
                .limit(basePageRequest.getPageSize())
                .fetchInto(ApplyPlanTraceBo.class);
    }

    /**
     * 被保人下的团险健康告知
     *
     * @param applyId
     * @param insuredId
     * @return
     */
    @Override
    public List<ApplyGroupHealthQuestionnaireAnswerPo> queryApplyGroupHealthQuestionnaireAnswer(String applyId, String insuredId) {
        SelectJoinStep<Record> selectJoinStep = this.getDslContext().select(APPLY_GROUP_HEALTH_QUESTIONNAIRE_ANSWER.fields())
                .from(APPLY_GROUP_HEALTH_QUESTIONNAIRE_ANSWER);
        List<Condition> conditions = new ArrayList<>();
        if (AssertUtils.isNotEmpty(insuredId)) {
            conditions.add(APPLY_GROUP_HEALTH_QUESTIONNAIRE_ANSWER.INSURED_ID.eq(insuredId));
        }
        conditions.add(APPLY_GROUP_HEALTH_QUESTIONNAIRE_ANSWER.APPLY_ID.eq(applyId));
        return selectJoinStep
                .where(conditions)
                .orderBy(APPLY_GROUP_HEALTH_QUESTIONNAIRE_ANSWER.CREATED_DATE.desc().nullsLast())
                .fetchInto(ApplyGroupHealthQuestionnaireAnswerPo.class);
    }

    /**
     * 被保人下的团险健康告知
     *
     * @param insuredIds 被保人ID集
     * @return
     */
    @Override
    public List<ApplyGroupHealthQuestionnaireAnswerPo> listApplyGroupHealthQuestionnaireAnswer(List<String> insuredIds) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(APPLY_GROUP_HEALTH_QUESTIONNAIRE_ANSWER.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        if (AssertUtils.isNotEmpty(insuredIds)) {
            conditions.add(APPLY_GROUP_HEALTH_QUESTIONNAIRE_ANSWER.INSURED_ID.in(insuredIds));
        }
        return this.getDslContext()
                .select(APPLY_GROUP_HEALTH_QUESTIONNAIRE_ANSWER.fields())
                .from(APPLY_GROUP_HEALTH_QUESTIONNAIRE_ANSWER)
                .where(conditions)
                .fetchInto(ApplyGroupHealthQuestionnaireAnswerPo.class);
    }

    /**
     * 查询投保单ID对应的险种责任
     *
     * @param applyId 投保单ID
     * @return list
     */
    @Override
    public List<ApplyCoverageDutyBo> getApplyCoverageDutyList(String applyId) {
        return this.getDslContext()
                .select(APPLY_COVERAGE_DUTY.fields())
                .from(APPLY_COVERAGE_DUTY)
                .where(APPLY_COVERAGE_DUTY.APPLY_ID.eq(applyId))
                .and(APPLY_COVERAGE_DUTY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())).fetchInto(ApplyCoverageDutyBo.class);
    }

    /**
     * 根据投保单ID查询险种责任
     *
     * @param applyId 投保单ID
     * @return list
     */
    @Override
    public List<ApplyCoverageDutyPo> listApplyCoverageDuty(String applyId) {
        return this.getDslContext()
                .select(APPLY_COVERAGE_DUTY.fields())
                .from(APPLY_COVERAGE_DUTY)
                .where(APPLY_COVERAGE_DUTY.APPLY_ID.eq(applyId))
                .and(APPLY_COVERAGE_DUTY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(ApplyCoverageDutyPo.class);
    }

    @Override
    public List<ApplyApplicantBo> queryApplyApplicantList(String applyType) {
        return this.getDslContext()
                .select(APPLY_APPLICANT.fields())
                .from(APPLY_APPLICANT)
                .innerJoin(APPLY).on(APPLY_APPLICANT.APPLY_ID.eq(APPLY.APPLY_ID))
                .where(APPLY.APPLY_TYPE.eq(applyType))
                .and(APPLY_APPLICANT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())).fetchInto(ApplyApplicantBo.class);
    }

    @Override
    public List<ApplyInsuredBo> queryApplyInsuredList(String applyType) {
        List<ApplyInsuredBo> applyInsuredBos;
        applyInsuredBos = this.getDslContext()
                .select(APPLY_INSURED.fields())
                .from(APPLY_INSURED)
                .innerJoin(APPLY).on(APPLY_INSURED.APPLY_ID.eq(APPLY.APPLY_ID))
                .where(APPLY.APPLY_TYPE.eq(applyType))
                .and(APPLY_INSURED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())).fetchInto(ApplyInsuredBo.class);
        applyInsuredBos.forEach(applyInsuredBo -> {
            List<ApplyBeneficiaryInfoBo> applyBeneficiaryInfoBos = queryApplyBeneficiaryListByApplyId(applyInsuredBo.getApplyId());
            if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBos)) {
                applyInsuredBo.setListBeneficiary(applyBeneficiaryInfoBos);
            }
        });
        return applyInsuredBos;
    }

    @Override
    public PrintApplyInsuredBo queryPrintApplyData(String applyId) {
        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(APPLY.APPLY_ID)
                .select(APPLY.APPLY_NO)
                .select(APPLY.APPLY_DATE)
                .select(APPLY_AGENT.AGENT_CODE)
                .select(APPLY_AGENT.AGENT_ID)
                .select(APPLY_INSURED_PRINT.APPLY_INSURED_PRINT_ID)
                .from(APPLY)
                .leftJoin(APPLY_AGENT).on(APPLY_AGENT.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(APPLY_INSURED_PRINT).on(APPLY_INSURED_PRINT.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_INSURED_PRINT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .where(APPLY.APPLY_ID.eq(applyId).and(APPLY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
        return selectConditionStep.fetchOneInto(PrintApplyInsuredBo.class);
    }

    @Override
    public PrintApplyInsuredBo queryPrintApplyDataNew(String applyId) {
        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(APPLY.APPLY_ID)
                .select(APPLY.APPLY_NO)
                .select(APPLY.APPLY_DATE)
                .select(APPLY_APPLICANT.COMPANY_NAME)
                .select(APPLY_AGENT.AGENT_CODE)
                .select(APPLY_AGENT.AGENT_ID)
                .select(APPLY_INSURED_PRINT.APPLY_INSURED_PRINT_ID)
                .from(APPLY)
                .innerJoin(APPLY_APPLICANT).on(APPLY_APPLICANT.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_APPLICANT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(APPLY_AGENT).on(APPLY_AGENT.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(APPLY_INSURED_PRINT).on(APPLY_INSURED_PRINT.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_INSURED_PRINT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .where(APPLY.APPLY_ID.eq(applyId).and(APPLY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
        return selectConditionStep.fetchOneInto(PrintApplyInsuredBo.class);
    }

    @Override
    public List<ApplyInsuredBo> queryApplyInsured(String applyId) {
        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(APPLY_INSURED.fields())
                .from(APPLY_INSURED)
                .leftJoin(APPLY).on(APPLY_INSURED.APPLY_ID.eq(APPLY.APPLY_ID))
                .where(APPLY.APPLY_ID.eq(applyId)
                        .and(APPLY_INSURED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(APPLY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                );
        return selectConditionStep.fetchInto(ApplyInsuredBo.class);
    }

    @Override
    public List<ApplyBeneficiaryBo> queryApplyBeneficiaryByApplyId(String applyId) {
        SelectSeekStep1<Record, Integer> selectSeekStep1 = this.getDslContext()
                .select(APPLY_BENEFICIARY.fields())
                .select(APPLY_BENEFICIARY_INFO.RELATIONSHIP)
                .select(APPLY_BENEFICIARY_INFO.RELATIONSHIP_INSTRUCTIONS)
                .select(APPLY_BENEFICIARY_INFO.INSURED_ID)
                .select(APPLY_BENEFICIARY_INFO.BENEFICIARY_PROPORTION)
                .select(APPLY_BENEFICIARY_INFO.BENEFICIARY_NO_ORDER)
                .from(APPLY)
                .leftJoin(APPLY_BENEFICIARY).on(APPLY_BENEFICIARY.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_BENEFICIARY_INFO).on(APPLY_BENEFICIARY.BENEFICIARY_ID.eq(APPLY_BENEFICIARY_INFO.BENEFICIARY_ID))
                .where(APPLY.APPLY_ID.eq(applyId)
                        .and(APPLY_BENEFICIARY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(APPLY_BENEFICIARY_INFO.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(APPLY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                ).orderBy(APPLY_BENEFICIARY_INFO.BENEFICIARY_NO_ORDER.sortAsc(Arrays.stream(ApplyTermEnum.BENEFICIARY_NO.values()).map(ApplyTermEnum.BENEFICIARY_NO::name).collect(Collectors.toList())));

        return selectSeekStep1.fetchInto(ApplyBeneficiaryBo.class);
    }

    /**
     * 需要设置的投保人代表数据
     *
     * @return
     */
    @Override
    public List<ApplyGroupReportSyncApplicantBo> querySyncApplicantCustomer() {
        return this.getDslContext().select(APPLY_AGENT.AGENT_ID)
                .select(APPLY_APPLICANT.DELEGATE_NAME,
                        APPLY_APPLICANT.DELEGATE_ID_NO,
                        APPLY_APPLICANT.DELEGATE_ID_TYPE,
                        APPLY_APPLICANT.DELEGATE_MOBILE,
                        APPLY_APPLICANT.DELEGATE_BIRTHDAY)
                .from(APPLY_APPLICANT)
                .leftJoin(APPLY_AGENT).on(APPLY_AGENT.APPLY_ID.eq(APPLY_APPLICANT.APPLY_ID).and(APPLY_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .where(APPLY_APPLICANT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())
                        .and(APPLY_APPLICANT.APPLICANT_TYPE.eq(ApplyTermEnum.APPLICANT_TYPE.GROUP.name()))
                        .and(APPLY_AGENT.AGENT_ID.isNotNull())
                        .and(APPLY_APPLICANT.DELEGATE_ID_TYPE.isNotNull())
                        .and(APPLY_APPLICANT.DELEGATE_ID_NO.isNotNull()))
                .groupBy(APPLY_AGENT.AGENT_ID,
                        APPLY_APPLICANT.DELEGATE_NAME,
                        APPLY_APPLICANT.DELEGATE_ID_NO,
                        APPLY_APPLICANT.DELEGATE_ID_TYPE,
                        APPLY_APPLICANT.DELEGATE_MOBILE,
                        APPLY_APPLICANT.DELEGATE_BIRTHDAY).fetchInto(ApplyGroupReportSyncApplicantBo.class);
    }

    /**
     * 需要设置的投保人代表Po数据
     *
     * @return
     */
    @Override
    public List<ApplyApplicantPo> queryApplicantCustomer() {
        return this.getDslContext().selectFrom(APPLY_APPLICANT)
                .where(APPLY_APPLICANT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())
                        .and(APPLY_APPLICANT.APPLICANT_TYPE.eq(ApplyTermEnum.APPLICANT_TYPE.GROUP.name()))
                        .and(APPLY_APPLICANT.DELEGATE_ID_TYPE.isNotNull())
                        .and(APPLY_APPLICANT.DELEGATE_ID_NO.isNotNull())
                        .and(APPLY_APPLICANT.DELEGATE_CUSTOMER_ID.isNull()))
                .fetchInto(ApplyApplicantPo.class);
    }

    @Override
    public List<ApplyInsuredPrintBo> queryApplyInsuredPrint(ApplyInsuredPrintVo applyInsuredPrintVo) {
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(APPLY.APPLY_ID, APPLY.APPLY_NO, APPLY.APPLY_DATE, APPLY.APPLY_STATUS)
                .select(APPLY.APPLY_ID.countOver().as("totalLine"))
                .select(APPLY_AGENT.AGENT_ID, APPLY_AGENT.AGENT_CODE)
                .select(APPLY_APPLICANT.COMPANY_NAME, APPLY_APPLICANT.DELEGATE_NAME, APPLY_APPLICANT.DELEGATE_MOBILE)
                .select(APPLY_INSURED_PRINT.APPLY_INSURED_PRINT_ID)
                .select(APPLY_COVERAGE.PRODUCT_ID)
                .select(APPLY_COVERAGE.PRODUCT_NAME.as("coverageName"))
                .from(APPLY)
                .leftJoin(APPLY_AGENT).on(APPLY.APPLY_ID.eq(APPLY_AGENT.APPLY_ID))
                .leftJoin(APPLY_APPLICANT).on(APPLY.APPLY_ID.eq(APPLY_APPLICANT.APPLY_ID))
                .leftJoin(APPLY_COVERAGE).on(APPLY.APPLY_ID.eq(APPLY_COVERAGE.APPLY_ID)
                        .and(APPLY_COVERAGE.INSURED_ID.isNull().and(APPLY_COVERAGE.PRIMARY_FLAG.eq("MAIN"))))
                .leftJoin(APPLY_INSURED_PRINT).on(APPLY.APPLY_ID.eq(APPLY_INSURED_PRINT.APPLY_ID));
        List<Condition> conditions = new ArrayList<>();
        conditions.add(APPLY.VALID_FLAG.equal(effective.name()));
        conditions.add(APPLY_INSURED_PRINT.PRINT_END_FLAG.equal("NO"));
        if (AssertUtils.isNotEmpty(applyInsuredPrintVo.getKeyword())) {
            conditions.add(APPLY.APPLY_NO.like("%" + applyInsuredPrintVo.getKeyword() + "%")
                    .or(APPLY_APPLICANT.COMPANY_NAME.like("%" + applyInsuredPrintVo.getKeyword() + "%"))
                    .or(APPLY_AGENT.AGENT_CODE.like("%" + applyInsuredPrintVo.getKeyword() + "%")));
        }
        if (AssertUtils.isNotEmpty(applyInsuredPrintVo.getAgentIds())) {
            conditions.add(APPLY_AGENT.AGENT_ID.in(applyInsuredPrintVo.getAgentIds()));
        }
        selectJoinStep.where(conditions);
        selectJoinStep.orderBy(APPLY.CREATED_DATE.desc()).offset(applyInsuredPrintVo.getOffset()).limit(applyInsuredPrintVo.getPageSize());
        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(ApplyInsuredPrintBo.class);
    }

    @Override
    public List<ApplyInsuredPrintBo> queryApplyInsuredPrintManage(ApplyInsuredPrintVo applyInsuredPrintVo) {
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(APPLY.APPLY_ID, APPLY.APPLY_NO, APPLY.APPLY_DATE, APPLY.APPLY_STATUS)
                .select(APPLY.APPLY_ID.countOver().as("totalLine"))
                .select(APPLY_AGENT.AGENT_ID, APPLY_AGENT.AGENT_CODE)
                .select(APPLY_APPLICANT.COMPANY_NAME, APPLY_APPLICANT.DELEGATE_NAME, APPLY_APPLICANT.DELEGATE_MOBILE)
                .select(APPLY_INSURED_PRINT.APPLY_INSURED_PRINT_ID)
                .select(APPLY_COVERAGE.PRODUCT_ID)
                .select(APPLY_COVERAGE.PRODUCT_NAME.as("coverageName"))
                .from(APPLY)
                .innerJoin(APPLY_INSURED_PRINT).on(APPLY.APPLY_ID.eq(APPLY_INSURED_PRINT.APPLY_ID))
                .leftJoin(APPLY_AGENT).on(APPLY.APPLY_ID.eq(APPLY_AGENT.APPLY_ID))
                .leftJoin(APPLY_APPLICANT).on(APPLY.APPLY_ID.eq(APPLY_APPLICANT.APPLY_ID))
                .leftJoin(APPLY_COVERAGE).on(APPLY.APPLY_ID.eq(APPLY_COVERAGE.APPLY_ID)
                        .and(APPLY_COVERAGE.INSURED_ID.isNull().and(APPLY_COVERAGE.PRIMARY_FLAG.eq("MAIN"))))
                ;
        List<Condition> conditions = new ArrayList<>();
        //conditions.add(APPLY_INSURED_PRINT.PRINT_END_FLAG.equal(YES.name()));
        conditions.add(APPLY.VALID_FLAG.equal(effective.name()));
        if (AssertUtils.isNotEmpty(applyInsuredPrintVo.getKeyword())) {
            conditions.add(APPLY.APPLY_NO.like("%" + applyInsuredPrintVo.getKeyword() + "%")
                    .or(APPLY_APPLICANT.COMPANY_NAME.like("%" + applyInsuredPrintVo.getKeyword() + "%"))
                    .or(APPLY_AGENT.AGENT_CODE.like("%" + applyInsuredPrintVo.getKeyword() + "%")));
        }
        if (AssertUtils.isNotEmpty(applyInsuredPrintVo.getAgentIds())) {
            conditions.add(APPLY_AGENT.AGENT_ID.in(applyInsuredPrintVo.getAgentIds()));
        }

        if (AssertUtils.isNotEmpty(applyInsuredPrintVo.getPolicyNo())) {
            conditions.add(APPLY.APPLY_NO.likeRegex(applyInsuredPrintVo.getPolicyNo()));
        }
        if (AssertUtils.isNotEmpty(applyInsuredPrintVo.getCompanyName())) {
            conditions.add(APPLY_APPLICANT.COMPANY_NAME.likeRegex(applyInsuredPrintVo.getCompanyName()));
        }
        if (AssertUtils.isNotEmpty(applyInsuredPrintVo.getProductName())) {
            conditions.add(APPLY_COVERAGE.PRODUCT_NAME.likeRegex(applyInsuredPrintVo.getProductName()));
        }
        if (AssertUtils.isNotEmpty(applyInsuredPrintVo.getAgentName())) {
            conditions.add(APPLY_AGENT.AGENT_CODE.likeRegex(applyInsuredPrintVo.getAgentName()));
        }
        if (AssertUtils.isNotEmpty(applyInsuredPrintVo.getApplicantName())) {
            conditions.add(APPLY_APPLICANT.DELEGATE_NAME.likeRegex(applyInsuredPrintVo.getApplicantName()));
        }
        if (AssertUtils.isNotEmpty(applyInsuredPrintVo.getApplicantMobile())) {
            conditions.add(APPLY_APPLICANT.DELEGATE_MOBILE.likeRegex(applyInsuredPrintVo.getApplicantMobile()));
        }
        if (AssertUtils.isNotEmpty(applyInsuredPrintVo.getApplyStats())) {
            conditions.add(APPLY.APPLY_STATUS.in(applyInsuredPrintVo.getApplyStats()));
        }

        //投保时间起期  大于等于
        String createDateStartFormat = applyInsuredPrintVo.getCreateDateStartFormat();
        if (AssertUtils.isNotEmpty(createDateStartFormat) && AssertUtils.isDateFormat(createDateStartFormat)) {
            conditions.add(APPLY.APPLY_DATE.ge(DateUtils.timeToTimeLow(DateUtils.stringToTime(createDateStartFormat))));
        }
        //投保时间止期  小于等于
        String createDateEndFormat = applyInsuredPrintVo.getCreateDateEndFormat();
        if (AssertUtils.isNotEmpty(createDateEndFormat) && AssertUtils.isDateFormat(createDateEndFormat)) {
            conditions.add(APPLY.APPLY_DATE.le(DateUtils.timeToTimeTop(DateUtils.stringToTime(createDateEndFormat))));
        }

        selectJoinStep.where(conditions);
        selectJoinStep.orderBy(APPLY.CREATED_DATE.desc()).offset(applyInsuredPrintVo.getOffset()).limit(applyInsuredPrintVo.getPageSize());
        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(ApplyInsuredPrintBo.class);
    }

    @Override
    public ApplyInsuredPrintPo queryApplyInsuredPrintPo(String applyInsuredPrintId) {
        return this.getDslContext().select(APPLY_INSURED_PRINT.fields())
                .from(APPLY_INSURED_PRINT).where(APPLY_INSURED_PRINT.APPLY_INSURED_PRINT_ID.eq(applyInsuredPrintId)).fetchOneInto(ApplyInsuredPrintPo.class);
    }

    @Override
    public ApplyInsuredPrintDetailPo queryApplyInsuredPrintPoByLanguage(String applyId, String languageCode) {
        SelectOffsetStep<Record> recordSelectOffsetStep = this.getDslContext().select(APPLY_INSURED_PRINT_DETAIL.fields())
                .select(APPLY_INSURED_PRINT.APPLY_ID.as("applyId"))
                .from(APPLY_INSURED_PRINT_DETAIL)
                .innerJoin(APPLY_INSURED_PRINT)
                .on(APPLY_INSURED_PRINT.APPLY_ID.eq(APPLY_INSURED_PRINT_DETAIL.APPLY_ID))
                .where(APPLY_INSURED_PRINT.APPLY_ID.eq(applyId)
                        .and(APPLY_INSURED_PRINT_DETAIL.LANGUAGE.eq(languageCode)))
                .limit(1);
        this.getLogger().info("sql:" + recordSelectOffsetStep.toString());
        return recordSelectOffsetStep.fetchOneInto(ApplyInsuredPrintDetailPo.class);
    }

    @Override
    public ApplyInsuredPrintPo queryApplyInsuredPrintByApplyId(String applyId) {
        return this.getDslContext().select(APPLY_INSURED_PRINT.fields())
                .from(APPLY_INSURED_PRINT).where(APPLY_INSURED_PRINT.APPLY_ID.eq(applyId)).fetchOneInto(ApplyInsuredPrintPo.class);
    }

    @Override
    public List<ActualPerformanceReportBo> queryActualPerformance(List<String> policyIdList) {

        Table<Record> apply_coverage_level = this.getDslContext()
                .select(APPLY_COVERAGE_LEVEL.PRODUCT_LEVEL)
                .select(APPLY_COVERAGE_LEVEL.MULT)
                .select(APPLY_COVERAGE.PRODUCT_ID.decode(
                        "PRO8800000000000G12", new BigDecimal(0), APPLY_COVERAGE.PREMIUM).as("coveragePremium"))
                .select(APPLY_COVERAGE.PRODUCT_ID.decode(
                        "PRO8800000000000G12", new BigDecimal(0), APPLY_COVERAGE_LEVEL.PREMIUM)
                        .as(APPLY_COVERAGE_LEVEL.PREMIUM))
                .select(APPLY_COVERAGE_LEVEL.TOTAL_PREMIUM)
                .select(APPLY_COVERAGE_LEVEL.AMOUNT)
                .select(APPLY_COVERAGE.ORIGINAL_PREMIUM)
                .select(APPLY_COVERAGE.COVERAGE_ID)
                .select(APPLY_COVERAGE_LEVEL.COVERAGE_DUTY_ID)
                .from(APPLY_COVERAGE)
                .leftJoin(APPLY_COVERAGE_LEVEL)
                .on(APPLY_COVERAGE.COVERAGE_ID.eq(APPLY_COVERAGE_LEVEL.COVERAGE_ID)).asTable();

        Field<BigDecimal> totalAmount = DSL.field("to_number({0}, '999999999999999999999999999999.99')", SQLDataType.NUMERIC, APPLY_COVERAGE.AMOUNT.decode("", "0", null, "0", APPLY_COVERAGE.AMOUNT));
        SelectOnConditionStep<Record> selectOnConditionStep = this.getDslContext()
                .select(APPLY.APPLY_ID)
                .select(APPLY.POLICY_NO)
                .select(APPLY.APPLY_NO)
                .select(APPLY.EFFECTIVE_DATE)
                .select(APPLY.SALES_BRANCH_ID)
                .select(APPLY_INSURED.BIRTHDAY.as("insuredBirthday"))
                .select(APPLY_APPLICANT.NAME.as("applicantName"))
                .select(APPLY_COVERAGE.PREMIUM_FREQUENCY)
                .select(APPLY_COVERAGE.ORIGINAL_PREMIUM.sum().as(APPLY_COVERAGE.ORIGINAL_PREMIUM))
                .select(APPLY_COVERAGE.PRODUCT_ID)
                .select(APPLY_COVERAGE.PRODUCT_CODE)
                .select(APPLY_COVERAGE.PRIMARY_FLAG)
                .select(APPLY_COVERAGE.PREMIUM_PERIOD)
                .select(APPLY_COVERAGE.COVERAGE_PERIOD)
                .select(APPLY_COVERAGE.COVERAGE_PERIOD_UNIT)
                .select(DSL.when(apply_coverage_level.field(APPLY_COVERAGE_LEVEL.AMOUNT).isNull(), totalAmount)
                        .when(apply_coverage_level.field(APPLY_COVERAGE_LEVEL.AMOUNT).eq(BigDecimal.ZERO), totalAmount)
                        .otherwise(apply_coverage_level.field(APPLY_COVERAGE_LEVEL.AMOUNT)).sum().as(APPLY_COVERAGE.AMOUNT))
                .select(DSL.when(apply_coverage_level.field(APPLY_COVERAGE_LEVEL.AMOUNT).isNull(), totalAmount)
                        .when(apply_coverage_level.field(APPLY_COVERAGE_LEVEL.AMOUNT).eq(BigDecimal.ZERO), totalAmount)
                        .otherwise(apply_coverage_level.field(APPLY_COVERAGE_LEVEL.AMOUNT)).sum().as("totalAmount"))
                .select(APPLY_COVERAGE_DUTY.DUTY_ID)
                .select(apply_coverage_level.field(APPLY_COVERAGE_LEVEL.PRODUCT_LEVEL).nvl(APPLY_COVERAGE.PRODUCT_LEVEL).as(APPLY_COVERAGE.PRODUCT_LEVEL))
                .select(apply_coverage_level.field(APPLY_COVERAGE_LEVEL.MULT).nvl(APPLY_COVERAGE.MULT).nvl("1").sum().as("mult"))
                .select(APPLY.APPLY_TYPE.decode(
                        LIFE_INSURANCE_GROUP.name(),
                        apply_coverage_level.field(APPLY_COVERAGE_LEVEL.PREMIUM),
                        LIFE_INSURANCE_PERSONAL.name(), apply_coverage_level.field("coveragePremium")).sum().as("premium"))
                .select(apply_coverage_level.field(APPLY_COVERAGE_LEVEL.TOTAL_PREMIUM).nvl(APPLY_COVERAGE.TOTAL_PREMIUM).sum().as("totalPremium"))
                .select(APPLY.APPLY_TYPE.decode(
                        LIFE_INSURANCE_GROUP.name(), "RESOURCE_APPLY_GROUP",
                        LIFE_INSURANCE_PERSONAL.name(), "RESOURCE_APPLY_INDIVIDUAL").as("coverageType"))
                .from(APPLY)
                .leftJoin(APPLY_APPLICANT).on(APPLY_APPLICANT.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_INSURED).on(APPLY_INSURED.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY.APPLY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name())))
                .leftJoin(APPLY_COVERAGE).on(APPLY_COVERAGE.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_COVERAGE.INSURED_ID.isNotNull()))
                .leftJoin(apply_coverage_level).on(apply_coverage_level.field(APPLY_COVERAGE_LEVEL.COVERAGE_ID).eq(APPLY_COVERAGE.COVERAGE_ID))
                .leftJoin(APPLY_COVERAGE_DUTY).on(APPLY_COVERAGE_DUTY.COVERAGE_DUTY_ID.eq(apply_coverage_level.field(APPLY_COVERAGE_LEVEL.COVERAGE_DUTY_ID)));

        selectOnConditionStep.where(APPLY.APPLY_ID.in(policyIdList));

        selectOnConditionStep.groupBy(APPLY.APPLY_ID,
                APPLY.POLICY_NO,
                APPLY.APPLY_NO,
                APPLY.SALES_BRANCH_ID,
                APPLY.EFFECTIVE_DATE,
                APPLY_INSURED.BIRTHDAY,
                APPLY_APPLICANT.NAME,
                APPLY_COVERAGE.PREMIUM_FREQUENCY,
                APPLY_COVERAGE.PRODUCT_ID,
                APPLY_COVERAGE.PRODUCT_CODE,
                APPLY_COVERAGE.PRIMARY_FLAG,
                APPLY_COVERAGE.PREMIUM_PERIOD,
                APPLY_COVERAGE.COVERAGE_PERIOD,
                APPLY_COVERAGE.COVERAGE_PERIOD_UNIT,
//                APPLY_COVERAGE.ORIGINAL_PREMIUM,
//                APPLY_COVERAGE.AMOUNT,
                APPLY_COVERAGE_DUTY.DUTY_ID,
                apply_coverage_level.field(APPLY_COVERAGE_LEVEL.PRODUCT_LEVEL),
                APPLY_COVERAGE.PRODUCT_LEVEL,
                APPLY.APPLY_TYPE,
//                apply_coverage_level.field(APPLY_COVERAGE_LEVEL.PREMIUM),
//                apply_coverage_level.field("coveragePremium"),
                APPLY.APPLY_TYPE);

        return selectOnConditionStep.fetchInto(ActualPerformanceReportBo.class);
    }

    /**
     * 投保单ID查询受益人信息
     *
     * @param applyId
     * @param modifyFlag
     * @return
     */
    @Override
    public List<ApplyBeneficiaryInfoBo> queryApplyLoanBeneficiary(String applyId, String modifyFlag) {
        return this.getDslContext()
                .select(APPLY_BENEFICIARY.fields())
                .select(APPLY_BENEFICIARY_INFO.fields())
                .from(APPLY_BENEFICIARY)
                .innerJoin(APPLY_BENEFICIARY_INFO).on(APPLY_BENEFICIARY.BENEFICIARY_ID.eq(APPLY_BENEFICIARY_INFO.BENEFICIARY_ID))
                .where(APPLY_BENEFICIARY.APPLY_ID.eq(applyId).and(APPLY_BENEFICIARY_INFO.MODIFY_FLAG.eq(modifyFlag)))
                .fetch().stream().map(record -> {
                    //受益人
                    ApplyBeneficiaryBo policyBeneficiaryBo = BasePojo.getInstance(ApplyBeneficiaryBo.class, record.into(ApplyBeneficiaryRecord.class));
                    ApplyBeneficiaryInfoBo policyBeneficiaryInfoBo = BasePojo.getInstance(ApplyBeneficiaryInfoBo.class, record.into(ApplyBeneficiaryInfoRecord.class));
                    policyBeneficiaryInfoBo.setApplyBeneficiaryBo(policyBeneficiaryBo);
                    return policyBeneficiaryInfoBo;
                }).collect(Collectors.toList());
    }

    @Override
    public List<ServiceChargeBankChannelBo> syncApplyServiceChargeBankChannel(BasePageRequest basePageRequest, String syncDate) {

        long monthLastDay = DateUtils.getThisMonthLastDay(syncDate, DateUtils.FORMATE2);
        long monthFirstDay = DateUtils.getThisMonthFirstDay(syncDate, DateUtils.FORMATE2);

        List<String> applyStatusList = Arrays.asList(
                APPLY_STATUS_INPUT_REVIEW.name(),
                APPLY_STATUS_INPUT_REVIEW_COMPLETE.name(),
                APPLY_STATUS_UNDERWRITE_ARTIFICIAL.name(),
                APPLY_STATUS_PAID_PENDING_ON_UW.name(),
                APPLY_STATUS_INITIAL_COMPLETE.name(),
                APPLY_STATUS_UNDERWRITE_PASS.name());

        Table<Record> applyCoverageTable = this.getDslContext()
                .select(APPLY_COVERAGE.APPLY_ID)
                .select(APPLY_COVERAGE.PRODUCT_ID)
                .select(APPLY_COVERAGE.TOTAL_PREMIUM.sum().as(APPLY_COVERAGE.TOTAL_PREMIUM))
                .from(APPLY_COVERAGE)
                .where(APPLY_COVERAGE.INSURED_ID.isNotNull(), APPLY_COVERAGE.VALID_FLAG.eq(effective.name()))
                .groupBy(APPLY_COVERAGE.APPLY_ID, APPLY_COVERAGE.PRODUCT_ID).asTable();

        SelectForUpdateStep<Record> recordSelectForUpdateStep = this.getDslContext()
                .select(APPLY.APPLY_NO)
                .select(APPLY.APPLY_DATE)
                .select(APPLY.APPLY_TYPE.as("policyType"))
                .select(APPLY_AGENT.AGENT_CODE)
                .select(APPLY_AGENT.AGENT_ID)
                .select(APPLY_APPLICANT.NAME.as("applicantName"))
                .select(APPLY_APPLICANT.COMPANY_NAME)
                .select(APPLY_INSURED.NAME.as("insuredName"))
                .select(APPLY.SALES_BRANCH_ID)
                .select(APPLY_COVERAGE.PRODUCT_ID)
                .select(APPLY_COVERAGE.PRODUCT_NAME)
                .select(APPLY_COVERAGE.PRODUCT_LEVEL)
                .select(APPLY_COVERAGE.PREMIUM_FREQUENCY)
                .select(APPLY_COVERAGE.PRIMARY_FLAG)
                .select(applyCoverageTable.field(APPLY_COVERAGE.TOTAL_PREMIUM).as("actualPremium"))
                .select(APPLY.APPLY_STATUS)
                .select(APPLY_REFERRAL_INFO.REFERRAL_SOURCES)
                .select(APPLY_REFERRAL_INFO.INTRODUCER_NAME)
                .select(APPLY_REFERRAL_INFO.INTRODUCER_POSITION)
                .select(APPLY_COVERAGE.COVERAGE_ID.countOver().as("totalLine"))
                .from(APPLY)
                .leftJoin(APPLY_REFERRAL_INFO)
                .on(APPLY_REFERRAL_INFO.APPLY_ID.eq(APPLY.APPLY_ID), APPLY_REFERRAL_INFO.VALID_FLAG.eq(effective.name()))
                .leftJoin(APPLY_AGENT)
                .on(APPLY_AGENT.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_APPLICANT)
                .on(APPLY_APPLICANT.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_INSURED)
                .on(APPLY_INSURED.APPLY_ID.eq(APPLY.APPLY_ID), APPLY.APPLY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()))
                .leftJoin(APPLY_COVERAGE)
                .on(
                        APPLY_COVERAGE.VALID_FLAG.eq(effective.name()),
                        APPLY.APPLY_ID.eq(APPLY_COVERAGE.APPLY_ID),
                        (
                                APPLY.APPLY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()).and(APPLY_COVERAGE.INSURED_ID.isNotNull())
                        ).or(
                                APPLY.APPLY_TYPE.eq(LIFE_INSURANCE_GROUP.name()).and(APPLY_COVERAGE.INSURED_ID.isNull())
                        )
                )
                .leftJoin(applyCoverageTable)
                .on(applyCoverageTable.field(APPLY_COVERAGE.APPLY_ID).eq(APPLY_COVERAGE.APPLY_ID), applyCoverageTable.field(APPLY_COVERAGE.PRODUCT_ID).eq(APPLY_COVERAGE.PRODUCT_ID))
                .where(APPLY.APPLY_DATE.between(monthFirstDay, monthLastDay), APPLY.APPLY_STATUS.in(applyStatusList), APPLY.CHANNEL_TYPE_CODE.eq(BANK.name()))
                .orderBy(APPLY_COVERAGE.COVERAGE_ID)
                .offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());

        System.out.println(recordSelectForUpdateStep.toString());


        return recordSelectForUpdateStep.fetchInto(ServiceChargeBankChannelBo.class);
    }

    @Override
    public List<SaleApplyPolicyBo> syncSaleApplyPolicy(BasePageRequest basePageRequest, String syncDate) {
        long hostLastDay = DateUtils.getCurrentTime();
        long hostFirstDay = DateUtils.stringToTime(syncDate, DateUtils.FORMATE51);


        SelectOnConditionStep<Record> selectOnConditionStep = this.getDslContext()
                .select(APPLY.APPLY_ID)
                .select(APPLY.POLICY_NO)
                .select(APPLY.APPLY_NO)
                .select(APPLY.APPLY_DATE)
                .select(APPLY.APPLY_TYPE)
                .select(APPLY_APPLICANT.NAME.as("applicantName"))
                .select(APPLY_APPLICANT.ID_TYPE.as("applicantIdType"))
                .select(APPLY_APPLICANT.ID_NO.as("applicantIdNo"))
                .select(APPLY_APPLICANT.MOBILE.as("applicantMobile"))
                .select(APPLY_APPLICANT.COMPANY_NAME)
                .select(APPLY_APPLICANT.COMPANY_ID_TYPE)
                .select(APPLY_APPLICANT.COMPANY_ID_NO)
                .select(APPLY_APPLICANT.COMPANY_PHONE)
                .select(APPLY_INSURED.NAME.as("insuredName"))
                .select(APPLY_INSURED.BIRTHDAY.as("insuredBirthday"))
                .select(APPLY.SALES_BRANCH_ID)
                .select(APPLY.CHANNEL_TYPE_CODE)
                .select(APPLY_AGENT.AGENT_ID)
                .select(APPLY.INVALID_DATE.as("applyInvalidDate"))
                .select(APPLY_PREMIUM.ACTUAL_PREMIUM.as("premiumActualPremium"))
                .select(APPLY.APP_SUBMIT_UNDERWRITING_DATE.nvl(APPLY.APPLY_DATE).as(APPLY.APP_SUBMIT_UNDERWRITING_DATE))
                .select(APPLY_UNDERWRITE_TASK.CREATED_DATE.as("underwriteStartDate"))
                .select(APPLY_UNDERWRITE_DECISION.CREATED_DATE.as("underwriteEndDate"))
                .select(APPLY.APPLY_STATUS)
                .select(APPLY.APPLY_ID.countOver().as("totalLine"))
                .from(APPLY)
                .leftJoin(APPLY_APPLICANT)
                .on(APPLY_APPLICANT.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_INSURED)
                .on(APPLY_INSURED.APPLY_ID.eq(APPLY.APPLY_ID), APPLY.APPLY_TYPE.eq(LIFE_INSURANCE_PERSONAL.name()))
                .leftJoin(APPLY_AGENT)
                .on(APPLY_AGENT.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_UNDERWRITE_TASK)
                .on(APPLY_UNDERWRITE_TASK.CONTROL_NO.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_UNDERWRITE_DECISION)
                .on(APPLY_UNDERWRITE_DECISION.UNDERWRITE_TASK_ID.eq(APPLY_UNDERWRITE_TASK.UNDERWRITE_TASK_ID))
                .leftJoin(APPLY_PREMIUM)
                .on(APPLY_PREMIUM.APPLY_ID.eq(APPLY.APPLY_ID));
        List<Condition> conditionList = new ArrayList<>();
        conditionList.add(APPLY.CREATED_DATE.between(hostFirstDay, hostLastDay).or(APPLY.UPDATED_DATE.between(hostFirstDay, hostLastDay)));
        selectOnConditionStep.where(conditionList);
        selectOnConditionStep.orderBy(APPLY.APPLY_ID);
        selectOnConditionStep.offset(basePageRequest.getOffset()).limit(basePageRequest.getPageSize());
        System.out.println(selectOnConditionStep.toString());
        return selectOnConditionStep.fetchInto(SaleApplyPolicyBo.class);
    }

    @Override
    public List<SaleApplyPolicyBo> syncSaleApplyPolicyCoverage(List<String> applyIdList) {
        Field<BigDecimal> PAYMENT_INSTALLMENTS = APPLY_COVERAGE.PAYMENT_INSTALLMENTS.nvl(1L).cast(BigDecimal.class);
        SelectHavingStep<Record> selectHavingStep = this.getDslContext()
                .select(APPLY_COVERAGE.APPLY_ID)
                .select(APPLY_COVERAGE.PRODUCT_ID)
                .select(APPLY_COVERAGE.PRODUCT_CODE)
                .select(APPLY_COVERAGE.PREMIUM_FREQUENCY)
                .select(APPLY_COVERAGE.COVERAGE_PERIOD)
                .select(APPLY_COVERAGE.COVERAGE_PERIOD_UNIT)
                .select(APPLY_COVERAGE.PREMIUM_PERIOD)
                .select(APPLY_COVERAGE.PREMIUM_PERIOD_UNIT)
                .select(APPLY_COVERAGE.PRIMARY_FLAG)
                //首期保费取折扣前的总保费 * 实际缴费期数
                .select(APPLY_COVERAGE.TOTAL_PREMIUM.multiply(PAYMENT_INSTALLMENTS).sum().as(APPLY_COVERAGE.ACTUAL_PREMIUM))
                .select(APPLY_COVERAGE.AMOUNT
                        .decode(null, new BigDecimal(0),
                                "", new BigDecimal(0), APPLY_COVERAGE.AMOUNT.cast(BigDecimal.class))
                        .sum().as(APPLY_COVERAGE.AMOUNT))
                .from(APPLY_COVERAGE)
                .leftJoin(APPLY)
                .on(APPLY_COVERAGE.APPLY_ID.eq(APPLY.APPLY_ID))
                .where(APPLY.APPLY_ID.in(applyIdList), APPLY_COVERAGE.INSURED_ID.isNotNull())
                .groupBy(APPLY_COVERAGE.APPLY_ID,
                        APPLY_COVERAGE.PRODUCT_ID,
                        APPLY_COVERAGE.PRODUCT_CODE,
                        APPLY_COVERAGE.PREMIUM_FREQUENCY,
                        APPLY_COVERAGE.COVERAGE_PERIOD,
                        APPLY_COVERAGE.COVERAGE_PERIOD_UNIT,
                        APPLY_COVERAGE.PREMIUM_PERIOD,
                        APPLY_COVERAGE.PREMIUM_PERIOD_UNIT,
                        APPLY_COVERAGE.PRIMARY_FLAG,
                        APPLY_COVERAGE.PAYMENT_INSTALLMENTS);

        return selectHavingStep.fetchInto(SaleApplyPolicyBo.class);
    }

    @Override
    public List<ApplyUnderwriteProblemPo> queryApplyUnderwriteProblemPo(String applyId) {
        return this.getDslContext().selectFrom(APPLY_UNDERWRITE_PROBLEM).where(APPLY_UNDERWRITE_PROBLEM.APPLY_ID.eq(applyId)
                .and(APPLY_UNDERWRITE_PROBLEM.VALID_FLAG.eq(effective.name())))
                .fetchInto(ApplyUnderwriteProblemPo.class);
    }

    @Override
    public List<ApplyPaymentTransactionBo> queryApplyPaymentTransactions(String applyId) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(APPLY_PAYMENT_TRANSACTION.APPLY_ID.eq(applyId));
        conditions.add(APPLY_PAYMENT_TRANSACTION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        List<ApplyPaymentTransactionBo> applyPaymentTransactionBos = this.getDslContext()
                .select(APPLY_PAYMENT_TRANSACTION.fields())
                .from(APPLY_PAYMENT_TRANSACTION)
                .where(conditions)
                .orderBy(APPLY_PAYMENT_TRANSACTION.PAYMENT_DATE.desc())
                .fetchInto(ApplyPaymentTransactionBo.class);
        if (AssertUtils.isNotEmpty(applyPaymentTransactionBos)) {
            applyPaymentTransactionBos.forEach(applyPaymentTransactionBo -> {
                List<ApplyPaymentTransactionItemPo> applyPaymentTransactionItemList = this.getApplyPaymentTransactionItemList(applyPaymentTransactionBo.getPaymentTransactionId(), null);
                applyPaymentTransactionBo.setApplyPaymentTransactionItemBos((List<ApplyPaymentTransactionItemBo>) this.converterList(applyPaymentTransactionItemList, new TypeToken<List<ApplyPaymentTransactionItemBo>>() {
                }.getType()));
            });
        }
        return applyPaymentTransactionBos;
    }

    @Override
    public List<ApplyPaymentTransactionBo> queryApplyPaymentTransactionList(List<String> applyIdList) {
        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(APPLY_PAYMENT_TRANSACTION.fields())
                .from(APPLY_PAYMENT_TRANSACTION)
                .where(APPLY_PAYMENT_TRANSACTION.APPLY_ID.in(applyIdList),
                        APPLY_PAYMENT_TRANSACTION.VALID_FLAG.eq(effective.name()));
        return selectConditionStep.fetchInto(ApplyPaymentTransactionBo.class);
    }

    /**
     * 查询合同信息上传表
     *
     * @param insuredBatchId 批次ID
     * @return ApplyContactUploadPo
     */
    @Override
    public ApplyContactUploadPo getApplyContactUploadPo(String insuredBatchId) {
        return this.getDslContext().selectFrom(APPLY_CONTACT_UPLOAD).where(APPLY_CONTACT_UPLOAD.INSURED_BATCH_ID.eq(insuredBatchId), APPLY_CONTACT_UPLOAD.VALID_FLAG.eq(effective.name()))
                .orderBy(APPLY_CONTACT_UPLOAD.CREATED_DATE.desc()).limit(1)
                .fetchOneInto(ApplyContactUploadPo.class);
    }

    /**
     * 查询合同信息完成表
     *
     * @param insuredBatchId 批次ID
     * @return ApplyContactDonePo
     */
    @Override
    public ApplyContactDonePo getApplyContactDonePo(String insuredBatchId) {
        return this.getDslContext().selectFrom(APPLY_CONTACT_DONE).where(APPLY_CONTACT_DONE.INSURED_BATCH_ID.eq(insuredBatchId), APPLY_CONTACT_DONE.VALID_FLAG.eq(effective.name()))
                .orderBy(APPLY_CONTACT_DONE.CREATED_DATE.desc()).limit(1)
                .fetchOneInto(ApplyContactDonePo.class);
    }

    /**
     * 获取投保单存在客户列表
     *
     * @param applyId        投保单ID
     * @param allCustomerIds 客户ID
     * @param notApproveFlag
     * @return ApplyRealClientListBos
     */
    @Override
    public List<ApplyRealClientListBo> getApplyRealClient(String applyId, List<String> allCustomerIds, boolean notApproveFlag) {
        SelectOnConditionStep on = this.getDslContext()
                .selectDistinct(APPLY.APPLY_ID, APPLY.APPLY_NO, APPLY.APPLY_STATUS, APPLY.APPLY_TYPE, APPLY.CREATED_DATE)
                .select(APPLY_APPLICANT.NAME, APPLY_APPLICANT.ID_TYPE, APPLY_APPLICANT.ID_NO, APPLY_APPLICANT.BIRTHDAY, APPLY_APPLICANT.SEX)
                .select(APPLY_COVERAGE.PRODUCT_NAME)
                .from(APPLY)
                .leftJoin(APPLY_COVERAGE).on(APPLY.APPLY_ID.eq(APPLY_COVERAGE.APPLY_ID), APPLY_COVERAGE.VALID_FLAG.eq(effective.name()),APPLY_COVERAGE.PRIMARY_FLAG.eq(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                .leftJoin(APPLY_APPLICANT).on(APPLY.APPLY_ID.eq(APPLY_APPLICANT.APPLY_ID), APPLY_APPLICANT.VALID_FLAG.eq(effective.name()))
                .leftJoin(APPLY_INSURED).on(APPLY.APPLY_ID.eq(APPLY_INSURED.APPLY_ID), APPLY_INSURED.VALID_FLAG.eq(effective.name()))
//                .leftJoin(APPLY_BENEFICIARY).on(APPLY.APPLY_ID.eq(APPLY_BENEFICIARY.APPLY_ID), APPLY_BENEFICIARY.VALID_FLAG.eq(effective.name()))
                ;
        on.where(APPLY.APPLY_ID.ne(applyId), APPLY.VALID_FLAG.eq(effective.name()));

        List<String> notInStatus = new ArrayList<>();
        notInStatus.add(APPLY_STATUS_INITIAL.name());
        if (notApproveFlag) {
            notInStatus.add(APPLY_STATUS_APPROVE_SUCCESS.name());
        }
        on.where(APPLY.APPLY_STATUS.notIn(notInStatus));

        on.where(APPLY_APPLICANT.CUSTOMER_ID.in(allCustomerIds)
                        .or(APPLY_INSURED.CUSTOMER_ID.in(allCustomerIds))
//                .or(APPLY_BENEFICIARY.CUSTOMER_ID.in(allCustomerIds))
        );
        on.orderBy(APPLY.CREATED_DATE, APPLY.APPLY_ID);
//        System.out.println(on.toString());
        return on.fetchInto(ApplyRealClientListBo.class);
    }

    /**
     * 查询网销重复的投保单
     *
     * @param applyId    投保单ID
     * @param customerId 客户ID
     * @return ApplyPos
     */
    @Override
    public List<ApplyPo> getRepeatOnlineApply(String applyId, String customerId) {
        SelectOnConditionStep<Record> on = this.getDslContext().select(APPLY.fields())
                .from(APPLY)
                .innerJoin(APPLY_APPLICANT).on(APPLY.APPLY_ID.eq(APPLY_APPLICANT.APPLY_ID), APPLY_APPLICANT.VALID_FLAG.eq(effective.name()), APPLY_APPLICANT.CUSTOMER_ID.eq(customerId));
        on.where(APPLY.APPLY_ID.ne(applyId), APPLY.VALID_FLAG.eq(effective.name()),
                        APPLY.APPLY_STATUS.in(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL.name(), ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name()),
                        APPLY.CHANNEL_TYPE_CODE.eq(ApplyTermEnum.CHANNEL_TYPE.ONLINE.name()));
        on.orderBy(APPLY.CREATED_DATE, APPLY.APPLY_ID);
        System.out.println(on.toString());
        return on.fetchInto(ApplyPo.class);
    }
}
