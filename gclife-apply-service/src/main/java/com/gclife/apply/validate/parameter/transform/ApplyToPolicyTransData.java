package com.gclife.apply.validate.parameter.transform;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.app.api.AppIntegralTaskApi;
import com.gclife.apply.core.jooq.tables.pojos.*;
import lombok.extern.slf4j.Slf4j;
import com.gclife.apply.dao.ApplyExtDao;
import com.gclife.apply.dao.ApplyUnderWriteExtDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.response.ApplySpecialContractResponse;
import com.gclife.apply.model.response.ProductHealthNoticeResponse;
import com.gclife.apply.service.*;
import com.gclife.apply.service.business.EventBusinessService;
import com.gclife.apply.service.business.MessageBusinessService;
import com.gclife.apply.service.data.ApplyBoService;
import com.gclife.apply.transform.ApplyDataTransform;
import com.gclife.apply.validate.parameter.PaymentParameterValidate;
import com.gclife.apply.validate.parameter.group.LanguageUtils;
import com.gclife.attachment.api.AttachmentPDFDocumentApi;
import com.gclife.attachment.model.policy.apply.ElectronicSignatureAttachmentBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.InternationalTypeEnum;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.payment.api.PaymentPrintApi;
import com.gclife.platform.api.*;
import com.gclife.platform.model.request.AccountRequest;
import com.gclife.platform.model.response.AreaNameResponse;
import com.gclife.platform.model.response.CareerResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.platform.model.response.UserResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.api.PolicyGenerateApi;
import com.gclife.policy.model.vo.*;
import com.gclife.product.api.ProductDutyApi;
import com.gclife.product.api.ProductRateApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.request.insurance.policy.PolicyPaymentRequest;
import com.gclife.product.model.response.duty.DutyResponse;
import com.gclife.product.model.response.insurnce.policy.PolicyPaymentResponse;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

import static com.gclife.apply.model.config.ApplyErrorConfigEnum.APPLY_GENERATE_APPLY_RECEIPT_BOOK_ERROR;
import static com.gclife.apply.model.config.ApplyTermEnum.ANNUAL_EARNED_INCOME.ANNUAL_EARNED_INCOME;
import static com.gclife.apply.model.config.ApplyTermEnum.CHANNEL_TYPE.BANK;
import static com.gclife.common.TerminologyTypeEnum.BRANCH_NAME;
import static java.lang.StrictMath.random;

/**
 * <AUTHOR>
 * create 17-10-24
 * description:
 */
@Component
@Slf4j
public class ApplyToPolicyTransData extends BaseBusinessServiceImpl {

    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private PlatformUsersBaseApi platformUsersBaseApi;
    @Autowired
    private PlatformCareerApi platformCareerApi;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private PlatformAccountApi platformAccountApi;
    @Autowired
    private PlatformAreaApi platformAreaApi;
    @Autowired
    private PolicyGenerateApi policyGenerateApi;
    @Autowired
    private ProductRateApi productRateApi;
    @Autowired
    private ProductDutyApi productDutyApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private ApplyExtDao applyExtDao;
    @Autowired
    private PaymentParameterValidate paymentParameterValidate;
    @Autowired
    private AttachmentPDFDocumentApi pdfDocumentApi;
    @Autowired
    private AppIntegralTaskApi integralTaskApi;
    @Autowired
    private MessageBusinessService messageBusinessService;
    @Autowired
    private ApplyBoService applyBoService;
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private ApplyDataTransform applyDataTransform;
    @Autowired
    private PaymentPrintApi paymentPrintApi;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private EventBusinessService eventBusinessService;
    @Autowired
    private ApplyAttachmentBaseService applyAttachmentBaseService;
    @Autowired
    private ApplyCoverageBaseService applyCoverageBaseService;
    @Autowired
    private ApplyRemarkBaseService applyRemarkBaseService;
    @Autowired
    private ApplySpecialContractBaseService applySpecialContractBaseService;
    @Autowired
    private ApplyUnderwriteBaseService applyUnderwriteBaseService;
    @Autowired
    private ApplyUnderWriteExtDao applyUnderWriteExtDao;


    /**
     * 投保单转换保单
     *
     * @param applyId 投保单ID
     */
    public PolicyVo transferApplyToPolicy(String applyId, String actualPayDate) {
        this.getLogger().info("投保单转保单开始:[{}]", DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
        //查询投保单
        ApplyBo applyBo = applyBaseService.queryApply(applyId);
        this.getLogger().info("投保单转保单开始--applyBo:[{}]", JSON.toJSONString(applyBo));
        //数据转换
        PolicyVo policyVo = transApplyDataToPolicyData(applyBo, actualPayDate);
        this.getLogger().info("投保单转保单完成:[{}]", JSON.toJSONString(policyVo));
        //投保单转保单
        ResultObject<PolicyVo> resPolicyResult = policyGenerateApi.applyTransToPolicy(policyVo);
        //判断是否转换成功
        AssertUtils.isResultObjectDataNull(this.getLogger(), resPolicyResult);
        //保存保单号
        applyBo.setPolicyNo(resPolicyResult.getData().getPolicyNo());
        applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_SUCCESS.name());
        applyBo.setEffectiveDate(resPolicyResult.getData().getEffectiveDate());
        applyBo.setPolicyId(resPolicyResult.getData().getPolicyId());
        applyBoService.saveApplyPo(applyBo);
        //出单体验
        applyExperience(applyBo);
        System.out.println("transferApplyToPolicy end time :" + DateUtils.timeStrToString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));

        // saveUserEventRecordImpl
        applyDataTransform.saveUserEventRecordApply(applyBo);

        //保存投保单操作表
        ApplyOperationPo applyOperationPo = applyBaseService.queryApplyOperationPo(applyId);
        if (AssertUtils.isNotNull(applyOperationPo) && ApplyTermEnum.APPLY_OPERATION_CODE.REAPPLYING.name().equals(applyOperationPo.getOperationCode())) {
            applyOperationPo.setOperationCode(ApplyTermEnum.APPLY_OPERATION_CODE.REAPPLY_END.name());
            applyBaseService.saveApplyOperationPo(applyOperationPo, null);
        }

        try {
            doApproveMessage(applyBo);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //生成投保单PDF附件
        new Thread(() -> {
            ApplyThreadLocalBo.asyncFlag.set(true);// 可以使用消息队列进行异步生成
            attachmentPdfGenerate(applyBo, TerminologyConfigEnum.LANGUAGE.EN_US.name());
            attachmentPdfGenerate(applyBo, TerminologyConfigEnum.LANGUAGE.ZH_CN.name());
            attachmentPdfGenerate(applyBo, TerminologyConfigEnum.LANGUAGE.KM_KH.name());
            ApplyThreadLocalBo.asyncFlag.set(false);
//            ApplyPremiumBo applyPremiumBo = applyBo.getApplyPremiumBo();
//            if (AssertUtils.isNotNull(applyPremiumBo)) {
//                attachmentApplyReceipt(applyPremiumBo.getPaymentId(), TerminologyConfigEnum.LANGUAGE.EN_US.name());
//            }
        }).start();

        return resPolicyResult.getData();
    }

    public void doApproveMessage(ApplyBo applyBo) {
        // 发消息给业务员
        // 查询代理人信息
        System.out.println("===================================发送1==============================================");
        AgentResponse applyAgentRespFc = agentApi.agentByIdGet(applyBo.getApplyAgentBo().getAgentId()).getData();
        applyBo.getApplyAgentBo().setAgentName(applyAgentRespFc.getAgentName());
        messageBusinessService.pushApplyMessageSingle(
                ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_UNDERWRITE_FOR_AGENT_CP.name(), applyBo, applyBo.getApplyAgentBo().getAgentId());
        System.out.println("===================================发送2==============================================");
        //产生喜报
        eventBusinessService.generateEvent(applyBo, ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_UNDERWRITE_DRAW_CARD_FOR_AGENT_CP.name());
        // 发消息给推荐人
        // 保单出单成功推荐人需要发一代二代推荐人，推荐人为寿险顾问才发此消息
        AgentResponse applyAgentRespFc1 = agentApi.agentByIdGet(applyAgentRespFc.getRecommendAgentId()).getData();
        if (AssertUtils.isNotNull(applyAgentRespFc1)) {
            if (ApplyTermEnum.AGENT_TYPE.LIFE_CONSULTANT.name().equals(applyAgentRespFc1.getAgentTypeCode())) {
                applyBo.getApplyAgentBo().setRecommendAgentName(applyAgentRespFc1.getAgentName());
                messageBusinessService.pushApplyMessageSingle(
                        ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_TRANFER_POLICY_RECOMMEND_CP.name(), applyBo, applyAgentRespFc.getRecommendAgentId());
            }

            //二代
            AgentResponse applyAgentRespFc2 = agentApi.agentByIdGet(applyAgentRespFc1.getRecommendAgentId()).getData();
            if (AssertUtils.isNotNull(applyAgentRespFc2)) {
                if (ApplyTermEnum.AGENT_TYPE.LIFE_CONSULTANT.name().equals(applyAgentRespFc2.getAgentTypeCode())) {
                    applyBo.getApplyAgentBo().setRecommendAgentName(applyAgentRespFc2.getAgentName());
                    messageBusinessService.pushApplyMessageSingle(
                            ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_TRANFER_POLICY_RECOMMEND_CP.name(), applyBo, applyAgentRespFc1.getRecommendAgentId());
                }
            }

        }
        System.out.println("===================================发送3==============================================");
        // 发消息给投保人
        messageBusinessService.pushApplyMessageSingleToApplicant(ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_TRANFER_POLICY_APPLICANT_CP.name(), applyBo);

        System.out.println("===================================发送承保后消息客户==============================================");
        // 发消息给投保人
        String businessCode = ApplyTermEnum.MSG_BUSINESS_TYPE.POLICY_UNDERWRITTEN_TO_CUSTOMER.name();
        if (ApplyTermEnum.CHANNEL_TYPE.ONLINE.name().equals(applyBo.getChannelTypeCode())) {
            businessCode = ApplyTermEnum.MSG_BUSINESS_TYPE.POLICY_UNDERWRITTEN_TO_CUSTOMER_ONLINE.name();
            List<ApplyCoveragePo> applyCoveragePos = applyCoverageBaseService.getApplyCoverageList(applyBo.getApplyId());
            if (AssertUtils.isNotEmpty(applyCoveragePos) &&
                    !ProductTermEnum.PREMIUM_PAYMENT_TERM.SINGLE.name().equals(applyDataTransform.transPremiumPaymentTerm(applyCoveragePos.get(0).getPremiumFrequency(), applyCoveragePos.get(0).getPremiumPeriod()))) {
                businessCode = ApplyTermEnum.MSG_BUSINESS_TYPE.POLICY_UNDERWRITTEN_TO_CUSTOMER_ONLINE_MULTI_YEAR_PAYMENT.name();
            }
        }
        messageBusinessService.pushApplyMessageSingleToApplicant(businessCode, applyBo);

        System.out.println("===================================发送4==============================================");
        // 发消息给后台操作人员
        messageBusinessService.pushApplyMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_TRANFER_POLICY_CP.name(), applyBo);

        System.out.println("===================================发送5==============================================");
        messageBusinessService.pushApplyMessageSingle(
                ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_TRANFER_POLICY_CP.name(), applyBo, applyBo.getApplyAgentBo().getAgentId());
    }

    public PolicyVo transferApplyToPolicy(ApplyBo applyBo) {
        log.info("yhj-投保单数据校验");
        //投保单数据校验
        paymentParameterValidate.validParameterApplyToPolicyTransData(applyBo);
        log.info("yhj-数据转换");
        //数据转换
        PolicyVo policyVo = transApplyDataToPolicyData(applyBo, null);
        log.info("yhj-投保单转保单 调用->policyGenerateApi.applyTransToPolicy(policyVo)");
        //投保单转保单
        ResultObject<PolicyVo> resPolicyResult = policyGenerateApi.applyTransToPolicy(policyVo);
        //判断是否转换成功
        AssertUtils.isResultObjectDataNull(this.getLogger(), resPolicyResult, ApplyErrorConfigEnum.APPLY_BUSINESS_SAVE_POLICY_DATA_ERROR);
        log.info("yhj-保存保单号 -> {}", resPolicyResult.getData().getPolicyNo());
        //保存保单号
        applyBo.setPolicyNo(resPolicyResult.getData().getPolicyNo());
        applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_SUCCESS.name());
        applyBoService.saveApplyPo(applyBo);
        //出单体验
        applyExperience(applyBo);

        // 生成投保单PDF附件
        new Thread(() -> {
            ApplyThreadLocalBo.asyncFlag.set(true);// 可以使用消息队列进行异步生成
            attachmentPdfGenerate(applyBo, TerminologyConfigEnum.LANGUAGE.EN_US.name());
            attachmentPdfGenerate(applyBo, TerminologyConfigEnum.LANGUAGE.ZH_CN.name());
            attachmentPdfGenerate(applyBo, TerminologyConfigEnum.LANGUAGE.KM_KH.name());
            ApplyThreadLocalBo.asyncFlag.set(false);
//            ApplyPremiumBo applyPremiumBo = applyBo.getApplyPremiumBo();
//            if (AssertUtils.isNotNull(applyPremiumBo)) {
//                attachmentApplyReceipt(applyPremiumBo.getPaymentId(), TerminologyConfigEnum.LANGUAGE.EN_US.name());
//            }
        }).start();
        log.info("yhj-投保单转保单 执行完毕。");
        return resPolicyResult.getData();
    }

    /**
     * 生成收据
     *
     * @param paymentId
     * @param language
     * @return
     */
    public String attachmentApplyReceipt(String paymentId, String language) {
        if (!AssertUtils.isNotNull(paymentId) || !AssertUtils.isNotNull(language)) {
            return null;
        }
        ResultObject paymentPrintFileInternal = paymentPrintApi.getPaymentPrintFileInternal(paymentId, language);
        AssertUtils.isResultObjectError(this.getLogger(), paymentPrintFileInternal);
        Object data = paymentPrintFileInternal.getData();
        PolicyAttachmentVo policyAttachmentVo = new PolicyAttachmentVo();
        policyAttachmentVo.setPolicyId(paymentId);
        policyAttachmentVo.setAttachmentId(String.valueOf(data));
        policyAttachmentVo.setAttachmentTypeCode(ApplyTermEnum.ATTACHMENT_TYPE_FLAG.APPLY_RECEIPT_BOOK.name());
        policyAttachmentVo.setLanguage(language);
        policyApi.saveAttachment(policyAttachmentVo);
        return String.valueOf(data);

    }

    /**
     * 生成投保单PDF
     *
     * @param applyBo  投保单数据
     * @param language 附件语言
     * @return applyAttachId 投保单附件ID
     */
    public List<AttachmentResponse> attachmentPdfGenerate(ApplyBo applyBo, String language) {
        this.getLogger().info("applyBo1:" + JSON.toJSONString(applyBo));

        List<ApplyInsuredBo> listInsured = applyBo.getListInsured();
        AssertUtils.isNotEmpty(this.getLogger(), listInsured, APPLY_GENERATE_APPLY_RECEIPT_BOOK_ERROR);
        // 取第一个被保人的险种信息 获取主险的产品Id
        String productId = null;
        if (AssertUtils.isNotEmpty(listInsured.get(0).getListCoverage())) {
            Optional<ApplyCoverageBo> applyCoverageBoOptional = listInsured.get(0).getListCoverage().stream().filter(applyCoverageAcceptBo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageAcceptBo.getPrimaryFlag())).findFirst();
            if (applyCoverageBoOptional.isPresent()) {
                productId = applyCoverageBoOptional.get().getProductId();
            }
        }
        AssertUtils.isNotEmpty(this.getLogger(), productId, APPLY_GENERATE_APPLY_RECEIPT_BOOK_ERROR);

        // 调用代理人微服务查询代理人信息
        ResultObject<AgentResponse> resultObject = this.agentApi.agentByIdGet(applyBo.getApplyAgentBo().getAgentId());

        if (!AssertUtils.isResultObjectDataNull(resultObject)) {
            AgentResponse data = resultObject.getData();
            // 代理人姓名
            applyBo.getApplyAgentBo().setAgentName(data.getAgentName());
            applyBo.getApplyAgentBo().setAgentMobile(data.getMobile());
            applyBo.getApplyAgentBo().setAgentCode(data.getAgentCode());
        }

        // 受理机构
        ResultObject<SyscodeRespFc> syscodeRespFcResultObject = platformBaseInternationServiceApi.queryOneInternational(BRANCH_NAME.name(), applyBo.getSalesBranchId(), language);
        if (!AssertUtils.isResultObjectDataNull(syscodeRespFcResultObject)) {
            // 受理机构名称
            applyBo.setAcceptBranchName(syscodeRespFcResultObject.getData().getCodeName());
        }

        //被保人健康告知备注
        ApplyHealthQuestionnaireRemarkPo insuredHealthRemarkPo = applyRemarkBaseService.getApplyHealthQuestionnaireRemark(applyBo.getApplyId(), ApplyTermEnum.CUSTOMER_TYPE.INSURED.name());
        ApplyHealthQuestionnaireRemarkPo applicantHealthRemarkPo = applyRemarkBaseService.getApplyHealthQuestionnaireRemark(applyBo.getApplyId(), ApplyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
        if (AssertUtils.isNotNull(insuredHealthRemarkPo)) {
            applyBo.setInsuredHealthRemark(insuredHealthRemarkPo.getRemark());
        }
        if (AssertUtils.isNotNull(applicantHealthRemarkPo)) {
            applyBo.setApplicantHealthRemark(applicantHealthRemarkPo.getRemark());
        }
        List<String> codeTypes = Arrays.asList(InternationalTypeEnum.HEALTH_NOTICE.name(),// 健康告知
                TerminologyTypeEnum.GENDER.name(),// 性别
                TerminologyTypeEnum.NATIONALITY.name(),// 国籍
                com.gclife.common.TerminologyTypeEnum.NATIONALITY_TYPE.name(),// 外国人国际类型
                TerminologyTypeEnum.RELATIONSHIP_WITH_THE_APPLICANT.name(),// 与投保人的关系
                TerminologyTypeEnum.RELATIONSHIP_WITH_THE_INSURED.name(),// 与被保人的关系
                TerminologyTypeEnum.ID_TYPE.name(),// 证件类型
                TerminologyTypeEnum.INSURANCE_TYPE.name(),// 保险类型
                ANNUAL_EARNED_INCOME.name(),// 收入国际化
                BANK.name(),// 银渠
                TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name());// 缴费终止期间单位
        ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(language, codeTypes);
        Map<String, List<SyscodeResponse>> mapSyscodeList = mapResultObject.getData();
        //查询健康告知
        List<ApplyHealthQuestionnaireAnswerBo> healthQuestionnaireBos = applyExtDao.loadHealthQuestionnaire(applyBo.getApplyId());
        List<SyscodeResponse> healthNoticeSyscodes = mapSyscodeList.get(InternationalTypeEnum.HEALTH_NOTICE.name());
        if (AssertUtils.isNotEmpty(healthQuestionnaireBos)) {
            if (AssertUtils.isNotEmpty(healthNoticeSyscodes)) {
                healthQuestionnaireBos.forEach(productHealthNoticeResponse -> {
                    productHealthNoticeResponse.setQuestionDesc(LanguageUtils.getNewCodeName(healthNoticeSyscodes, productHealthNoticeResponse.getQuestionCode()));
                });
            }
            applyBo.setListHealthNotice(healthQuestionnaireBos);
        }
        // 性别
        List<SyscodeResponse> genderSyscodes = mapSyscodeList.get(TerminologyTypeEnum.GENDER.name());
        // 国籍
        List<SyscodeResponse> nationalitySyscodes = mapSyscodeList.get(TerminologyTypeEnum.NATIONALITY.name());
        // 国籍添加网销国籍
        nationalitySyscodes.addAll(mapSyscodeList.get(com.gclife.common.TerminologyTypeEnum.NATIONALITY_TYPE.name()));
        // 与投保人关系
        List<SyscodeResponse> relationshipWithTheApplicantSyscodes = mapSyscodeList.get(TerminologyTypeEnum.RELATIONSHIP_WITH_THE_APPLICANT.name());
        // 与被保人关系
        List<SyscodeResponse> relationshipWithTheInsuredSyscodes = mapSyscodeList.get(TerminologyTypeEnum.RELATIONSHIP_WITH_THE_INSURED.name());
        // 证件类型
        List<SyscodeResponse> idTypeSyscodes = mapSyscodeList.get(TerminologyTypeEnum.ID_TYPE.name());
        // 缴费期限类型
        List<SyscodeResponse> premiumPeriodUnitSyscodes = mapSyscodeList.get(TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name());
        List<SyscodeResponse> coveragePeriodUnitSyscodes = mapSyscodeList.get(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name());
        List<SyscodeResponse> insuranceTypeSyscodes = mapSyscodeList.get(TerminologyTypeEnum.INSURANCE_TYPE.name());
        List<SyscodeResponse> bankSyscodes = mapSyscodeList.get(BANK.name());
        List<SyscodeResponse> annualEarnedIncomeSyscodes = mapSyscodeList.get(ANNUAL_EARNED_INCOME.name());
        // 投保人
        if (AssertUtils.isNotNull(applyBo.getApplicant())) {
            applyBo.getApplicant().setIncomeName(LanguageUtils.getNewCodeName(annualEarnedIncomeSyscodes, applyBo.getApplicant().getIncome()));
            // 国籍
            if (AssertUtils.isNotEmpty(nationalitySyscodes) && AssertUtils.isNotEmpty(applyBo.getApplicant().getNationality())) {
                nationalitySyscodes.stream()
                        .filter(syscode -> syscode.getCodeKey().equals(applyBo.getApplicant().getNationality()))
                        .findFirst().ifPresent(syscode -> applyBo.getApplicant().setNationalityName(syscode.getCodeName()));
            }
            // 证件类型
            if (AssertUtils.isNotEmpty(idTypeSyscodes) && AssertUtils.isNotEmpty(applyBo.getApplicant().getIdType())) {
                applyBo.getApplicant().setIdTypeName(LanguageUtils.getNewCodeName(idTypeSyscodes, applyBo.getApplicant().getIdType()));
            }
            // 地址拼接
            if (AssertUtils.isNotEmpty(applyBo.getApplicant().getHomeAreaCode())) {
                ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(applyBo.getApplicant().getHomeAreaCode(), language);
                if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                    applyBo.getApplicant().setFullAddress(respFcResultObject.getData().getAreaName() + " " + applyBo.getApplicant().getHomeAddress());
                }
            }
            // 地址拼接
            if (AssertUtils.isNotEmpty(applyBo.getApplicant().getCompanyAreaCode())) {
                ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(applyBo.getApplicant().getCompanyAreaCode(), language);
                if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                    applyBo.getApplicant().setCompanyAreaName(respFcResultObject.getData().getAreaName());
                }
            }
            // 地址拼接
            if (AssertUtils.isNotEmpty(applyBo.getApplicant().getDoctorAreaCode())) {
                ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(applyBo.getApplicant().getDoctorAreaCode(), language);
                if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                    applyBo.getApplicant().setDoctorAreaCodeName(respFcResultObject.getData().getAreaName());
                }
            }
            // 职业
            if (AssertUtils.isNotEmpty(applyBo.getApplicant().getOccupationCode())) {
                ResultObject<CareerResponse> careerResponseResultObject = platformCareerApi.careerInfoGet(applyBo.getApplicant().getOccupationCode(), language);
                if (!AssertUtils.isResultObjectDataNull(careerResponseResultObject)) {
                    applyBo.getApplicant().setOccupationName(careerResponseResultObject.getData().getCareerName());
                }
            }
        }

        // 被保人
        if (AssertUtils.isNotEmpty(listInsured)) {
            listInsured.forEach(applyInsuredBo -> {
                applyInsuredBo.setIncomeName(LanguageUtils.getNewCodeName(annualEarnedIncomeSyscodes, applyInsuredBo.getIncome()));
                // 地址拼接
                if (AssertUtils.isNotEmpty(applyInsuredBo.getHomeAreaCode())) {
                    ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(applyInsuredBo.getHomeAreaCode(), language);
                    if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                        applyInsuredBo.setFullAddress(respFcResultObject.getData().getAreaName() + " " + applyInsuredBo.getHomeAddress());
                    }
                }
                // 地址拼接
                if (AssertUtils.isNotEmpty(applyInsuredBo.getCompanyAreaCode())) {
                    ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(applyInsuredBo.getCompanyAreaCode(), language);
                    if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                        applyInsuredBo.setCompanyAreaName(respFcResultObject.getData().getAreaName());
                    }
                }
                // 地址拼接
                if (AssertUtils.isNotEmpty(applyInsuredBo.getDoctorAreaCode())) {
                    ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(applyInsuredBo.getDoctorAreaCode(), language);
                    if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                        applyInsuredBo.setDoctorAreaCodeName(respFcResultObject.getData().getAreaName());
                    }
                }
                // 职业
                if (AssertUtils.isNotEmpty(applyInsuredBo.getOccupationCode())) {
                    ResultObject<CareerResponse> careerResponseResultObject = platformCareerApi.careerInfoGet(applyInsuredBo.getOccupationCode(), language);
                    if (!AssertUtils.isResultObjectDataNull(careerResponseResultObject)) {
                        applyInsuredBo.setOccupationName(careerResponseResultObject.getData().getCareerName());
                    }
                }
                // 国籍
                applyInsuredBo.setNationalityName(LanguageUtils.getNewCodeName(nationalitySyscodes, applyInsuredBo.getNationality()));
                // 证件类型
                applyInsuredBo.setIdTypeName(LanguageUtils.getNewCodeName(idTypeSyscodes, applyInsuredBo.getIdType()));
                // 与投保人关系

                applyInsuredBo.setRelationshipName(LanguageUtils.getNewCodeName(relationshipWithTheApplicantSyscodes, applyInsuredBo.getRelationship()));
                // 受益人信息
                if (AssertUtils.isNotEmpty(applyInsuredBo.getListBeneficiary())) {
                    applyInsuredBo.getListBeneficiary().forEach(applyBeneficiaryInfoBo -> {
                        // 与被保人关系
                        applyBeneficiaryInfoBo.setRelationshipName(LanguageUtils.getNewCodeName(relationshipWithTheInsuredSyscodes, applyBeneficiaryInfoBo.getRelationship()));
                        if (AssertUtils.isNotNull(applyBeneficiaryInfoBo.getApplyBeneficiaryBo())) {
                            // 证件类型
                            applyBeneficiaryInfoBo.getApplyBeneficiaryBo().setIdTypeName(LanguageUtils.getNewCodeName(idTypeSyscodes, applyBeneficiaryInfoBo.getApplyBeneficiaryBo().getIdType()));
                            // 性别
                            applyBeneficiaryInfoBo.getApplyBeneficiaryBo().setSexName(LanguageUtils.getNewCodeName(genderSyscodes, applyBeneficiaryInfoBo.getApplyBeneficiaryBo().getSex()));
                        }
                    });
                }
                // 险种信息
                if (AssertUtils.isNotEmpty(applyInsuredBo.getListCoverage())) {
                    applyInsuredBo.getListCoverage().forEach(applyCoverageBo -> {
                        // 缴费期限类型
                        applyCoverageBo.setPremiumPeriodUnitName(LanguageUtils.getNewCodeName(premiumPeriodUnitSyscodes, applyCoverageBo.getPremiumPeriodUnit()));
                        applyCoverageBo.setCoveragePeriodUnitName(LanguageUtils.getNewCodeName(coveragePeriodUnitSyscodes, applyCoverageBo.getCoveragePeriodUnit()));
                    });
                }
            });

        }
        List<ApplyOtherInsuranceBo> otherInsurance = applyBo.getOtherInsurance();
        if (AssertUtils.isNotEmpty(otherInsurance)) {
            otherInsurance.forEach(applyOtherInsuranceBo -> {
                applyOtherInsuranceBo.setInsuranceTypeName(LanguageUtils.getNewCodeName(insuranceTypeSyscodes, applyOtherInsuranceBo.getInsuranceType()));
            });
        }
        List<ApplyAccountBo> listApplyAccount = applyBo.getListApplyAccount();
        if (AssertUtils.isNotEmpty(listApplyAccount)) {
            listApplyAccount.forEach(applyAccountBo -> {
                applyAccountBo.setBankName(LanguageUtils.getNewCodeName(bankSyscodes, applyAccountBo.getBankCode()));
            });
        }
        //如果存在折扣，则将折扣后的首期保费改成折后之前的保费
        //因不用语言打印是引用到同一个applyBo对象，若更改此保费会影响后续语言的保费值，故暂存此保费，数据到打印之后再改回来
        BigDecimal originReceivablePremium = applyBo.getReceivablePremium();
        ApplyPremiumBo applyPremiumBo = applyBo.getApplyPremiumBo();
        if (AssertUtils.isNotNull(applyPremiumBo.getSpecialDiscount())
                && AssertUtils.isNotNull(applyPremiumBo.getPremiumBeforeDiscount())
                && AssertUtils.isNotEmpty(applyPremiumBo.getDiscountType())
                && AssertUtils.isNotEmpty(applyPremiumBo.getDiscountModel())
        ) {
            BigDecimal premiumBeforeDiscount = originReceivablePremium;
            if (!AssertUtils.isNotEmpty(applyPremiumBo.getDiscountModel()) || ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(applyPremiumBo.getDiscountModel())) {
                premiumBeforeDiscount = premiumBeforeDiscount.divide(new BigDecimal("1").subtract(applyPremiumBo.getSpecialDiscount()), 2, BigDecimal.ROUND_HALF_UP);
            } else if (ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(applyPremiumBo.getDiscountModel())) {
                premiumBeforeDiscount = premiumBeforeDiscount.add(applyPremiumBo.getSpecialDiscount());
            }
            applyBo.setReceivablePremium(premiumBeforeDiscount);
        }

        // 电子签名
        ElectronicSignatureAttachmentBo electronicSignatureAttachmentBo = new ElectronicSignatureAttachmentBo();
        List<ApplyAttachmentPo> applyAttachmentPos = applyAttachmentBaseService.listApplyAttachment(applyBo.getApplyId());
        getLogger().info("applyBo.getApplyId():{}, applyAttachmentPos:{}", applyBo.getApplyId(), applyAttachmentPos);
        applyAttachmentPos.forEach(applyAttachmentPo -> {
            String attachmentId = applyAttachmentPo.getDescription();
            String attachmentTypeCode = applyAttachmentPo.getAttachmentTypeCode();
            if (ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_AGENT.name().equals(attachmentTypeCode)) {
                electronicSignatureAttachmentBo.setAgentSignatureAttachmentId(attachmentId);
            }
            if (ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_APPLICANT.name().equals(attachmentTypeCode)) {
                electronicSignatureAttachmentBo.setApplicantSignatureAttachmentId(attachmentId);
            }
            if (ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_INSURED.name().equals(attachmentTypeCode)) {
                electronicSignatureAttachmentBo.setInsuredSignatureAttachmentId(attachmentId);
            }
            // 投保人与被保人是同一人 则被保人签名取投保人的
            if (AssertUtils.isNotEmpty(listInsured)) {
                ApplyInsuredBo applyInsuredBo = listInsured.get(0);
                if (AssertUtils.isNotNull(applyInsuredBo)) {
                    if (ApplyTermEnum.RELATIONSHIP_WITH_THE_INSURED.ONESELF.name().equals(applyInsuredBo.getRelationship())
                            && ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_APPLICANT.name().equals(attachmentTypeCode)) {
                        electronicSignatureAttachmentBo.setInsuredSignatureAttachmentId(attachmentId);
                    }
                }
            }
        });
        applyBo.setElectronicSignatureAttachmentBo(electronicSignatureAttachmentBo);
        ApplyHolderBo applyHolderBo = applyBo.getHolder();
        if (AssertUtils.isNotNull(applyHolderBo)) {
            applyHolderBo.setRelationshipName(LanguageUtils.getNewCodeName(relationshipWithTheApplicantSyscodes, applyHolderBo.getRelationship()));
            applyHolderBo.setSexName(LanguageUtils.getNewCodeName(genderSyscodes, applyHolderBo.getSex()));
            applyHolderBo.setIdTypeName(LanguageUtils.getNewCodeName(idTypeSyscodes, applyHolderBo.getIdType()));
        }

        this.getLogger().info("applyBo2:" + JSON.toJSONString(applyBo));

        ElectronicPolicyGeneratorRequest applyPlanGeneratorReqFc = new ElectronicPolicyGeneratorRequest();
        applyPlanGeneratorReqFc.setPdfType(ApplyTermEnum.PDF_TYPE.APPLY.name());
        applyPlanGeneratorReqFc.setProductId(productId);
        applyPlanGeneratorReqFc.setLanguage(language);
        applyPlanGeneratorReqFc.setContent(JackSonUtils.toJson(applyBo, language));
        applyPlanGeneratorReqFc.setAsyncFlag(ApplyThreadLocalBo.asyncFlag.get());
        applyPlanGeneratorReqFc.setConvertImageFlag(ApplyThreadLocalBo.convertImageFlag.get());
        this.getLogger().info("applyPlanGeneratorReqFc:" + JSON.toJSONString(applyPlanGeneratorReqFc));
        // 生成PDF
        ResultObject<List<AttachmentResponse>> attachmentRespFcObject = pdfDocumentApi.electronicPolicyGenerator(applyPlanGeneratorReqFc);
        if (AssertUtils.isResultObjectDataNull(attachmentRespFcObject)) {
            return null;
        }
        List<AttachmentResponse> attachmentRespFcList = attachmentRespFcObject.getData();
        //保存保单附件
        attachmentRespFcList.forEach(attachmentRespFc -> {
            PolicyAttachmentVo policyAttachmentVo = new PolicyAttachmentVo();
            policyAttachmentVo.setPolicyId(applyBo.getApplyId());
            policyAttachmentVo.setAttachmentId(attachmentRespFc.getMediaId());
            policyAttachmentVo.setAttachmentTypeCode(attachmentRespFc.getTemplateType());
            policyAttachmentVo.setLanguage(language);
            policyApi.saveAttachment(policyAttachmentVo);
            attachmentRespFc.setApplyAttachmentPdfId(attachmentRespFc.getMediaId());
        });
        getLogger().info("originReceivablePremium:{},language:{}", originReceivablePremium, language);
        applyBo.setReceivablePremium(originReceivablePremium);
        return attachmentRespFcList;

    }

    private void applyExperience(ApplyBo applyBo) {
        if (AssertUtils.isNotNull(applyBo.getApplyAgentBo().getAgentId())) {
            //查询代理人
            ResultObject<AgentResponse> respFcResultObject = agentApi.agentByIdGet(applyBo.getApplyAgentBo().getAgentId());
            AssertUtils.isResultObjectDataNull(getLogger(), respFcResultObject, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
            List<ApplyPo> applyPoList = applyExtDao.loadApplyPoByAgentId(applyBo.getApplyAgentBo().getAgentId());
            if (AssertUtils.isNotEmpty(applyPoList) && applyPoList.size() == 1) {
                //首单
                ResultObject resultObject = integralTaskApi.updateTasksStatus(ApplyTermEnum.INTEGRAL_TASK_CODE.TASK_OUT_POLICY.name(), respFcResultObject.getData().getUserId());
//                ResultObject resultObject = appServiceInterface.tasksCompletePut(ApplyTermEnum.INTEGRAL_TASK_CODE.TASK_OUT_POLICY.name(), respFcResultObject.getData().getUserId());
                AssertUtils.isResultObjectError(getLogger(), resultObject, ApplyErrorConfigEnum.APPLY_ORDER_TASK_FINISH_ERROR);
            }
        }
    }

    /***
     * 投保单转保单请求数据转换
     * @param applyBo　投保单业务对象
     * @return PolicyVo
     */
    public PolicyVo transApplyDataToPolicyData(ApplyBo applyBo, String actualPayDate) {
        //投保单数据校验
        paymentParameterValidate.validParameterApplyToPolicyTransData(applyBo);
        //初始化保单对象
        PolicyVo policyVo = new PolicyVo();
        //转换保单基础信息
        transferBasePolicyData(applyBo, policyVo, actualPayDate);
        //转换保单账户
        transferPolicyAccountData(applyBo, policyVo);
        //代理人
        transferPolicyAgentData(applyBo, policyVo);
        //投保人
        transferPolicyApplicantData(applyBo, policyVo);
        //保单联系方式
        transferPolicyContactData(applyBo, policyVo);
        //保单附件
        transferPolicyAttachment(applyBo, policyVo);
        //保单付费人/保单缴费
        transferPolicyPremiumPay(applyBo, policyVo);
        // 特别约定
        transferPolicySpecialContract(applyBo, policyVo);
        //保单打印
        transferPolicyPrintData(policyVo);
        //被保人
        transferPolicyInsuredData(applyBo, policyVo);
        //保单加费
        transferPolicyAddPremium(applyBo, policyVo);
        //转换保单支付记录()
        transferPolicyPaymentData(applyBo, policyVo, actualPayDate);
        //调用产品计算费率
        transferPolicyPaymentRateData(policyVo);
        //转换对账记录
        transferPolicyPremiumPersistData(policyVo);
        //贷款合同信息
        transferPolicyLoanData(applyBo, policyVo);
        //职业性质
        transferPolicyOccupationNature(applyBo, policyVo);
        //其他保险信息
        transferPolicyOtherInsurance(applyBo, policyVo);
        //投保单声明
        transferPolicyStatement(applyBo, policyVo);
        //推荐信息
        transferPolicyReferralInfoData(applyBo, policyVo);
        //保单持有人
        transferPolicyHolderData(applyBo, policyVo);
        return policyVo;
    }

    private void transferPolicyHolderData(ApplyBo applyBo, PolicyVo policyVo) {
        if (!AssertUtils.isNotNull(applyBo.getHolder())) {
            return;
        }
        PolicyHolderVo policyHolderVo = new PolicyHolderVo();
        ClazzUtils.copyPropertiesIgnoreNull(applyBo.getHolder(), policyHolderVo);
        policyVo.setHolder(policyHolderVo);
    }

    private void transferPolicyReferralInfoData(ApplyBo applyBo, PolicyVo policyVo) {
        if (!AssertUtils.isNotNull(applyBo.getReferralInfo())) {
            return;
        }
        PolicyReferralInfoVo policyReferralInfoVo = new PolicyReferralInfoVo();
        ClazzUtils.copyPropertiesIgnoreNull(applyBo.getReferralInfo(), policyReferralInfoVo);
        policyVo.setReferralInfo(policyReferralInfoVo);
    }

    private void transferPolicyAddPremium(ApplyBo applyBo, PolicyVo policyVo) {
        if (AssertUtils.isNotEmpty(applyBo.getListApplyAddPremiumPo())) {
            List<PolicyAddPremiumVo> policyAddPremiumVos = (List<PolicyAddPremiumVo>) this.converterList(
                    applyBo.getListApplyAddPremiumPo(), new TypeToken<List<PolicyAddPremiumVo>>() {
                    }.getType()
            );
            policyVo.setListPolicyAddPremium(policyAddPremiumVos);
        }
    }

    private void transferPolicyStatement(ApplyBo applyBo, PolicyVo policyVo) {
        if (!AssertUtils.isNotEmpty(applyBo.getStatements())) {
            return;
        }
        List<PolicyStatementVo> policyStatementVos = (List<PolicyStatementVo>) this.converterList(
                applyBo.getStatements(), new TypeToken<List<PolicyStatementVo>>() {
                }.getType()
        );
        policyVo.setStatements(policyStatementVos);
    }

    private void transferPolicyOtherInsurance(ApplyBo applyBo, PolicyVo policyVo) {
        if (!AssertUtils.isNotEmpty(applyBo.getOtherInsurance())) {
            return;
        }
        List<PolicyOtherInsuranceVo> policyOtherInsuranceVos = (List<PolicyOtherInsuranceVo>) this.converterList(
                applyBo.getOtherInsurance(), new TypeToken<List<PolicyOtherInsuranceVo>>() {
                }.getType()
        );
        policyVo.setOtherInsurance(policyOtherInsuranceVos);
    }

    private void transferPolicyOccupationNature(ApplyBo applyBo, PolicyVo policyVo) {
        if (!AssertUtils.isNotEmpty(applyBo.getOccupationNature())) {
            return;
        }
        List<PolicyOccupationNatureVo> policyOccupationNatureVos = (List<PolicyOccupationNatureVo>) this.converterList(
                applyBo.getOccupationNature(), new TypeToken<List<PolicyOccupationNatureVo>>() {
                }.getType()
        );
        policyVo.setOccupationNature(policyOccupationNatureVos);
    }

    private void transferPolicyLoanData(ApplyBo applyBo, PolicyVo policyVo) {
        if (!AssertUtils.isNotNull(applyBo.getLoanContract())) {
            return;
        }
        PolicyLoanVo loanContract = new PolicyLoanVo();
        ClazzUtils.copyPropertiesIgnoreNull(applyBo.getLoanContract(), loanContract);
        policyVo.setLoanContract(loanContract);
    }

    /**
     * 转换保单特别约定信息
     *
     * @param applyBo     　投保单号
     * @param policyReqFc 　保单请求对象
     */
    private void transferPolicySpecialContract(ApplyBo applyBo, PolicyVo policyReqFc) {
        if (AssertUtils.isNotEmpty(applyBo.getListPolicySpecialContract())) {
            List<PolicySpecialContractVo> policySpecialContractReqFcs = (List<PolicySpecialContractVo>) this.converterList(
                    applyBo.getListPolicySpecialContract(), new TypeToken<List<PolicySpecialContractVo>>() {
                    }.getType()
            );
            policyReqFc.setListPolicySpecialContract(policySpecialContractReqFcs);
        }
    }


    /**
     * 转换被保人信息
     *
     * @param applyBo     　投保人业务对象
     * @param policyReqFc 　保单对象
     */
    private void transferPolicyInsuredData(ApplyBo applyBo, PolicyVo policyReqFc) {
        this.getLogger().info("投保单转保单开始--transferPolicyInsuredData--start:[{}]", DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        //获取投保单被保人，支付对象
        List<ApplyInsuredBo> listApplyInsuredBo = applyBo.getListInsured();
        //初始化被保人
        List<PolicyInsuredVo> listPolicyInsuredReqFc = new ArrayList<>();

        listApplyInsuredBo.forEach(applyInsuredBo -> {
            //被保人基础信息
            PolicyInsuredVo policyInsuredVo = (PolicyInsuredVo) this.converterObject(applyInsuredBo, PolicyInsuredVo.class);
            policyInsuredVo.setInsuredId(null);
            //受益人，险种信息
            List<ApplyBeneficiaryInfoBo> listApplyBeneficiaryInfoBo = applyInsuredBo.getListBeneficiary();
            //转换受益人信息
            transferPolicyBeneficiaryData(policyInsuredVo, listApplyBeneficiaryInfoBo);
            //转换险种信息
            transferPolicyCoverageData(applyBo, policyInsuredVo, applyInsuredBo, policyReqFc.getPolicyPremium(), policyReqFc);

            listPolicyInsuredReqFc.add(policyInsuredVo);
        });
        this.getLogger().info("投保单转保单开始--transferPolicyInsuredData--start:[{}]", DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        policyReqFc.setListPolicyInsured(listPolicyInsuredReqFc);
    }

    /**
     * 　保单险种信息转换
     *
     * @param applyBo         　投保单业务对象
     * @param applyInsuredBo  　投保单被保人对象
     * @param policyInsuredVo 保单被保人对象
     */
    private void transferPolicyCoverageData(ApplyBo applyBo, PolicyInsuredVo policyInsuredVo, ApplyInsuredBo
            applyInsuredBo, PolicyPremiumVo policyPremiumVo, PolicyVo policyVo) {
        List<PolicyCoverageVo> policyCoverageVos = new ArrayList<>();
        ApplyPremiumBo applyPremiumBo = applyBo.getApplyPremiumBo();
        List<ApplyCoverageBo> listApplyCoverageBo = applyInsuredBo.getListCoverage();
        //险种循环
        listApplyCoverageBo.forEach(applyCoverageBo -> {
            PolicyCoverageVo policyCoverageVo = new PolicyCoverageVo();
            //转换险种基本信息
            transferBasePolicyCoverageData(applyCoverageBo, policyCoverageVo, policyVo, applyPremiumBo);
            //险种红利
            transferPolicyCoverageBonusData(applyCoverageBo, policyCoverageVo);
            //险种缴费
            transferPolicyCoveragePremiumData(applyBo, applyInsuredBo, applyPremiumBo, applyCoverageBo, policyCoverageVo, policyPremiumVo, policyVo);
            //险种责任
            transferPolicyCoverageDuty(applyCoverageBo, policyCoverageVo);
            //添加到集合
            policyCoverageVos.add(policyCoverageVo);
        });
        //设置险种
        policyInsuredVo.setListPolicyCoverage(policyCoverageVos);
    }

    /**
     * 转换险种责任
     *
     * @param applyCoverageBo  　　投保单险种对象
     * @param policyCoverageVo 　保单险种对象
     */
    private void transferPolicyCoverageDuty(ApplyCoverageBo applyCoverageBo, PolicyCoverageVo policyCoverageVo) {
        ResultObject<List<DutyResponse>> listProductDutyGetRespFc = productDutyApi.queryDuty(applyCoverageBo.getProductId());
        if (!AssertUtils.isResultObjectListDataNull(listProductDutyGetRespFc)) {
            //生存给付责任
            List<PolicyCoverageSurvivalVo> policyCoverageSurvivalVos = new ArrayList<>();
            //责任
            List<PolicyCoverageDutyVo> policyCoverageDutyVos = new ArrayList<>();
            listProductDutyGetRespFc.getData().forEach(dutyResponse -> {
                //生存给付责任特殊处理
                applyDataTransform.transferPolicySurvivalDuty(applyCoverageBo, policyCoverageSurvivalVos, dutyResponse);
                //责任处理
                this.transferPolicyDuty(policyCoverageDutyVos, dutyResponse);
            });
            policyCoverageVo.setListPolicyCoverageSurvival(policyCoverageSurvivalVos);
            policyCoverageVo.setListPolicyCoverageDuty(policyCoverageDutyVos);

        }
    }

    /**
     * 责任转换
     *
     * @param listPolicyCoverageDuty 　保单责任列表
     * @param dutyResponse           　产品责任
     */
    private void transferPolicyDuty(List<PolicyCoverageDutyVo> listPolicyCoverageDuty, DutyResponse dutyResponse) {
        PolicyCoverageDutyVo policyCoverageDuty = new PolicyCoverageDutyVo();
        // TODO 责任数据待完善
        policyCoverageDuty.setDutyId(dutyResponse.getDutyId());
        policyCoverageDuty.setDutyName(dutyResponse.getDutyName());
        listPolicyCoverageDuty.add(policyCoverageDuty);
    }

    /**
     * 险种红利信息
     *
     * @param applyCoverageBo  　投保单险种信息
     * @param policyCoverageVo 　保单险种对象
     */
    private void transferPolicyCoverageBonusData(ApplyCoverageBo applyCoverageBo, PolicyCoverageVo policyCoverageVo) {
        PolicyCoverageBonusVo policyCoverageBonusVo = new PolicyCoverageBonusVo();
        policyCoverageBonusVo.setBonusndSelection(applyCoverageBo.getDividendReceiveMode());
        policyCoverageVo.setPolicyCoverageBonus(policyCoverageBonusVo);
    }

    /**
     * 转换保单缴费信息
     *
     * @param applyBo          　投保单对象
     * @param applyInsuredBo   　投保单被保人对象
     * @param applyPremiumBo   　投保单缴费对象
     * @param applyCoverageBo  　投保单险种对象
     * @param policyCoverageVo 　保单险种对象
     */
    private void transferPolicyCoveragePremiumData(ApplyBo applyBo, ApplyInsuredBo applyInsuredBo, ApplyPremiumBo
            applyPremiumBo, ApplyCoverageBo applyCoverageBo, PolicyCoverageVo policyCoverageVo, PolicyPremiumVo
                                                           policyPremiumVo, PolicyVo policyReqFc) {
        PolicyCoveragePremiumVo policyCoveragePremium = new PolicyCoveragePremiumVo();
        ClazzUtils.copyPropertiesIgnoreNull(applyCoverageBo, policyCoveragePremium);
        policyCoveragePremium.setAddPremiumTerm(applyCoverageBo.getAddPremiumPeriod());
        policyCoveragePremium.setFrequency(1L);
        if (AssertUtils.isNotNull(applyPremiumBo)) {
            policyCoveragePremium.setPayStatus(applyPremiumBo.getPremiumStatus());
        }
        if (AssertUtils.isNotNull(applyInsuredBo.getBirthday())) {
            policyCoveragePremium.setPayToDate(applyDataTransform.getPayToDate(applyInsuredBo.getBirthday(), applyCoverageBo.getPremiumPeriod(), applyCoverageBo.getPremiumPeriodUnit(), applyCoverageBo.getPremiumFrequency(), policyReqFc.getApproveDate()));
            policyCoveragePremium.setPaymentCompleteDate(policyCoveragePremium.getPayToDate());
        }
        policyCoveragePremium.setPeriodOccuAddPremium(applyCoverageBo.getPeriodCareerAddPremium());
        policyCoveragePremium.setPeriodStandardPremium(applyCoverageBo.getPeriodStandardPremium());
        //新增保费字段
        policyCoveragePremium.setPeriodTotalPremium(applyCoverageBo.getTotalPremium());
        policyCoveragePremium.setPeriodOriginalPremium(applyCoverageBo.getOriginalPremium());
        policyCoveragePremium.setTotalActualPremium(applyCoverageBo.getActualPremium());
        policyCoveragePremium.setActualPremium(applyCoverageBo.getActualPremium());

        policyCoveragePremium.setTotalOriginalPremium(applyCoverageBo.getOriginalPremium());

        policyCoveragePremium.setRefundAmount(BigDecimal.ZERO);

        policyCoveragePremium.setPremiumPeriodType(applyCoverageBo.getPremiumPeriodUnit());
        policyCoveragePremium.setPremiumPeriodUnit(applyCoverageBo.getPremiumPeriodUnit());
        policyCoveragePremium.setCurrencyCode(applyBo.getCurrencyCode());
        // 设置应缴日期
        policyCoveragePremium.setReceivableDate(applyPremiumBo.getReceivableDate());
        if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
            policyPremiumVo.setPaymentCompleteDate(policyCoveragePremium.getPaymentCompleteDate());
            policyPremiumVo.setCurrencyCode(applyBo.getCurrencyCode());
        }
        //主险产品支持月交三个月保费特殊处理
        List<String> monthlyTripleProductId = Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_9.id(), ProductTermEnum.PRODUCT.PRODUCT_13.id());
        Optional<ApplyCoverageBo> any = applyInsuredBo.getListCoverage().stream().filter(applyCoverageBo1 -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo1.getPrimaryFlag()) &&
                ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.MONTH.name().equals(applyCoverageBo1.getPremiumFrequency()) &&
                monthlyTripleProductId.contains(applyCoverageBo1.getProductId())).findAny();
        boolean isProductMonthlyTriple = any.isPresent();
        //险种支付记录
        transferPolicyCoveragePaymentData(applyBo, applyCoverageBo, policyCoveragePremium, isProductMonthlyTriple);
        //设置保单缴费
        policyCoverageVo.setPolicyCoveragePremium(policyCoveragePremium);
    }

    /**
     * 转换保单支付记录信息
     *
     * @param applyCoverageBo        　投保单险种对象
     * @param isProductMonthlyTriple
     */
    private void transferPolicyCoveragePaymentData(ApplyBo applyBo,
                                                   ApplyCoverageBo applyCoverageBo,
                                                   PolicyCoveragePremiumVo policyCoveragePremiumVo, boolean isProductMonthlyTriple) {
        PolicyCoveragePaymentVo policyCoveragePaymentVo = new PolicyCoveragePaymentVo();
        ClazzUtils.copyPropertiesIgnoreNull(applyCoverageBo, policyCoveragePaymentVo);
        //新增保费字段
        policyCoveragePaymentVo.setPeriodOriginalPremium(applyCoverageBo.getOriginalPremium());
        boolean b = applyCoverageBo.getPaymentInstallments() > 1;
        policyCoveragePaymentVo.setPeriodActualPremium((isProductMonthlyTriple || b) ? applyCoverageBo.getOriginalPremium() : applyCoverageBo.getActualPremium());
        policyCoveragePaymentVo.setActualPremium(isProductMonthlyTriple || b ? applyCoverageBo.getOriginalPremium() : applyCoverageBo.getActualPremium());

        policyCoveragePaymentVo.setPeriodTotalPremium(applyCoverageBo.getOriginalPremium());
        policyCoveragePaymentVo.setTotalOriginalPremium(applyCoverageBo.getOriginalPremium());
        policyCoveragePaymentVo.setTotalPremium(applyCoverageBo.getTotalPremium());
        policyCoveragePaymentVo.setBasePremium(applyCoverageBo.getBasePremium());
        policyCoveragePaymentVo.setPaymentCompleteDate(policyCoveragePremiumVo.getPaymentCompleteDate());
        policyCoveragePaymentVo.setPeriodStandardPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setStandardPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setPeriodStandardRate(new BigDecimal(0));
        policyCoveragePaymentVo.setValuePremium(new BigDecimal(0));
        policyCoveragePaymentVo.setValuePremiumRate(new BigDecimal(0));
        policyCoveragePaymentVo.setAnnOccuAddPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setAnnStandardPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setAnnTotalPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setAnnWeakAddPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setGovStandardPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setGovStandardRate(new BigDecimal(0));
        policyCoveragePaymentVo.setPeriodOccuAddPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setCareerAddPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setPeriodWeakAddPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setWeakAddPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setAgencyFee(new BigDecimal(0));
        policyCoveragePaymentVo.setAgencyFeeRate(new BigDecimal(0));
        policyCoveragePaymentVo.setCommissionFee(new BigDecimal(0));
        policyCoveragePaymentVo.setCommissionFeeRate(new BigDecimal(0));
        policyCoveragePaymentVo.setBasePremium(applyCoverageBo.getTotalPremium());
        policyCoveragePaymentVo.setExtraPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setAddPremiumTerm(new BigDecimal(0));
        policyCoveragePaymentVo.setAddPremiumStartDate(0L);
        policyCoveragePaymentVo.setFrequency(1L);
        policyCoveragePaymentVo.setCurrencyCode(applyBo.getCurrencyCode());
        // 设置加费金额
        List<ApplyAddPremiumPo> applyAddPremiumPos = applyBo.getListApplyAddPremiumPo();
        if (AssertUtils.isNotEmpty(applyAddPremiumPos)) {
            applyAddPremiumPos.forEach(applyAddPremiumPo -> {
                if (applyCoverageBo.getCoverageId().equals(applyAddPremiumPo.getCoverageId())) {
                    if (AssertUtils.isNotNull(applyAddPremiumPo.getPeriodCareerAddPremium())) {
                        policyCoveragePaymentVo.setCareerAddPremium(policyCoveragePaymentVo.getCareerAddPremium().add(applyAddPremiumPo.getPeriodCareerAddPremium()));
                    }
                    if (AssertUtils.isNotNull(applyAddPremiumPo.getPeriodWeakAddPremium())) {
                        policyCoveragePaymentVo.setWeakAddPremium(policyCoveragePaymentVo.getWeakAddPremium().add(applyAddPremiumPo.getPeriodWeakAddPremium()));
                    }
                    if (AssertUtils.isNotNull(applyAddPremiumPo.getPeriodOtherAddPremium())) {
                        policyCoveragePaymentVo.setOtherAddPremium(policyCoveragePaymentVo.getOtherAddPremium().add(applyAddPremiumPo.getPeriodOtherAddPremium()));
                    }
                    if (AssertUtils.isNotNull(applyAddPremiumPo.getTotalAddPremium())) {
                        policyCoveragePaymentVo.setTotalAddPremium(policyCoveragePaymentVo.getTotalAddPremium().add(applyAddPremiumPo.getTotalAddPremium()));
                    }
                }
            });
        }
        policyCoveragePremiumVo.setPolicyCoveragePayment(policyCoveragePaymentVo);
    }

    /**
     * 转换险种基本信息
     *
     * @param applyCoverageBo
     * @param policyCoverageVo
     * @param policyReqFc      　保费
     * @param applyPremiumBo
     */
    private void transferBasePolicyCoverageData(ApplyCoverageBo
                                                        applyCoverageBo, PolicyCoverageVo policyCoverageVo, PolicyVo policyReqFc, ApplyPremiumBo applyPremiumBo) {
        ClazzUtils.copyPropertiesIgnoreNull(applyCoverageBo, policyCoverageVo);
        policyCoverageVo.setCoverageId(null);
        if (AssertUtils.isNotNull(applyCoverageBo.getDividendAmount())) {
            //单份保额
            policyCoverageVo.setDividendAmount(new BigDecimal(applyCoverageBo.getDividendAmount()).divide(new BigDecimal(applyCoverageBo.getMult())));
        }

        if (AssertUtils.isNotEmpty(applyCoverageBo.getAmount())) {
            // 单份保额
            policyCoverageVo.setAmount(new BigDecimal(applyCoverageBo.getAmount()));
            // 总保额
            policyCoverageVo.setTotalAmount(new BigDecimal(applyCoverageBo.getAmount()).multiply(new BigDecimal(applyCoverageBo.getMult())) + "");
        }
        policyCoverageVo.setBaseSumAmount(applyCoverageBo.getAmount());
        policyCoverageVo.setBonusSumAmount(applyCoverageBo.getDividendAmount());
        policyCoverageVo.setTotalDividendAmount(applyCoverageBo.getDividendAmount());
        policyCoverageVo.setEffectiveDate(policyReqFc.getApproveDate());
        if (AssertUtils.isNotNull(applyCoverageBo.getCoveragePeriodEndDate())) {
            policyCoverageVo.setMaturityDate(applyCoverageBo.getCoveragePeriodEndDate());
        }
        //反算折扣前的实缴保费
        if (AssertUtils.isNotNull(applyPremiumBo.getSpecialDiscount())
                && AssertUtils.isNotNull(applyPremiumBo.getPremiumBeforeDiscount())
                && AssertUtils.isNotEmpty(applyPremiumBo.getDiscountType())
                && (!AssertUtils.isNotEmpty(applyPremiumBo.getDiscountModel()) || ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(applyPremiumBo.getDiscountModel()))
        ) {
            //反算折扣前的实缴保费
            BigDecimal premiumBeforeDiscount = applyCoverageBo.getActualPremium()
                    .divide(new BigDecimal("1").subtract(applyPremiumBo.getSpecialDiscount()), 2, BigDecimal.ROUND_HALF_UP);
            if (applyCoverageBo.getTotalPremium().subtract(premiumBeforeDiscount).abs().compareTo(new BigDecimal("0.03")) < 0) {
                premiumBeforeDiscount = applyCoverageBo.getTotalPremium();
            }
            applyCoverageBo.setActualPremium(premiumBeforeDiscount);
            policyCoverageVo.setActualPremium(premiumBeforeDiscount);
        }
        policyCoverageVo.setAccSiMultiple(applyCoverageBo.getAccSiMultiple());
        policyCoverageVo.setAdditionalAccAmount(applyCoverageBo.getAdditionalAccAmount());
        policyCoverageVo.setPackageCode(applyCoverageBo.getPackageCode());
        policyCoverageVo.setPlanCode(applyCoverageBo.getPlanCode());
    }

    /**
     * 转换受益人信息
     *
     * @param policyInsuredReqFc         　保单被保人对象
     * @param listApplyBeneficiaryInfoBo 　投保单受益人集合
     */
    private void transferPolicyBeneficiaryData(PolicyInsuredVo policyInsuredReqFc, List<ApplyBeneficiaryInfoBo> listApplyBeneficiaryInfoBo) {
        //受益人
        if (AssertUtils.isNotNull(listApplyBeneficiaryInfoBo)) {
            List<PolicyBeneficiaryInfoVo> listPolicyBeneficiaryInfo = new ArrayList<>();
            listApplyBeneficiaryInfoBo.forEach(applyBeneficiaryInfoBo -> {
                PolicyBeneficiaryInfoVo policyBeneficiaryInfoVo = new PolicyBeneficiaryInfoVo();
                policyBeneficiaryInfoVo.setBankAccountName(applyBeneficiaryInfoBo.getBankAccountName());
                policyBeneficiaryInfoVo.setBankAccountNo(applyBeneficiaryInfoBo.getBankAccountNo());
                policyBeneficiaryInfoVo.setBankCode(applyBeneficiaryInfoBo.getBankCode());
                policyBeneficiaryInfoVo.setBeneficiaryGrade(applyBeneficiaryInfoBo.getBeneficiaryGrade());
                policyBeneficiaryInfoVo.setBeneficiaryNo(applyBeneficiaryInfoBo.getBeneficiaryNo());
                policyBeneficiaryInfoVo.setBeneficiaryNoOrder(applyBeneficiaryInfoBo.getBeneficiaryNoOrder());
                policyBeneficiaryInfoVo.setBeneficiaryProportion(applyBeneficiaryInfoBo.getBeneficiaryProportion());
                policyBeneficiaryInfoVo.setBeneficiaryType(applyBeneficiaryInfoBo.getBeneficiaryType());
                policyBeneficiaryInfoVo.setClaimPremDrawType(applyBeneficiaryInfoBo.getClaimPremDrawType());
                policyBeneficiaryInfoVo.setPromiseAge(applyBeneficiaryInfoBo.getPromiseAge());
                policyBeneficiaryInfoVo.setPromiseRate(applyBeneficiaryInfoBo.getPromiseRate());
                policyBeneficiaryInfoVo.setTransferGrantFlag(applyBeneficiaryInfoBo.getTransferGrantFlag());
                policyBeneficiaryInfoVo.setRelationship(applyBeneficiaryInfoBo.getRelationship());
                policyBeneficiaryInfoVo.setModifyFlag(applyBeneficiaryInfoBo.getModifyFlag());
                policyBeneficiaryInfoVo.setRelationshipInstructions(applyBeneficiaryInfoBo.getRelationshipInstructions());
                PolicyBeneficiaryVo policyBeneficiaryVo = (PolicyBeneficiaryVo) this.converterObject(applyBeneficiaryInfoBo.getApplyBeneficiaryBo(), PolicyBeneficiaryVo.class);
                policyBeneficiaryVo.setBeneficiaryId(null);
                policyBeneficiaryInfoVo.setPolicyBeneficiary(policyBeneficiaryVo);
                List<PolicyBeneficiaryAttachmentVo> policyBeneficiaryAttachmentReqFcs = new ArrayList<>();
                if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBo.getListBeneficiaryAttachment())) {
                    applyBeneficiaryInfoBo.getListBeneficiaryAttachment().forEach(applyBeneficiaryAttachmentBo -> {
                        PolicyBeneficiaryAttachmentVo policyBeneficiaryAttachmentVo = new PolicyBeneficiaryAttachmentVo();
                        policyBeneficiaryAttachmentVo.setPolicyBeneficiaryAttachmentId(null);
                        policyBeneficiaryAttachmentVo.setAttachmentId(applyBeneficiaryAttachmentBo.getAttachmentId());
                        policyBeneficiaryAttachmentVo.setCustomerId(
                                AssertUtils.isNotNull(applyBeneficiaryInfoBo.getApplyBeneficiaryBo()) ? applyBeneficiaryInfoBo.getApplyBeneficiaryBo().getCustomerId() : null
                        );
                        policyBeneficiaryAttachmentReqFcs.add(policyBeneficiaryAttachmentVo);
                    });
                }
                policyBeneficiaryInfoVo.setListBeneficiaryAttachment(policyBeneficiaryAttachmentReqFcs);
                listPolicyBeneficiaryInfo.add(policyBeneficiaryInfoVo);
            });
            policyInsuredReqFc.setListPolicyBeneficiary(listPolicyBeneficiaryInfo);
        }
    }


    /**
     * 保单打印
     *
     * @param policyReqFc 　保单对象
     */
    private void transferPolicyPrintData(PolicyVo policyReqFc) {
        PolicyPrintInfoVo policyPrintInfoVo = new PolicyPrintInfoVo();
        policyPrintInfoVo.setNeedPrint(ApplyTermEnum.YES_NO.YES.name());
        policyReqFc.setPolicyPrintInfo(policyPrintInfoVo);
    }


    /**
     * 转换保单支付记录
     *
     * @param applyBo       投保单业务对象
     * @param policyReqFc   保单对象
     * @param actualPayDate
     */
    private void transferPolicyPaymentData(ApplyBo applyBo, PolicyVo policyReqFc, String actualPayDate) {
        this.getLogger().info("投保单转保单开始--transferPolicyPaymentData--start:[{}]", DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        PolicyPaymentVo policyPaymentVo = new PolicyPaymentVo();
        //有业绩日期用业绩日期，没有业绩日期 看有没有回溯日期，有用实际缴费日期，没有用承保日期
        Long bizDate = AssertUtils.isNotNull(applyBo.getBizDate()) ? applyBo.getBizDate() : AssertUtils.isNotNull(applyBo.getBackTrackDate()) ? Long.valueOf(actualPayDate) : policyReqFc.getApproveDate();
        policyPaymentVo.setBizDate(bizDate);
        policyPaymentVo.setBizYearMonth(DateUtils.getTimeYearMonth(bizDate));
        policyPaymentVo.setBizBranchId(applyBo.getSalesBranchId());
        policyPaymentVo.setCurrencyCode(applyBo.getCurrencyCode());
        policyPaymentVo.setPolicyYear(1L);
        policyPaymentVo.setFrequency(1L);
        policyPaymentVo.setCommissionFee(new BigDecimal(0));
        policyPaymentVo.setSignTypeCode(applyBo.getSignType());
        policyPaymentVo.setPayStatusCode(applyBo.getApplyPremiumBo().getPremiumStatus());
        policyPaymentVo.setPaymentStatusCode(applyBo.getApplyPremiumBo().getPremiumStatus());
        policyPaymentVo.setPayModeCode(applyBo.getInitialPaymentMode());
        policyPaymentVo.setPaymentModeCode(applyBo.getInitialPaymentMode());
        policyPaymentVo.setApplyDate(applyBo.getApplyDate());
        policyPaymentVo.setPremiumSource(applyBo.getApplySource());
        policyPaymentVo.setProviderId(applyBo.getProviderId());
        policyPaymentVo.setStatusCode(ApplyTermEnum.SETTLEMENT_STATUS.RECEIVABLE.name());
        policyPaymentVo.setReceivableDate(policyReqFc.getApproveDate());
        Long gainedDate = AssertUtils.isNotNull(applyBo.getBackTrackDate()) ? Long.valueOf(actualPayDate) : policyReqFc.getApproveDate();
        policyPaymentVo.setGainedDate(gainedDate);

        policyPaymentVo.setPeriodOriginalPremium(BigDecimal.ZERO);
        policyPaymentVo.setPeriodActualPremium(BigDecimal.ZERO);

        final boolean[] isProductMonthlyTriple = new boolean[1];
        final boolean[] paymentInstallments = new boolean[1];
        List<String> monthlyTripleProductId = Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_9.id(), ProductTermEnum.PRODUCT.PRODUCT_13.id());

        List<PolicyInsuredVo> policyInsuredVos = policyReqFc.getListPolicyInsured();
        policyInsuredVos.forEach(policyInsuredVo -> {
            policyInsuredVo.getListPolicyCoverage().forEach(policyCoverageVo -> {
                PolicyCoveragePaymentVo policyCoveragePaymentVo = policyCoverageVo.getPolicyCoveragePremium().getPolicyCoveragePayment();
                if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoverageVo.getPrimaryFlag())) {
                    if (AssertUtils.isNotNull(policyCoveragePaymentVo.getAddPremiumTerm())) {
                        policyPaymentVo.setAddPremiumTerm(policyCoveragePaymentVo.getAddPremiumTerm().longValue());
                    }
                    policyPaymentVo.setAddPremiumStartDate(policyCoveragePaymentVo.getAddPremiumStartDate());
                    policyPaymentVo.setPremiumPeriod(policyCoveragePaymentVo.getPremiumPeriod());
                    policyPaymentVo.setPremiumPeriodUnit(policyCoveragePaymentVo.getPremiumPeriodUnit());
                    policyPaymentVo.setPremiumFrequency(policyCoveragePaymentVo.getPremiumFrequency());
                    policyPaymentVo.setPaymentCompleteDate(policyCoveragePaymentVo.getPaymentCompleteDate());
                    policyPaymentVo.setPayEndDate(policyCoveragePaymentVo.getPaymentCompleteDate());
                    isProductMonthlyTriple[0] = monthlyTripleProductId.contains(policyCoverageVo.getProductId())
                            && ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.MONTH.name().equals(policyCoverageVo.getPremiumFrequency());
                    paymentInstallments[0] = policyCoverageVo.getPaymentInstallments() > 1;
                }
                //费用相加
                policyPaymentVo.setCommissionFee(policyPaymentVo.getCommissionFee().add(policyCoveragePaymentVo.getCommissionFee()));
                //新增保费字段
                policyPaymentVo.setPeriodOriginalPremium(policyPaymentVo.getPeriodOriginalPremium().add(policyCoveragePaymentVo.getPeriodOriginalPremium()));
                policyPaymentVo.setPeriodActualPremium(policyPaymentVo.getPeriodActualPremium().add(policyCoveragePaymentVo.getPeriodActualPremium()));

                policyPaymentVo.setPeriodTotalPremium(policyPaymentVo.getPeriodTotalPremium().add(policyCoveragePaymentVo.getPeriodTotalPremium()));
                policyPaymentVo.setTotalOriginalPremium(policyPaymentVo.getTotalOriginalPremium().add(policyCoveragePaymentVo.getTotalOriginalPremium()));
                policyPaymentVo.setPeriodStandardPremium(policyPaymentVo.getPeriodStandardPremium().add(policyCoveragePaymentVo.getPeriodStandardPremium()));
                policyPaymentVo.setStandardPremium(policyPaymentVo.getStandardPremium().add(policyCoveragePaymentVo.getStandardPremium()));
                policyPaymentVo.setValuePremium(policyPaymentVo.getValuePremium().add(policyCoveragePaymentVo.getValuePremium()));
                policyPaymentVo.setAnnOccuAddPremium(policyPaymentVo.getAnnOccuAddPremium().add(policyCoveragePaymentVo.getAnnOccuAddPremium()));
                policyPaymentVo.setAnnStandardPremium(policyPaymentVo.getAnnStandardPremium().add(policyCoveragePaymentVo.getAnnStandardPremium()));
                policyPaymentVo.setAnnTotalPremium(policyPaymentVo.getAnnTotalPremium().add(policyCoveragePaymentVo.getAnnTotalPremium()));
                policyPaymentVo.setAnnWeakAddPremium(policyPaymentVo.getAnnWeakAddPremium().add(policyCoveragePaymentVo.getAnnWeakAddPremium()));
                policyPaymentVo.setGovStandardPremium(policyPaymentVo.getGovStandardPremium().add(policyCoveragePaymentVo.getGovStandardPremium()));
                policyPaymentVo.setPeriodOccuAddPremium(policyPaymentVo.getPeriodOccuAddPremium().add(policyCoveragePaymentVo.getPeriodOccuAddPremium()));
                policyPaymentVo.setCareerAddPremium(policyPaymentVo.getCareerAddPremium().add(policyCoveragePaymentVo.getCareerAddPremium()));
                policyPaymentVo.setPeriodWeakAddPremium(policyPaymentVo.getPeriodWeakAddPremium().add(policyCoveragePaymentVo.getPeriodWeakAddPremium()));
                policyPaymentVo.setWeakAddPremium(policyPaymentVo.getWeakAddPremium().add(policyCoveragePaymentVo.getWeakAddPremium()));
                policyPaymentVo.setTotalAddPremium(policyPaymentVo.getTotalAddPremium().add(policyCoveragePaymentVo.getTotalAddPremium()));
                policyPaymentVo.setAgencyFee(policyPaymentVo.getAgencyFee().add(policyCoveragePaymentVo.getAgencyFee()));
                policyPaymentVo.setBasePremium(policyPaymentVo.getBasePremium().add(policyCoveragePaymentVo.getBasePremium()));
                policyPaymentVo.setExtraPremium(policyPaymentVo.getExtraPremium().add(policyCoveragePaymentVo.getExtraPremium()));
                policyPaymentVo.setProductLevel(policyCoveragePaymentVo.getProductLevel());
                policyPaymentVo.getListPolicyCoveragePayment().add(policyCoveragePaymentVo);
            });
        });
        //sprint-v3.8.2.20210125  去掉个险以前的加费逻辑，算费的时候就已经加上加费
        policyPaymentVo.setTotalPremium(policyPaymentVo.getPeriodActualPremium().add(policyPaymentVo.getExtraPremium()).subtract(policyPaymentVo.getTotalDiscountPremium()));
        policyPaymentVo.setActualPremium(policyPaymentVo.getPeriodActualPremium());
        //九号产品月交保费特殊处理
        if (isProductMonthlyTriple[0] || paymentInstallments[0]) {
            policyPaymentVo.setPeriodActualPremium(policyPaymentVo.getPeriodOriginalPremium());
            policyPaymentVo.setActualPremium(policyPaymentVo.getPeriodOriginalPremium());
            policyPaymentVo.setTotalPremium(policyPaymentVo.getPeriodOriginalPremium());
        }
        policyReqFc.getPolicyPremium().setPolicyPayment(policyPaymentVo);
        this.getLogger().info("投保单转保单开始--transferPolicyPaymentData--end:[{}]", DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));

    }

    /**
     * 转换保单对账记录
     *
     * @param policyReqFc 保单对象
     */
    private void transferPolicyPremiumPersistData(PolicyVo policyReqFc) {
        //查询代理人
        AgentResponse agentRespFc = agentApi.agentByIdGet(policyReqFc.getPolicyAgent().getAgentId()).getData();
        PolicyPaymentVo policyPaymentVo = policyReqFc.getPolicyPremium().getPolicyPayment();
        PremiumPersistVo premiumPersistVo = new PremiumPersistVo();
        premiumPersistVo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
        premiumPersistVo.setProviderId(policyPaymentVo.getProviderId());
        premiumPersistVo.setBizYearMonth(policyPaymentVo.getBizYearMonth());
        premiumPersistVo.setBizDate(policyPaymentVo.getBizDate());
        premiumPersistVo.setBizBranchId(policyPaymentVo.getBizBranchId());
        premiumPersistVo.setPolicyYear(policyPaymentVo.getPolicyYear());
        premiumPersistVo.setFrequency(policyPaymentVo.getFrequency());
        premiumPersistVo.setPeriodTotalPremium(policyPaymentVo.getPeriodTotalPremium());
        premiumPersistVo.setAgencyFee(policyPaymentVo.getAgencyFee());
        premiumPersistVo.setCommissionFee(policyPaymentVo.getCommissionFee());
        premiumPersistVo.setServiceChargeFee(policyPaymentVo.getServiceChargeFee());
        premiumPersistVo.setServiceChargeRate(policyPaymentVo.getServiceChargeRate());
        premiumPersistVo.setBasePremium(policyPaymentVo.getBasePremium());
        premiumPersistVo.setCurrencyCode(policyPaymentVo.getCurrencyCode());
        premiumPersistVo.setSignDate(DateUtils.getCurrentDateToTime());
        premiumPersistVo.setScanDate(DateUtils.getCurrentDateToTime());
        premiumPersistVo.setApplyDate(policyPaymentVo.getApplyDate());
        premiumPersistVo.setApplicantName(policyReqFc.getPolicyApplicant().getName());
        premiumPersistVo.setApplicantIdno(policyReqFc.getPolicyApplicant().getIdNo());
        premiumPersistVo.setAgentId(policyReqFc.getPolicyAgent().getAgentId());
        premiumPersistVo.setAgentCode(policyReqFc.getPolicyAgent().getAgentCode());
        if (AssertUtils.isNotNull(agentRespFc)) {
            premiumPersistVo.setAgentName(agentRespFc.getAgentName());
        }
        premiumPersistVo.setPayModeCode(policyPaymentVo.getPayModeCode());
        premiumPersistVo.setPayDate(DateUtils.getCurrentDateToTime());
        premiumPersistVo.setPremiumSource(policyPaymentVo.getPremiumSource());
        premiumPersistVo.setReceivableDate(DateUtils.getCurrentDateToTime());
        premiumPersistVo.setStatusCode(policyPaymentVo.getStatusCode());
        premiumPersistVo.setSignTypeCode(policyPaymentVo.getSignTypeCode());
        policyPaymentVo.setPremiumPersist(premiumPersistVo);
    }

    /**
     * 转换保单支付记录
     *
     * @param policyReqFc 保单对象
     */
    private void transferPolicyPaymentRateData(PolicyVo policyReqFc) {
        this.getLogger().info("投保单转保单开始--transferPolicyPaymentRateData--start:[{}]", DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        PolicyPaymentVo policyPaymentVo = policyReqFc.getPolicyPremium().getPolicyPayment();
        this.getLogger().info("投保单转保单开始--transferPolicyPaymentRateData-request-rateCalculationRate:[{}]", JSON.toJSONString(policyPaymentVo));
        //调用
        PolicyPaymentRequest policyPaymentRequest = (PolicyPaymentRequest) this.converterObject(policyPaymentVo, PolicyPaymentRequest.class);
        ResultObject<PolicyPaymentResponse> reqFcResultObject = productRateApi.rateCalculationRate(policyPaymentRequest);
        this.getLogger().info("投保单转保单开始--transferPolicyPaymentRateData-response-rateCalculationRate:[{}]", JSON.toJSONString(reqFcResultObject.getData()));
        PolicyPaymentResponse policyPaymentResponse = reqFcResultObject.getData();
        if (AssertUtils.isNotNull(policyPaymentResponse)) {
            //数据匹配
            PolicyPaymentVo policyPayment = policyReqFc.getPolicyPremium().getPolicyPayment();
            policyPayment.setPeriodStandardPremium(policyPaymentResponse.getPeriodStandardPremium());
            policyPayment.setValuePremium(policyPaymentResponse.getValuePremium());
            policyPayment.setCommissionFee(policyPaymentResponse.getCommissionFee());
            policyPayment.setServiceChargeFee(policyPaymentResponse.getServiceChargeFee());
            policyPayment.setServiceChargeRate(policyPaymentResponse.getServiceChargeRate());
            //险种
            policyReqFc.getListPolicyInsured().forEach(policyInsuredReqFc -> {
                policyInsuredReqFc.getListPolicyCoverage().forEach(policyCoverageReqFc -> {
                    policyPaymentResponse.getListPolicyCoveragePayment().stream()
                            .filter(policyCoveragePaymentReqFc -> policyCoveragePaymentReqFc.getProductId().equals(policyCoverageReqFc.getProductId()))
                            .findFirst()
                            .ifPresent(policyCoveragePaymentRequest -> {
                                PolicyCoveragePaymentVo policyCoveragePaymentVo = policyCoverageReqFc.getPolicyCoveragePremium().getPolicyCoveragePayment();
                                policyCoveragePaymentVo.setCommissionFee(policyCoveragePaymentRequest.getCommissionFee());
                                policyCoveragePaymentVo.setCommissionFeeRate(policyCoveragePaymentRequest.getCommissionFeeRate());
                                policyCoveragePaymentVo.setServiceChargeFee(policyCoveragePaymentRequest.getServiceChargeFee());
                                policyCoveragePaymentVo.setServiceChargeRate(policyCoveragePaymentRequest.getServiceChargeRate());
                                policyCoveragePaymentVo.setPeriodStandardPremium(policyCoveragePaymentRequest.getPeriodStandardPremium());
                                policyCoveragePaymentVo.setPeriodStandardRate(policyCoveragePaymentRequest.getPeriodStandardRate());
                                policyCoveragePaymentVo.setValuePremiumRate(policyCoveragePaymentRequest.getValuePremiumRate());
                                policyCoveragePaymentVo.setValuePremium(policyCoveragePaymentRequest.getValuePremium());
                                policyCoveragePaymentVo.setAnnStandardPremium(policyCoveragePaymentRequest.getAnnStandardPremium());
                                policyCoveragePaymentVo.setAnnStandardRate(policyCoveragePaymentRequest.getAnnStandardRate());
                                policyCoveragePaymentVo.setAgencyFee(policyCoveragePaymentRequest.getAgencyFee());
                                policyCoveragePaymentVo.setAgencyFeeRate(policyCoveragePaymentRequest.getAgencyFeeRate());
                                policyCoveragePaymentVo.setGovStandardPremium(policyCoveragePaymentRequest.getGovStandardPremium());
                                policyCoveragePaymentVo.setGovStandardRate(policyCoveragePaymentRequest.getGovStandardRate());
                            });
                });
            });
        }
        this.getLogger().info("投保单转保单开始--transferPolicyPaymentRateData--end:[{}]", DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 保单付费人/保单缴费
     *
     * @param policyReqFc 保单对象
     * @param applyBo     投保单业务对象
     */
    private void transferPolicyPremiumPay(ApplyBo applyBo, PolicyVo policyReqFc) {
        // 查询投保单险种信息
        List<ApplyCoveragePo> applyCoveragePosOfInsured = applyCoverageBaseService.listApplyCoverageOfInsured(applyBo.getApplyId());
        if (AssertUtils.isNotEmpty(applyCoveragePosOfInsured)) {
            List<ApplyCoverageBo> applyCoverageBos = (List<ApplyCoverageBo>) this.converterList(
                    applyCoveragePosOfInsured, new TypeToken<List<ApplyCoverageBo>>() {
                    }.getType()
            );
            applyBo.setListInsuredCoverage(applyCoverageBos);
        }
        ApplyPremiumBo applyPremiumBo = applyBo.getApplyPremiumBo();
        List<ApplyCoverageBo> listInsuredCoverage = applyBo.getListInsuredCoverage();
        ApplyCoverageBo mainCoverage = new ApplyCoverageBo();
        for (ApplyCoverageBo applyCoverageBo : listInsuredCoverage) {
            if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                mainCoverage = applyCoverageBo;
                break;
            }
        }
        if (AssertUtils.isNotNull(applyPremiumBo)) {
            PolicyPremiumVo policyPremiumVo = new PolicyPremiumVo();
            ClazzUtils.copyPropertiesIgnoreNull(mainCoverage, policyPremiumVo);
            policyPremiumVo.setFrequency("1");
            policyPremiumVo.setPaymentNo(applyPremiumBo.getPaymentId());
            policyPremiumVo.setPayStatus(applyPremiumBo.getPremiumStatus());
            policyPremiumVo.setPremiumStatus(applyPremiumBo.getPremiumStatus());
            policyPremiumVo.setChargingMethod(applyBo.getInitialPaymentMode());
            policyPremiumVo.setTotalOriginalPremium(applyPremiumBo.getOriginalPremium());
            //新增保费字段
            policyPremiumVo.setPeriodOriginalPremium(applyPremiumBo.getPeriodOriginalPremium());
            policyPremiumVo.setPeriodTotalPremium(applyPremiumBo.getPeriodTotalPremium());

            policyPremiumVo.setPolicyBalance(applyPremiumBo.getOriginalPremium());
            if (AssertUtils.isNotNull(applyPremiumBo.getReceivableDate())) {
                policyPremiumVo.setPremDueDate(applyPremiumBo.getReceivableDate());
                policyPremiumVo.setReceivableDate(applyPremiumBo.getReceivableDate());
            }
            //反算折扣前的实缴保费
            if (AssertUtils.isNotNull(applyPremiumBo.getSpecialDiscount())
                    && AssertUtils.isNotNull(applyPremiumBo.getPremiumBeforeDiscount())
                    && AssertUtils.isNotEmpty(applyPremiumBo.getDiscountType())
            ) {
                policyPremiumVo.setPremiumBeforeDiscount(applyPremiumBo.getPremiumBeforeDiscount());
                policyPremiumVo.setSpecialDiscount(applyPremiumBo.getSpecialDiscount());
                policyPremiumVo.setDiscountType(applyPremiumBo.getDiscountType());
                policyPremiumVo.setTotalActualPremium(applyPremiumBo.getPremiumBeforeDiscount());
                policyPremiumVo.setActualPremium(applyPremiumBo.getPremiumBeforeDiscount());
                policyPremiumVo.setPromotionType(applyPremiumBo.getPromotionType());
                policyPremiumVo.setDiscountModel(applyPremiumBo.getDiscountModel());
                //网销20a优惠码设置参数
                policyPremiumVo.setPromotionalCode(applyPremiumBo.getPromotionalCode());
                policyPremiumVo.setPromotionalPremium(applyPremiumBo.getPromotionalPremium());
            } else {
                policyPremiumVo.setTotalActualPremium(applyPremiumBo.getTotalActualPremium());
                policyPremiumVo.setActualPremium(applyPremiumBo.getActualPremium());
            }
            policyReqFc.setPolicyPremium(policyPremiumVo);
        }
    }

    /**
     * 转换保单附件信息
     *
     * @param applyBo     　投保单业务对象
     * @param policyReqFc 保单对象
     */

    private void transferPolicyAttachment(ApplyBo applyBo, PolicyVo policyReqFc) {
        if (AssertUtils.isNotEmpty(applyBo.getListAttachment())) {
            List<PolicyAttachmentVo> policyAttachmentVos = (List<PolicyAttachmentVo>) this.converterList(
                    applyBo.getListAttachment(), new TypeToken<List<PolicyAttachmentVo>>() {
                    }.getType()
            );
            policyReqFc.setListPolicyAttachment(policyAttachmentVos);
        }
    }


    /**
     * 转换保单联系信息
     *
     * @param applyBo     　投保单业务对象
     * @param policyReqFc 　保单对象
     */
    private void transferPolicyContactData(ApplyBo applyBo, PolicyVo policyReqFc) {
        ApplyContactInfoBo applyContactInfoBo = applyBo.getApplyContact();
        if (AssertUtils.isNotNull(applyContactInfoBo)) {
            PolicyContactInfoVo policyContactInfoVo = new PolicyContactInfoVo();
            policyContactInfoVo.setContractAddress(applyContactInfoBo.getSendAddrContact());
            policyContactInfoVo.setContractEmail(applyContactInfoBo.getContractEmail());
            policyContactInfoVo.setContractHomePhone(applyContactInfoBo.getContractHomePhone());
            policyContactInfoVo.setContractMobile(applyContactInfoBo.getContractMobile());
            policyContactInfoVo.setContractOfficePhone(applyContactInfoBo.getContractOfficePhone());
            policyContactInfoVo.setContractPhone(applyContactInfoBo.getContractPhone());
            policyContactInfoVo.setNeedPosLetter(applyContactInfoBo.getNeedPosLetter());
            policyContactInfoVo.setSmsServiceFlag(applyContactInfoBo.getSmsServiceFlag());
            policyContactInfoVo.setSendAddrAreaCode(applyContactInfoBo.getSendAddrAreaCode());
            policyContactInfoVo.setPostcodes(applyContactInfoBo.getPostcodes());
            policyReqFc.setPolicyContactInfo(policyContactInfoVo);
        }
    }


    /**
     * 转换保单投保人
     *
     * @param applyBo     　投保单业务对象
     * @param policyReqFc 　保单对象
     */
    private void transferPolicyApplicantData(ApplyBo applyBo, PolicyVo policyReqFc) {
        ApplyApplicantBo applyApplicantBo = applyBo.getApplicant();
        PolicyApplicantVo policyApplicantVo = (PolicyApplicantVo) this.converterObject(applyApplicantBo, PolicyApplicantVo.class);
        policyApplicantVo.setApplicantId(null);
        policyReqFc.setPolicyApplicant(policyApplicantVo);
    }

    /**
     * 转换保单代理人
     *
     * @param applyBo     　投保单业务数据
     * @param policyReqFc 　保单对象
     */
    private void transferPolicyAgentData(ApplyBo applyBo, PolicyVo policyReqFc) {
        this.getLogger().info("投保单转保单开始--transferPolicyAgentData--start:[{}]", DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        ApplyAgentBo applyAgentBo = applyBo.getApplyAgentBo();
        PolicyAgentVo policyAgentReqFc = new PolicyAgentVo();
        policyAgentReqFc.setAgentCode(applyAgentBo.getAgentCode());
        policyAgentReqFc.setPercent(applyAgentBo.getPercent());
        policyAgentReqFc.setAgentId(applyAgentBo.getAgentId());
        policyAgentReqFc.setAbaAccount(applyAgentBo.getAbaAccount());
        policyAgentReqFc.setReferralName(applyAgentBo.getReferralName());
        policyReqFc.setPolicyAgent(policyAgentReqFc);
        this.getLogger().info("投保单转保单开始--transferPolicyAgentData--start:[{}]", DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (AssertUtils.isNotNull(applyBo.getApplyFactAgentPo())) {
            PolicyFactAgentVo policyFactAgentReqFc = new PolicyFactAgentVo();
            policyFactAgentReqFc.setAgentId(applyBo.getApplyFactAgentPo().getAgentId());
            policyReqFc.setPolicyFactAgent(policyFactAgentReqFc);
        }

        this.getLogger().info("投保单转保单开始--transferPolicyAgentData--start:[{}]", DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 转换保单账户数据
     *
     * @param policyReqFc 　保单对象
     * @param applyBo     投保单业务对象
     */
    private void transferPolicyAccountData(ApplyBo applyBo, PolicyVo policyReqFc) {
        this.getLogger().info("投保单转保单开始--transferPolicyAccountData--start:[{}]", DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        List<ApplyAccountBo> listApplyAccountBo = applyBo.getListApplyAccount();
        if (AssertUtils.isNotNull(listApplyAccountBo)) {
            List<PolicyAccountVo> policyAccountVos = new ArrayList<>();
            listApplyAccountBo.forEach(applyAccountBo -> {
                AccountRequest accountRequest = new AccountRequest();
                accountRequest.setAccountNo(applyAccountBo.getAccountNo());
                accountRequest.setAccountOwner(applyAccountBo.getAccountOwner());
                accountRequest.setAccountType(applyAccountBo.getAccountType());
                accountRequest.setAcctuserSignStatus(applyAccountBo.getAcctuserSignStatus());
                accountRequest.setAuthorizedDate(applyAccountBo.getAuthorizedDate());
                accountRequest.setBankCode(applyAccountBo.getBankCode());
                accountRequest.setCity(applyAccountBo.getAreaCode());
                accountRequest.setIdNo(applyAccountBo.getIdNo());
                accountRequest.setSubbranch(applyAccountBo.getSubbranch());
                accountRequest.setUseType(applyAccountBo.getUseType());
                accountRequest.setIdType(applyAccountBo.getIdType());
                platformAccountApi.accountInfoPost(accountRequest);
                PolicyAccountVo policyAccountVo = new PolicyAccountVo();
                policyAccountVo.setAccountNo(applyAccountBo.getAccountNo());
                policyAccountVo.setAccountOwner(applyAccountBo.getAccountOwner());
                policyAccountVo.setAccountType(applyAccountBo.getAccountType());
                policyAccountVo.setAcctuserSignStatus(applyAccountBo.getAcctuserSignStatus());
                policyAccountVo.setAuthorizedDate(applyAccountBo.getAuthorizedDate());
                policyAccountVo.setBankCode(applyAccountBo.getBankCode());
                policyAccountVo.setCity(applyAccountBo.getAreaCode());
                policyAccountVo.setIdNo(applyAccountBo.getIdNo());
                policyAccountVo.setSubbranch(applyAccountBo.getSubbranch());
                policyAccountVo.setUseType(applyAccountBo.getUseType());
                policyAccountVo.setIdType(applyAccountBo.getIdType());
                policyAccountVo.setCity(applyAccountBo.getAreaCode());
                policyAccountVo.setApplicantName(applyAccountBo.getApplicantName());
                policyAccountVo.setRelationship(applyAccountBo.getRelationship());
                policyAccountVos.add(policyAccountVo);
            });
            policyReqFc.setListPolicyAccount(policyAccountVos);
            this.getLogger().info("投保单转保单开始--transferPolicyAccountData--end:[{}]", DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }
    }

    /**
     * 转换保单基础信息
     *
     * @param applyBo     　投保单号
     * @param policyReqFc 　保单请求对象
     */
    private void transferBasePolicyData(ApplyBo applyBo, PolicyVo policyReqFc, String actualPayDate) {
        this.getLogger().info("投保单转保单--transferBasePolicyData--start:[{}]", applyBo.getApplyNo());
        Long approveDate = AssertUtils.isNotNull(applyBo.getBackTrackDate()) ? applyBo.getBackTrackDate() : Long.valueOf(actualPayDate);
        //投保单支付记录
        policyReqFc.setReferralCode(applyBo.getReferralCode());
        policyReqFc.setApplyDate(applyBo.getApplyDate());
        policyReqFc.setApplyId(applyBo.getApplyId());
        policyReqFc.setApplyNo(applyBo.getApplyNo());
        policyReqFc.setPolicyNo(applyBo.getPolicyNo());
        policyReqFc.setAcceptBranchId(applyBo.getAcceptBranchId());
        policyReqFc.setApplySource(applyBo.getApplySource());
        policyReqFc.setCertifyId(applyBo.getCertifyId());
        policyReqFc.setProviderId(applyBo.getProviderId());
        policyReqFc.setPolicyType(applyBo.getApplyType());
        policyReqFc.setSelfInsuranceFlag(applyBo.getSelfInsuranceFlag());
        if (AssertUtils.isNotNull(approveDate)) {
            //回调实收时间不为空，承保时间以此为准
            policyReqFc.setApproveDate(approveDate);
        } else if (AssertUtils.isNotNull(applyBo.getApproveDate())) {
            policyReqFc.setApproveDate(applyBo.getApproveDate());
        } else {
            policyReqFc.setApproveDate(this.calculateApproveDate(applyBo.getApplyPaymentTransactionBo()));
        }
        //有业绩日期用业绩日期，没有业绩日期 看有没有回溯日期，有用实际缴费日期，没有用承保日期
        Long bizDate = AssertUtils.isNotNull(applyBo.getBizDate()) ? applyBo.getBizDate() : AssertUtils.isNotNull(applyBo.getBackTrackDate()) ? Long.valueOf(actualPayDate) : policyReqFc.getApproveDate();
        policyReqFc.setBizDate(bizDate);

        if (AssertUtils.isNotNull(applyBo.getEffectiveDate())) {
            policyReqFc.setEffectiveDate(applyBo.getEffectiveDate());
        } else {
            policyReqFc.setEffectiveDate(policyReqFc.getApproveDate());
        }
        policyReqFc.setManagerBranchId(applyBo.getManagerBranchId());
        policyReqFc.setSalesBranchId(applyBo.getSalesBranchId());
        policyReqFc.setSignBranchId(applyBo.getSignBranchId());
        policyReqFc.setChannelTypeCode(applyBo.getChannelTypeCode());
        policyReqFc.setCurrencyCode(applyBo.getCurrencyCode());
        policyReqFc.setOnlineLanguage(applyBo.getOnlineLanguage());
        if (AssertUtils.isNotEmpty(applyBo.getVerifyNo())) {
            policyReqFc.setVerifyNo(applyBo.getVerifyNo());
        } else {
            policyReqFc.setVerifyNo((int) Math.floor(10000000 + random() * 89999999 + 1) + "");
        }

        //mustReturnFlag
        policyReqFc.setMustReturnFlag(applyDataTransform.calMustReturnFlag(applyBo));

        String passAutoUWFlag = applyDataTransform.doWhetherThroughAutoUW(applyBo.getApplyId(), applyBo.getApplyType())
                ? TerminologyConfigEnum.WHETHER.YES.name()
                : TerminologyConfigEnum.WHETHER.NO.name();
        policyReqFc.setPassAutoUWFlag(passAutoUWFlag);
        policyReqFc.setBackTrackDate(applyBo.getBackTrackDate());
        policyReqFc.setRiskCommencementDate(Long.valueOf(actualPayDate));
        policyReqFc.setActivationCode(applyBo.getActivationCode());
        this.getLogger().info("投保单转保单--transferBasePolicyData--end:[{}]", applyBo.getApplyNo());
    }

    /**
     * 暂时不用
     *
     * @param applyPaymentTransactionBo
     * @return
     */
    private Long calculateApproveDate(ApplyPaymentTransactionBo applyPaymentTransactionBo) {
        this.getLogger().info("投保单转保单--calculateApproveDate--start:[{}]", JSON.toJSONString(applyPaymentTransactionBo));
        //方法一开始已经判断过支付成功，故此处支付事物已是支付成功的记录
        Long approveDate = DateUtils.getCurrentTime();
        List<ApplyPaymentTransactionItemBo> applyPaymentTransactionItemBos = applyPaymentTransactionBo.getApplyPaymentTransactionItemBos();
        for (ApplyPaymentTransactionItemBo applyPaymentTransactionItemBo : applyPaymentTransactionItemBos) {
            //银行转账，wing线上线上的承保时间全取上传缴费凭证时间
            if (ApplyTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(applyPaymentTransactionItemBo.getPaymentMethodCode())
                    || ApplyTermEnum.PAYMENT_METHODS.WING_H5.name().equals(applyPaymentTransactionItemBo.getPaymentMethodCode())
                    || ApplyTermEnum.PAYMENT_METHODS.WING_OFFLINE.name().equals(applyPaymentTransactionItemBo.getPaymentMethodCode())
            ) {
                List<ApplyPaymentAttachmentPo> paymentAttachmentPos = applyAttachmentBaseService.listApplyPaymentAttachment(applyPaymentTransactionBo.getApplyId(), ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PAYMENT_INSTRUMENT.name());
                if (AssertUtils.isNotEmpty(paymentAttachmentPos)) {
                    approveDate = AssertUtils.isNotNull(paymentAttachmentPos.get(0).getUpdatedDate()) ? paymentAttachmentPos.get(0).getUpdatedDate() : DateUtils.getCurrentTime();
                    break;
                }
            }
            if (ApplyTermEnum.PAYMENT_METHODS.CASH.name().equals(applyPaymentTransactionItemBo.getPaymentMethodCode())) {
                approveDate = DateUtils.getCurrentTime();
                break;
            }
        }
        this.getLogger().info("投保单转保单--calculateApproveDate--end:[{}]", DateUtils.timeStrToString(approveDate, DateUtils.FORMATE5));
        return approveDate;
    }

    public ApplyPo transApplyFailedToPolicy(String applyId) {
        ApplyPo applyPo = applyExtDao.loadApplyPoById(applyId);
        if (AssertUtils.isNotNull(applyPo)) {
            applyPo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_FAILED.name());
        }
        return applyPo;
    }

    public void aaa(String language, String applyId) {
        //查询健康告知
        List<ApplyHealthQuestionnaireAnswerBo> healthQuestionnaireBos = applyExtDao.loadHealthQuestionnaire(applyId);
        List<ProductHealthNoticeResponse> productHealthNoticeResponses = (List<ProductHealthNoticeResponse>) this.converterList(healthQuestionnaireBos, new TypeToken<List<ProductHealthNoticeResponse>>() {
        }.getType());
        // 健康告知
        List<SyscodeRespFc> healthNoticeSyscodes = platformBaseInternationServiceApi
                .getTerminologyAssignLanguageList(InternationalTypeEnum.HEALTH_NOTICE.name(), language).getData();
        if (AssertUtils.isNotEmpty(productHealthNoticeResponses)) {
            if (AssertUtils.isNotEmpty(healthNoticeSyscodes)) {
                productHealthNoticeResponses.forEach(productHealthNoticeResponse -> {
                    healthNoticeSyscodes.stream().filter(syscode -> syscode.getCodeKey().equals(productHealthNoticeResponse.getQuestionCode()))
                            .findFirst().ifPresent(syscode -> productHealthNoticeResponse.setQuestionDesc(syscode.getCodeName()));
                });
            }
        }
        System.out.println(JSON.toJSONString(productHealthNoticeResponses));
    }

    public ApplySpecialContractResponse transApplySpecialContract(Users currentLoginUsers, String applyId) {

        ApplySpecialContractPo applySpecialContractPo = applySpecialContractBaseService.applySpecialContract(applyId);

        ApplySpecialContractResponse applySpecialContractResponse = (ApplySpecialContractResponse) this.converterObject(applySpecialContractPo, ApplySpecialContractResponse.class);
        if (!AssertUtils.isNotNull(applySpecialContractResponse)) {
            return applySpecialContractResponse;
        }

        ResultObject<UserResponse> userResultObject = platformUsersBaseApi.queryOneUsersPoById(applySpecialContractResponse.getCreatedUserId());

        if (!AssertUtils.isResultObjectDataNull(userResultObject)) {
            UserResponse userResponse = userResultObject.getData();
            if (userResponse.getUserId().equals(applySpecialContractResponse.getCreatedUserId())) {
                applySpecialContractResponse.setCreatedUserName(userResponse.getName());
            }
        }

        return applySpecialContractResponse;
    }

}
