package com.gclife.apply.validate.parameter.transform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentIdentityBaseResponse;
import com.gclife.agent.model.response.AgentRecommendBaseResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.agent.model.response.AgentSimpleBaseResponse;
import com.gclife.apply.core.jooq.tables.pojos.*;
import com.gclife.apply.model.ApplyInsuredDoneBo;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.config.ModelConstantEnum;
import com.gclife.apply.model.feign.ApplyAttachmentResp;
import com.gclife.apply.model.request.group.ProductRequest;
import com.gclife.apply.model.response.*;
import com.gclife.apply.model.response.group.ApplyPremiumResponse;
import com.gclife.apply.model.response.group.GroupApplyDetailResponse;
import com.gclife.apply.model.response.group.GroupInputResponse;
import com.gclife.apply.model.response.group.ProductResponse;
import com.gclife.apply.service.*;
import com.gclife.apply.transform.ApplyDataTransform;
import com.gclife.apply.validate.ClazzBusinessService;
import com.gclife.apply.validate.parameter.group.LanguageUtils;
import com.gclife.certify.api.CertifyApplyApi;
import com.gclife.certify.model.response.CertifyNumberResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.party.api.CustomerManageApi;
import com.gclife.party.model.request.CustomerBusinessRequest;
import com.gclife.party.model.response.UserCustomerResponse;
import com.gclife.platform.api.*;
import com.gclife.platform.model.request.SyscodeRequest;
import com.gclife.platform.model.response.*;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.response.PolicyResponse;
import com.gclife.policy.model.response.UnderwriteInfoResponse;
import com.gclife.product.api.ProductApi;
import com.gclife.product.api.ProductCertifyAttachmentBaseApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.request.calculate.ApplyRequest;
import com.gclife.product.model.request.calculate.InsuredRequest;
import com.gclife.product.model.response.insurnce.certify.CertifyAttachmentResponse;
import com.gclife.product.model.response.manager.ProductDetailedInfoResponse;
import com.gclife.workflow.api.WorkFlowApi;
import com.gclife.workflow.model.response.WorkItemTrackResponse;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.apply.model.config.ApplyErrorConfigEnum.*;
import static com.gclife.apply.model.config.GroupErrorConfigEnum.GROUP_APPLY_PARAMETER_MAIN_COVERAGE_IS_NOT_NULL;


/**
 * <AUTHOR>
 * create 18-5-2
 * description:
 */
@Component
public class GroupApplyTransData extends BaseBusinessServiceImpl {

    @Autowired
    ApplyParameterTransData applyParameterTransData;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private PlatformUsersBaseApi platformUsersBaseApi;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;
    @Autowired
    private PlatformCareerApi platformCareerApi;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private PlatformAreaApi platformAreaApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private AgentBaseAgentApi baseAgentApi;
    @Autowired
    private ApplyInputGetTransData applyInputGetTransData;
    @Autowired
    private ClazzBusinessService clazzBusinessService;
    @Autowired
    private ProductCertifyAttachmentBaseApi productCertifyAttachmentBaseApi;
    @Autowired
    private CertifyApplyApi certifyApplyApi;
    @Autowired
    private ProductApi productApi;
    @Autowired
    private ApplyCoverageBaseService applyCoverageBaseService;
    @Autowired
    private ApplySpecialContractBaseService applySpecialContractBaseService;
    @Autowired
    private ApplyDataTransform applyDataTransform;
    @Autowired
    private ApplyAgentBaseService applyAgentBaseService;
    @Autowired
    private CustomerManageApi customerManageApi;
    @Autowired
    private ApplyInsuredBaseService applyInsuredBaseService;
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private ApplyBeneficiaryBaseService applyBeneficiaryBaseService;
    @Autowired
    private WorkFlowApi workFlowApi;
    @Autowired
    private PlatformEmployeeApi platformEmployeeApi;
    @Autowired
    private ApplyUnderwriteBaseService applyUnderwriteBaseService;

    /**
     * 被保人手工导入
     *
     * @param listInsured 被保人数据
     * @param applyId     投保单ID
     * @param users       用户
     */
    public void transInsuredManualInput(List<com.gclife.apply.model.request.group.InsuredRequest> listInsured, List<ApplyInsuredBo> applyInsuredBos, String applyId, Users users) {
        //职业国际化
        List<String> occupationCodeList = listInsured.stream().filter(applyInsuredBo -> AssertUtils.isNotEmpty(applyInsuredBo.getOccupationCode()))
                .map(com.gclife.apply.model.request.group.InsuredRequest::getOccupationCode).collect(Collectors.toList());
        List<CareerResponse> careerList = null;
        if (AssertUtils.isNotEmpty(occupationCodeList)) {
            careerList = platformCareerApi.careerInfoPost(occupationCodeList, users.getLanguage()).getData();
        }
        for (com.gclife.apply.model.request.group.InsuredRequest insuredRequest : listInsured) {
            ApplyInsuredBo applyInsuredBo = new ApplyInsuredBo();
            applyInsuredBo.setApplyId(applyId);
            applyInsuredBo.setName(insuredRequest.getName());
            applyInsuredBo.setSex(insuredRequest.getSex());
            applyInsuredBo.setIdType(insuredRequest.getIdType());
            applyInsuredBo.setNationality(insuredRequest.getNationality());
            applyInsuredBo.setMobile(insuredRequest.getMobile());
            applyInsuredBo.setIdNo(insuredRequest.getIdNo().replaceAll("\\s*", ""));
            long birthdayTime = DateUtils.stringToTime(insuredRequest.getBirthdayFormat(), DateUtils.FORMATE3);
            applyInsuredBo.setBirthday(birthdayTime);
            applyInsuredBo.setOccupationCode(insuredRequest.getOccupationCode());
            if (AssertUtils.isNotNull(careerList)) {
                careerList.stream()
                        .filter(careerResponse -> insuredRequest.getOccupationCode().equals(careerResponse.getCareerId()))
                        .findFirst().ifPresent(careerResponse -> applyInsuredBo.setOccupationType(careerResponse.getCareerType()));
            }
            // 提前设置主键
            applyInsuredBo.setInsuredId(UUIDUtils.getUUIDShort());
            applyInsuredBo.setForceSave(true);

            applyInsuredBos.add(applyInsuredBo);
        }
    }

    /**
     * 组装被保人(模板导入)
     *
     * @param originalInsuredBos 原有被保人信息
     * @param insuredDoneBos     被保人清单完成信息
     * @param applyCoveragePos   公共险种
     * @param healthAnswerPos    存放健康告知信息
     * @param applyPo            投保单信息
     * @param users              用户
     */
    public List<ApplyInsuredBo> transInsuredOfTemplateImport(List<ApplyInsuredBo> originalInsuredBos, List<ApplyInsuredDoneBo> insuredDoneBos, List<ApplyCoveragePo> applyCoveragePos, List<ApplyGroupHealthQuestionnaireAnswerPo> healthAnswerPos, ApplyPo applyPo, Users users) {
        List<ApplyInsuredBo> applyInsuredBos = new ArrayList<>();
        String applyId = applyPo.getApplyId();
        // 职业国际化
        List<String> occupationCodes = insuredDoneBos.stream().filter(insuredDone -> AssertUtils.isNotEmpty(insuredDone.getOccupationCode()))
                .map(ApplyInsuredDonePo::getOccupationCode).collect(Collectors.toList());
        List<CareerResponse> careerResponses = platformCareerApi.careerInfoPost(occupationCodes, users.getLanguage()).getData();

        insuredDoneBos.forEach(insuredDone -> {
            ApplyInsuredBo applyInsuredBo = (ApplyInsuredBo) this.converterObject(insuredDone, ApplyInsuredBo.class);
            applyInsuredBo.setName(insuredDone.getInsuredName());
            applyInsuredBo.setApplyId(applyId);
            if (AssertUtils.isNotNull(careerResponses)) {
                careerResponses.stream()
                        .filter(careerResponse -> insuredDone.getOccupationCode().equals(careerResponse.getCareerId()))
                        .findFirst().ifPresent(careerResponse -> applyInsuredBo.setOccupationType(careerResponse.getCareerType()));
            }
            // 提前设置主键
            applyInsuredBo.setInsuredId(UUIDUtils.getUUIDShort());
            applyInsuredBo.setForceSave(true);

            // 设置险种
            List<ApplyCoverageBo> applyCoverageBos = new ArrayList<>();

            // 1+
            ApplyCoverageBo coverageOnePlus = new ApplyCoverageBo();
            applyCoveragePos.stream()
                    .filter(applyCoveragePo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag()) && ProductTermEnum.PRODUCT.PRODUCT_1_PLUS.id().equals(applyCoveragePo.getProductId()))
                    .findFirst().ifPresent(applyCoveragePo -> {
                ClazzUtils.copyPropertiesIgnoreNull(applyCoveragePo, coverageOnePlus);
                coverageOnePlus.setInsuredId(applyInsuredBo.getInsuredId());
                coverageOnePlus.setCoverageId(null);
            });
            List<ApplyCoverageLevelPo> coverageLevelOnePluses = new ArrayList<>();
            if (AssertUtils.isNotEmpty(insuredDone.getMultOnePlusA()) && Integer.valueOf(insuredDone.getMultOnePlusA()) > 0) {
                ApplyCoverageLevelPo applyCoverageLevelPo = new ApplyCoverageLevelPo();
                applyCoverageLevelPo.setApplyId(applyId);
                applyCoverageLevelPo.setProductLevel("A");
                applyCoverageLevelPo.setMult(insuredDone.getMultOnePlusA());
                coverageLevelOnePluses.add(applyCoverageLevelPo);
            }
            if (AssertUtils.isNotEmpty(insuredDone.getMultOnePlusB()) && Integer.valueOf(insuredDone.getMultOnePlusB()) > 0) {
                ApplyCoverageLevelPo applyCoverageLevelPo = new ApplyCoverageLevelPo();
                applyCoverageLevelPo.setApplyId(applyId);
                applyCoverageLevelPo.setProductLevel("B");
                applyCoverageLevelPo.setMult(insuredDone.getMultOnePlusB());
                coverageLevelOnePluses.add(applyCoverageLevelPo);
            }
            if (AssertUtils.isNotEmpty(insuredDone.getMultOnePlusC()) && Integer.valueOf(insuredDone.getMultOnePlusC()) > 0) {
                ApplyCoverageLevelPo applyCoverageLevelPo = new ApplyCoverageLevelPo();
                applyCoverageLevelPo.setApplyId(applyId);
                applyCoverageLevelPo.setProductLevel("C");
                applyCoverageLevelPo.setMult(insuredDone.getMultOnePlusC());
                coverageLevelOnePluses.add(applyCoverageLevelPo);
            }
            if (AssertUtils.isNotNull(coverageOnePlus) && AssertUtils.isNotEmpty(coverageOnePlus.getProductId())) {
                coverageOnePlus.setListCoverageLevel(coverageLevelOnePluses);
                applyCoverageBos.add(coverageOnePlus);
            }

            // 11
            if (AssertUtils.isNotEmpty(insuredDone.getProductLevelEleven()) &&
                    AssertUtils.isNotEmpty(insuredDone.getMultEleven()) && Integer.valueOf(insuredDone.getMultEleven()) > 0) {
                // 组装险种信息
                ApplyCoverageBo coverageEleven = this.getApplyCoverageBo(
                        applyCoveragePos, applyPo, applyInsuredBo, ProductTermEnum.PRODUCT.PRODUCT_11.id());

                List<ApplyCoverageLevelPo> coverageLevelElevens = new ArrayList<>();
                ApplyCoverageLevelPo applyCoverageLevelPo = new ApplyCoverageLevelPo();
                applyCoverageLevelPo.setApplyId(applyId);
                applyCoverageLevelPo.setProductLevel(insuredDone.getProductLevelEleven());
                applyCoverageLevelPo.setMult(insuredDone.getMultEleven());
                coverageLevelElevens.add(applyCoverageLevelPo);

                coverageEleven.setListCoverageLevel(coverageLevelElevens);
                applyCoverageBos.add(coverageEleven);
            }

            // 7+
            if (AssertUtils.isNotEmpty(insuredDone.getProductLevelSevenPlus()) &&
                    AssertUtils.isNotEmpty(insuredDone.getMultSevenPlus()) && Integer.valueOf(insuredDone.getMultSevenPlus()) > 0) {
                // 组装险种信息
                ApplyCoverageBo coverageSevenPlus = this.getApplyCoverageBo(
                        applyCoveragePos, applyPo, applyInsuredBo, ProductTermEnum.PRODUCT.PRODUCT_7_PLUS.id());

                List<ApplyCoverageLevelPo> coverageLevelSevenPluses = new ArrayList<>();
                ApplyCoverageLevelPo applyCoverageLevelPo = new ApplyCoverageLevelPo();
                applyCoverageLevelPo.setApplyId(applyId);
                applyCoverageLevelPo.setProductLevel(insuredDone.getProductLevelSevenPlus());
                applyCoverageLevelPo.setMult(insuredDone.getMultSevenPlus());
                coverageLevelSevenPluses.add(applyCoverageLevelPo);

                coverageSevenPlus.setListCoverageLevel(coverageLevelSevenPluses);
                applyCoverageBos.add(coverageSevenPlus);
            }

            // 12
            boolean existTwelve = (AssertUtils.isNotEmpty(insuredDone.getPlanTwelveClinic()) && AssertUtils.isNotNull(insuredDone.getPremuimTwelveClinic())) ||
                    (AssertUtils.isNotEmpty(insuredDone.getPlanTwelveHospital()) && AssertUtils.isNotNull(insuredDone.getPremuimTwelveHospital())) ||
                    (AssertUtils.isNotEmpty(insuredDone.getPlanTwelveTransfer()) && AssertUtils.isNotNull(insuredDone.getPremuimTwelveTransfer()));
            if (existTwelve) {
                // 组装险种信息
                ApplyCoverageBo coverageTwelve = this.getApplyCoverageBo(
                        applyCoveragePos, applyPo, applyInsuredBo, ProductTermEnum.PRODUCT.PRODUCT_12.id());
                List<ApplyCoverageDutyBo> listCoverageDuty = new ArrayList<>();
                if (AssertUtils.isNotEmpty(insuredDone.getPlanTwelveClinic()) && AssertUtils.isNotNull(insuredDone.getPremuimTwelveClinic())) {
                    ApplyCoverageDutyBo applyCoverageDutyBo = this.transDutyAndLevel(applyId, insuredDone, ApplyTermEnum.DUTY.PRO8800000000000G12_DUTY_1.name());

                    listCoverageDuty.add(applyCoverageDutyBo);
                }
                if (AssertUtils.isNotEmpty(insuredDone.getPlanTwelveHospital()) && AssertUtils.isNotNull(insuredDone.getPremuimTwelveHospital())) {
                    ApplyCoverageDutyBo applyCoverageDutyBo = this.transDutyAndLevel(applyId, insuredDone, ApplyTermEnum.DUTY.PRO8800000000000G12_DUTY_2.name());

                    listCoverageDuty.add(applyCoverageDutyBo);
                }
                if (AssertUtils.isNotEmpty(insuredDone.getPlanTwelveTransfer()) && AssertUtils.isNotNull(insuredDone.getPremuimTwelveTransfer())) {
                    ApplyCoverageDutyBo applyCoverageDutyBo = this.transDutyAndLevel(applyId, insuredDone, ApplyTermEnum.DUTY.PRO8800000000000G12_DUTY_3.name());

                    listCoverageDuty.add(applyCoverageDutyBo);
                }
                coverageTwelve.setListCoverageDuty(listCoverageDuty);
                applyCoverageBos.add(coverageTwelve);
            }

            //17
            ApplyCoverageBo coverageSeventeen = new ApplyCoverageBo();
            applyCoveragePos.stream()
                    .filter(applyCoveragePo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag()) && ProductTermEnum.PRODUCT.PRODUCT_17.id().equals(applyCoveragePo.getProductId()))
                    .findFirst().ifPresent(applyCoveragePo -> {
                ClazzUtils.copyPropertiesIgnoreNull(applyCoveragePo, coverageSeventeen);
                //17号产品缴费周期默认年缴
                coverageSeventeen.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());
                coverageSeventeen.setInsuredId(applyInsuredBo.getInsuredId());
                coverageSeventeen.setCoverageId(null);
            });
            List<ApplyCoverageLevelPo> coverageLevelSeventeen = new ArrayList<>();
            if (AssertUtils.isNotNull(insuredDone.getSeventeenTlPremium()) && insuredDone.getSeventeenTlPremium().compareTo(BigDecimal.ZERO) > 0
                    && AssertUtils.isNotNull(insuredDone.getSeventeenTlAmount()) && insuredDone.getSeventeenTlAmount().compareTo(BigDecimal.ZERO) > 0
            ) {
                ApplyCoverageLevelPo applyCoverageLevelPo = new ApplyCoverageLevelPo();
                applyCoverageLevelPo.setApplyId(applyId);
                applyCoverageLevelPo.setProductLevel("TL");
                applyCoverageLevelPo.setTotalPremium(insuredDone.getSeventeenTlPremium());
                applyCoverageLevelPo.setInputOriginPremium(insuredDone.getSeventeenTlPremium());
                applyCoverageLevelPo.setAmount(insuredDone.getSeventeenTlAmount());
                applyCoverageLevelPo.setMult(insuredDone.getSeventeenTlMult());
                //17号产品缴费周期默认年缴
                applyCoverageLevelPo.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());
                coverageLevelSeventeen.add(applyCoverageLevelPo);
            }
            if (AssertUtils.isNotNull(insuredDone.getSeventeenTaPremium()) && insuredDone.getSeventeenTaPremium().compareTo(BigDecimal.ZERO) > 0
                    && AssertUtils.isNotNull(insuredDone.getSeventeenTaAmount()) && insuredDone.getSeventeenTaAmount().compareTo(BigDecimal.ZERO) > 0
            ) {
                ApplyCoverageLevelPo applyCoverageLevelPo = new ApplyCoverageLevelPo();
                applyCoverageLevelPo.setApplyId(applyId);
                applyCoverageLevelPo.setProductLevel("TA");
                applyCoverageLevelPo.setTotalPremium(insuredDone.getSeventeenTaPremium());
                applyCoverageLevelPo.setInputOriginPremium(insuredDone.getSeventeenTaPremium());
                applyCoverageLevelPo.setAmount(insuredDone.getSeventeenTaAmount());
                applyCoverageLevelPo.setMult(insuredDone.getSeventeenTaMult());
                //17号产品缴费周期默认年缴
                applyCoverageLevelPo.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());
                coverageLevelSeventeen.add(applyCoverageLevelPo);
            }
            if (AssertUtils.isNotNull(insuredDone.getSeventeenTcPremium()) && insuredDone.getSeventeenTcPremium().compareTo(BigDecimal.ZERO) > 0
                    && AssertUtils.isNotNull(insuredDone.getSeventeenTcAmount()) && insuredDone.getSeventeenTcAmount().compareTo(BigDecimal.ZERO) > 0
            ) {
                ApplyCoverageLevelPo applyCoverageLevelPo = new ApplyCoverageLevelPo();
                applyCoverageLevelPo.setApplyId(applyId);
                applyCoverageLevelPo.setProductLevel("TC");
                applyCoverageLevelPo.setTotalPremium(insuredDone.getSeventeenTcPremium());
                applyCoverageLevelPo.setInputOriginPremium(insuredDone.getSeventeenTcPremium());
                applyCoverageLevelPo.setAmount(insuredDone.getSeventeenTcAmount());
                applyCoverageLevelPo.setMult(insuredDone.getSeventeenTcMult());
                //17号产品缴费周期默认年缴
                applyCoverageLevelPo.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());
                coverageLevelSeventeen.add(applyCoverageLevelPo);
            }
            if (AssertUtils.isNotNull(insuredDone.getSeventeenTsPremium()) && insuredDone.getSeventeenTsPremium().compareTo(BigDecimal.ZERO) > 0
                    && AssertUtils.isNotNull(insuredDone.getSeventeenTsAmount()) && insuredDone.getSeventeenTsAmount().compareTo(BigDecimal.ZERO) > 0
            ) {
                ApplyCoverageLevelPo applyCoverageLevelPo = new ApplyCoverageLevelPo();
                applyCoverageLevelPo.setApplyId(applyId);
                applyCoverageLevelPo.setProductLevel("TS");
                applyCoverageLevelPo.setTotalPremium(insuredDone.getSeventeenTsPremium());
                applyCoverageLevelPo.setInputOriginPremium(insuredDone.getSeventeenTsPremium());
                applyCoverageLevelPo.setAmount(insuredDone.getSeventeenTsAmount());
                applyCoverageLevelPo.setMult(insuredDone.getSeventeenTsMult());
                //17号产品缴费周期默认年缴
                applyCoverageLevelPo.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());
                coverageLevelSeventeen.add(applyCoverageLevelPo);
            }
            if (AssertUtils.isNotNull(coverageSeventeen) && AssertUtils.isNotEmpty(coverageSeventeen.getProductId())) {
                coverageSeventeen.setListCoverageLevel(coverageLevelSeventeen);
                applyCoverageBos.add(coverageSeventeen);
            }
            //18
            if (AssertUtils.isNotNull(insuredDone.getEighteenPremium()) && insuredDone.getEighteenPremium().compareTo(BigDecimal.ZERO) > 0
                    && AssertUtils.isNotEmpty(insuredDone.getEighteenLevel())
            ) {
                // 组装险种信息
                ApplyCoverageBo coverageEighteen = this.getApplyCoverageBo(
                        applyCoveragePos, applyPo, applyInsuredBo, ProductTermEnum.PRODUCT.PRODUCT_18.id());
                //18号产品缴费周期默认年缴
                coverageEighteen.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());

                List<ApplyCoverageLevelPo> coverageLevelSevenPluses = new ArrayList<>();
                ApplyCoverageLevelPo applyCoverageLevelPo = new ApplyCoverageLevelPo();
                applyCoverageLevelPo.setApplyId(applyId);
                applyCoverageLevelPo.setTotalPremium(insuredDone.getEighteenPremium());
                applyCoverageLevelPo.setInputOriginPremium(insuredDone.getEighteenPremium());
                applyCoverageLevelPo.setProductLevel(insuredDone.getEighteenLevel());
                applyCoverageLevelPo.setMult(insuredDone.getEighteenMult());
                //18号产品缴费周期默认年缴
                applyCoverageLevelPo.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());
                coverageLevelSevenPluses.add(applyCoverageLevelPo);

                coverageEighteen.setListCoverageLevel(coverageLevelSevenPluses);
                applyCoverageBos.add(coverageEighteen);
            }

            //26
            if (AssertUtils.isNotNull(insuredDone.getTwentysixPremium()) && insuredDone.getTwentysixPremium().compareTo(BigDecimal.ZERO) > 0
                    && AssertUtils.isNotNull(insuredDone.getTwentysixAmount()) && insuredDone.getTwentysixAmount().compareTo(BigDecimal.ZERO) > 0
            ) {
                // 组装险种信息
                ApplyCoverageBo coverageTwentysix = this.getApplyCoverageBo(
                        applyCoveragePos, applyPo, applyInsuredBo, ProductTermEnum.PRODUCT.PRODUCT_26.id());
                //18号产品缴费周期默认年缴
                coverageTwentysix.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());

                List<ApplyCoverageLevelPo> coverageLevelTwentysix = new ArrayList<>();
                ApplyCoverageLevelPo applyCoverageLevelPo = new ApplyCoverageLevelPo();
                applyCoverageLevelPo.setApplyId(applyId);
                applyCoverageLevelPo.setTotalPremium(insuredDone.getTwentysixPremium());
                applyCoverageLevelPo.setInputOriginPremium(insuredDone.getTwentysixPremium());
                // 天正说默认设置档次为PA
                applyCoverageLevelPo.setProductLevel("PA");
                applyCoverageLevelPo.setAmount(insuredDone.getTwentysixAmount());
                applyCoverageLevelPo.setMult(insuredDone.getTwentysixMult());
                //26号产品缴费周期默认年缴
                applyCoverageLevelPo.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());
                coverageLevelTwentysix.add(applyCoverageLevelPo);

                coverageTwentysix.setListCoverageLevel(coverageLevelTwentysix);
                applyCoverageBos.add(coverageTwentysix);
            }

            //27
            if (AssertUtils.isNotNull(insuredDone.getTwentysevenPremium()) && insuredDone.getTwentysevenPremium().compareTo(BigDecimal.ZERO) > 0
                    && AssertUtils.isNotEmpty(insuredDone.getTwentysevenLevel())
            ) {
                // 组装险种信息
                ApplyCoverageBo coverageTwentyseven = this.getApplyCoverageBo(
                        applyCoveragePos, applyPo, applyInsuredBo, ProductTermEnum.PRODUCT.PRODUCT_27.id());
                //27号产品缴费周期默认年缴
                coverageTwentyseven.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());

                List<ApplyCoverageLevelPo> coverageLevelTwentyseven = new ArrayList<>();
                ApplyCoverageLevelPo applyCoverageLevelPo = new ApplyCoverageLevelPo();
                applyCoverageLevelPo.setApplyId(applyId);
                applyCoverageLevelPo.setTotalPremium(insuredDone.getTwentysevenPremium());
                applyCoverageLevelPo.setInputOriginPremium(insuredDone.getTwentysevenPremium());
                applyCoverageLevelPo.setProductLevel(insuredDone.getTwentysevenLevel());
                applyCoverageLevelPo.setMult(insuredDone.getTwentysevenMult());
                //27号产品缴费周期默认年缴
                applyCoverageLevelPo.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());
                coverageLevelTwentyseven.add(applyCoverageLevelPo);

                coverageTwentyseven.setListCoverageLevel(coverageLevelTwentyseven);
                applyCoverageBos.add(coverageTwentyseven);
            }

            boolean pro29Flag = false;
            //29
            if (AssertUtils.isNotNull(insuredDone.getTwentyninePremium()) && insuredDone.getTwentyninePremium().compareTo(BigDecimal.ZERO) > 0
                    && AssertUtils.isNotEmpty(insuredDone.getTwentynineLevel())
            ) {
                // 组装险种信息
                ApplyCoverageBo coverageTwentynine = this.getApplyCoverageBo(
                        applyCoveragePos, applyPo, applyInsuredBo, ProductTermEnum.PRODUCT.PRODUCT_29.id());
                //29号产品缴费周期默认年缴
                coverageTwentynine.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());

                List<ApplyCoverageLevelPo> coverageLevelTwentynine = new ArrayList<>();
                ApplyCoverageLevelPo applyCoverageLevelPo = new ApplyCoverageLevelPo();
                applyCoverageLevelPo.setApplyId(applyId);
                applyCoverageLevelPo.setTotalPremium(insuredDone.getTwentyninePremium());
                applyCoverageLevelPo.setInputOriginPremium(insuredDone.getTwentyninePremium());
                applyCoverageLevelPo.setProductLevel(insuredDone.getTwentynineLevel());
                applyCoverageLevelPo.setAmount(insuredDone.getTwentynineAmount());
                applyCoverageLevelPo.setMult("1");
                //29号产品缴费周期默认年缴
                applyCoverageLevelPo.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());
                coverageLevelTwentynine.add(applyCoverageLevelPo);

                coverageTwentynine.setListCoverageLevel(coverageLevelTwentynine);
                applyCoverageBos.add(coverageTwentynine);
                pro29Flag = true;
            }

            //33
            if (AssertUtils.isNotNull(insuredDone.getThirtythreePremium()) && insuredDone.getThirtythreePremium().compareTo(BigDecimal.ZERO) > 0
                    && AssertUtils.isNotNull(insuredDone.getThirtythreeAmount()) && insuredDone.getThirtythreeAmount().compareTo(BigDecimal.ZERO) > 0
            ) {
                // 组装险种信息
                ApplyCoverageBo coverageThirtythree = this.getApplyCoverageBo(
                        applyCoveragePos, applyPo, applyInsuredBo, ProductTermEnum.PRODUCT.PRODUCT_33.id());
                //33号产品缴费周期默认年缴
                coverageThirtythree.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());

                List<ApplyCoverageLevelPo> coverageLevelThirtythree = new ArrayList<>();
                ApplyCoverageLevelPo applyCoverageLevelPo = new ApplyCoverageLevelPo();
                applyCoverageLevelPo.setApplyId(applyId);
                applyCoverageLevelPo.setTotalPremium(insuredDone.getThirtythreePremium());
                applyCoverageLevelPo.setInputOriginPremium(insuredDone.getThirtythreePremium());
                // 天正说档次在这里没有特殊的意义，设置为--
                applyCoverageLevelPo.setProductLevel(AssertUtils.isNotEmpty(insuredDone.getTwentynineLevel()) ? insuredDone.getTwentynineLevel() : "--");
                applyCoverageLevelPo.setAmount(insuredDone.getThirtythreeAmount());
                applyCoverageLevelPo.setMult(insuredDone.getThirtythreeMult());
                //33号产品缴费周期默认年缴
                applyCoverageLevelPo.setPremiumFrequency(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name());
                coverageLevelThirtythree.add(applyCoverageLevelPo);

                coverageThirtythree.setListCoverageLevel(coverageLevelThirtythree);
                applyCoverageBos.add(coverageThirtythree);
            }

            // 设置险种
            applyInsuredBo.setListCoverage(applyCoverageBos);

            // 受益人信息
            List<ApplyBeneficiaryInfoBo> beneficiaryInfoBos = new ArrayList<>();
            insuredDone.getListBeneficiaryDone().forEach(beneficiaryDonePo -> {
                ApplyBeneficiaryBo beneficiaryBo = new ApplyBeneficiaryBo();
                ClazzUtils.copyPropertiesIgnoreNull(beneficiaryDonePo, beneficiaryBo);
                beneficiaryBo.setBeneficiaryId(UUIDUtils.getUUIDShort());
                beneficiaryBo.setForceSave(true);

                ApplyBeneficiaryInfoBo beneficiaryInfoBo = new ApplyBeneficiaryInfoBo();
                ClazzUtils.copyPropertiesIgnoreNull(beneficiaryDonePo, beneficiaryInfoBo);
                beneficiaryInfoBo.setBeneficiaryId(beneficiaryBo.getBeneficiaryId());
                beneficiaryInfoBo.setInsuredId(applyInsuredBo.getInsuredId());
                beneficiaryInfoBo.setModifyFlag(TerminologyConfigEnum.WHETHER.YES.name());

                // 处理受益人与被保人关系为“Other”的数据
                if (ApplyTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER.name().equals(beneficiaryInfoBo.getRelationship())) {
                    originalInsuredBos.stream()
                            .filter(originalInsured -> originalInsured.getIdNo().equals(applyInsuredBo.getIdNo())
                                    && AssertUtils.isNotEmpty(originalInsured.getListBeneficiary()))
                            .findFirst().ifPresent(originalInsured -> {
                        originalInsured.getListBeneficiary().stream()
                                .filter(originalBeneficiaryInfo -> AssertUtils.isNotNull(originalBeneficiaryInfo.getApplyBeneficiaryBo())
                                        && originalBeneficiaryInfo.getApplyBeneficiaryBo().getName().equals(beneficiaryBo.getName())
                                        && ApplyTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER.name().equals(originalBeneficiaryInfo.getRelationship()))
                                .findFirst().ifPresent(originalBeneficiaryInfo -> beneficiaryInfoBo.setRelationshipInstructions(originalBeneficiaryInfo.getRelationshipInstructions()));
                    });
                }

                beneficiaryInfoBo.setApplyBeneficiaryBo(beneficiaryBo);
                beneficiaryInfoBos.add(beneficiaryInfoBo);
            });
            applyInsuredBo.setListBeneficiary(beneficiaryInfoBos);
            applyInsuredBos.add(applyInsuredBo);

            // 健康告知
            if (AssertUtils.isNotEmpty(insuredDone.getHealthAnswer())) {
                for (String healthAnswer : insuredDone.getHealthAnswer().split(",")) {
                    ApplyGroupHealthQuestionnaireAnswerPo healthAnswerPo = new ApplyGroupHealthQuestionnaireAnswerPo();
                    healthAnswerPo.setApplyId(applyId);
                    healthAnswerPo.setInsuredId(applyInsuredBo.getInsuredId());
                    // 29产品的健康告知需要特殊处理加上空格
                    healthAnswerPo.setAnswer(pro29Flag ? (healthAnswer + " ") : healthAnswer);
                    healthAnswerPo.setAnswerRemark(insuredDone.getHealthAnswerRemark());
                    healthAnswerPo.setCustomerType(ApplyTermEnum.CUSTOMER_TYPE.INSURED.name());
                    healthAnswerPos.add(healthAnswerPo);
                }
            }
        });
        return applyInsuredBos;
    }

    /**
     * 组装责任和档次
     *
     * @param applyId     投保单id
     * @param insuredDone
     * @param dutyId      责任ID
     * @return
     */
    private ApplyCoverageDutyBo transDutyAndLevel(String applyId, ApplyInsuredDonePo insuredDone, String dutyId) {
        List<ApplyCoverageLevelPo> applyCoverageLevelPos = new ArrayList<>();
        ApplyCoverageLevelPo coverageLevelPo = new ApplyCoverageLevelPo();
        coverageLevelPo.setApplyId(applyId);
        if (ApplyTermEnum.DUTY.PRO8800000000000G12_DUTY_1.name().equals(dutyId)) {
            coverageLevelPo.setProductLevel(insuredDone.getPlanTwelveClinic());
            coverageLevelPo.setMult(AssertUtils.isNotEmpty(insuredDone.getMultTwelveClinic()) ? insuredDone.getMultTwelveClinic() : "1");
            coverageLevelPo.setTotalPremium(insuredDone.getPremuimTwelveClinic());
        } else if (ApplyTermEnum.DUTY.PRO8800000000000G12_DUTY_2.name().equals(dutyId)) {
            coverageLevelPo.setProductLevel(insuredDone.getPlanTwelveHospital());
            coverageLevelPo.setMult(AssertUtils.isNotEmpty(insuredDone.getMultTwelveHospital()) ? insuredDone.getMultTwelveHospital() : "1");
            coverageLevelPo.setTotalPremium(insuredDone.getPremuimTwelveHospital());
        } else if (ApplyTermEnum.DUTY.PRO8800000000000G12_DUTY_3.name().equals(dutyId)) {
            coverageLevelPo.setProductLevel(insuredDone.getPlanTwelveTransfer());
            coverageLevelPo.setMult(AssertUtils.isNotEmpty(insuredDone.getMultTwelveTransfer()) ? insuredDone.getMultTwelveTransfer() : "1");
            coverageLevelPo.setTotalPremium(insuredDone.getPremuimTwelveTransfer());
        }
        coverageLevelPo.setPremium(coverageLevelPo.getTotalPremium().divide(new BigDecimal(coverageLevelPo.getMult()), 2, BigDecimal.ROUND_HALF_UP));
        applyCoverageLevelPos.add(coverageLevelPo);

        ApplyCoverageDutyBo coverageDutyBo = new ApplyCoverageDutyBo();
        coverageDutyBo.setApplyId(applyId);
        coverageDutyBo.setDutyId(dutyId);
        coverageDutyBo.setDutyName(ApplyTermEnum.DUTY.valueOf(dutyId).desc());
        coverageDutyBo.setListCoverageLevel(applyCoverageLevelPos);
        return coverageDutyBo;
    }

    /**
     * 计算保费
     *
     * @param branchId        销售机构ID
     * @param applyInsuredBos
     */
    public List<ApplyCoverageBo> calculatePremium(String branchId, List<ApplyInsuredBo> applyInsuredBos, ApplyPremiumBo applyPremiumBo) {
        ApplyRequest applyRequest = new ApplyRequest();
        List<InsuredRequest> insuredRequests = (List<InsuredRequest>) this.converterList(
                applyInsuredBos, new TypeToken<List<InsuredRequest>>() {
                }.getType()
        );
        applyRequest.setListInsured(insuredRequests);
        applyRequest.setBranchId(branchId);
        applyRequest.setBusinessType("APPLY");
        applyRequest.setPromotionType(applyPremiumBo.getPromotionType());
        this.getLogger().info("saveInsured-保费计算请求参数:{}", JSON.toJSONString(applyRequest));
        ResultObject<com.gclife.product.model.response.apply.ApplyResponse> responseResultObject = productApi.trialCalculation(applyRequest);
        this.getLogger().info("saveInsured-保费计算返回参数:{}", JSON.toJSONString(responseResultObject));
        AssertUtils.isResultObjectError(this.getLogger(), responseResultObject);
        com.gclife.product.model.response.apply.ApplyResponse applyResponse = responseResultObject.getData();

        List<ApplyCoverageBo> applyCoverageBos = new ArrayList<>();
        if (AssertUtils.isNotNull(applyResponse) && AssertUtils.isNotEmpty(applyResponse.getListInsured())) {
            applyPremiumBo.setCurrencyCode(applyResponse.getCurrencyCode());
            applyPremiumBo.setTotalPremium(applyResponse.getTotalPremium());
            applyPremiumBo.setOriginalPremium(applyResponse.getTotalPremium());
            applyPremiumBo.setReceivablePremium(applyResponse.getTotalPremium());
            //新增保费字段
            applyPremiumBo.setPeriodOriginalPremium(applyResponse.getTotalPremium());
            applyPremiumBo.setPeriodTotalPremium(applyResponse.getTotalPremium());
            applyPremiumBo.setTotalActualPremium(applyResponse.getActualPremium());
            applyPremiumBo.setActualPremium(applyResponse.getActualPremium());

            // 设置保费
            applyInsuredBos.forEach(insuredBo -> {
                // 被保人
                applyResponse.getListInsured().stream()
                        .filter(insuredResponse -> insuredResponse.getIdNo().equals(insuredBo.getIdNo()))
                        .findFirst().ifPresent(insuredResponse -> {
                    insuredBo.getListCoverage().forEach(coverageBo -> {
                        // 险种
                        insuredResponse.getListCoverage().stream()
                                .filter(coverageResponse -> coverageResponse.getProductId().equals(coverageBo.getProductId()))
                                .findFirst().ifPresent(coverageResponse -> {
                            coverageBo.setTotalPremium(coverageResponse.getTotalPremium());
                            coverageBo.setOriginalPremium(coverageResponse.getOriginalPremium());
                            coverageBo.setMult(coverageResponse.getMult() + "");
                            if (AssertUtils.isNotNull(coverageResponse.getAmount())) {
                                coverageBo.setAmount(coverageResponse.getAmount() + "");
                            }

                            if (TerminologyConfigEnum.WHETHER.YES.name().equals(coverageBo.getDutyChooseFlag())) {
                                // 责任
                                coverageBo.getListCoverageDuty().forEach(coverageDutyBo -> {
                                    coverageResponse.getListCoverageDuty().stream()
                                            .filter(dutyResponse -> dutyResponse.getDutyId().equals(coverageDutyBo.getDutyId()))
                                            .findFirst().ifPresent(dutyResponse -> {
                                        coverageDutyBo.getListCoverageLevel().forEach(coverageLevelPo -> {
                                            dutyResponse.getListCoverageLevel().stream()
                                                    .filter(levelResponse -> levelResponse.getProductLevel().equals(coverageLevelPo.getProductLevel()))
                                                    .findFirst().ifPresent(levelResponse -> {
                                                coverageLevelPo.setTotalPremium(levelResponse.getTotalPremium());
                                                coverageLevelPo.setPremium(levelResponse.getPremium());
                                                coverageLevelPo.setMult(levelResponse.getMult() + "");
                                                if (AssertUtils.isNotEmpty(levelResponse.getAmount())) {
                                                    coverageLevelPo.setAmount(new BigDecimal(levelResponse.getAmount()));
                                                }
                                            });
                                        });
                                    });
                                });
                            } else {
                                // 档次
                                coverageBo.getListCoverageLevel().forEach(coverageLevelPo -> {
                                    coverageResponse.getListCoverageLevel().stream()
                                            .filter(levelResponse -> levelResponse.getProductLevel().equals(coverageLevelPo.getProductLevel()))
                                            .findFirst().ifPresent(levelResponse -> {
                                        coverageLevelPo.setTotalPremium(levelResponse.getTotalPremium());
                                        coverageLevelPo.setPremium(levelResponse.getPremium());
                                        coverageLevelPo.setMult(levelResponse.getMult() + "");
                                        if (AssertUtils.isNotEmpty(levelResponse.getAmount())) {
                                            coverageLevelPo.setAmount(new BigDecimal(levelResponse.getAmount()));
                                        }
                                    });
                                });
                            }
                        });
                        applyCoverageBos.add(coverageBo);
                    });
                });
            });
        }
        return applyCoverageBos;
    }

    /**
     * 处理被保人新增
     *
     * @param addApplyInsureds 待新增被保人
     * @param newCoverageBos   新增险种
     * @param userId           用户id
     */
    public void handleInsuredAdd(List<ApplyInsuredBo> addApplyInsureds, List<ApplyCoverageBo> newCoverageBos, String userId) {
        ApplyAgentPo applyAgentPo = applyAgentBaseService.queryApplyAgent(addApplyInsureds.get(0).getApplyId());
        String agentId = AssertUtils.isNotNull(applyAgentPo) ? applyAgentPo.getAgentId() : userId;

        // 批量保存被保人、受益人客户信息
        List<CustomerBusinessRequest> customerBusinessRequests = new ArrayList<>();
        addApplyInsureds.forEach(insuredBo -> {
            // 被保人
            CustomerBusinessRequest customerBusinessRequest = (CustomerBusinessRequest) this.converterObject(insuredBo, CustomerBusinessRequest.class);
            //准客户不能设置勋章，否则会被当做客户存起来
//            customerBusinessRequest.setMedalNo(ApplyTermEnum.CUSTOMER_TYPE.INSURED.name());
            customerBusinessRequest.setUserId(agentId);
            customerBusinessRequests.add(customerBusinessRequest);
            if (AssertUtils.isNotEmpty(insuredBo.getListBeneficiary())) {
                insuredBo.getListBeneficiary().forEach(beneficiaryInfoBo -> {
                    if (AssertUtils.isNotNull(beneficiaryInfoBo.getApplyBeneficiaryBo())
                            && AssertUtils.isNotEmpty(beneficiaryInfoBo.getApplyBeneficiaryBo().getIdNo()) && AssertUtils.isNotNull(beneficiaryInfoBo.getApplyBeneficiaryBo().getIdType())) {
                        CustomerBusinessRequest beneficialBusinessRequest = (CustomerBusinessRequest) this.converterObject(beneficiaryInfoBo.getApplyBeneficiaryBo(), CustomerBusinessRequest.class);
                        //准客户不能设置勋章，否则会被当做客户存起来
//                        beneficialBusinessRequest.setMedalNo(ApplyTermEnum.CUSTOMER_TYPE.BENEFICIARY.name());
                        beneficialBusinessRequest.setUserId(agentId);
                        customerBusinessRequests.add(beneficialBusinessRequest);
                    }
                });
            }
        });
        ResultObject<List<UserCustomerResponse>> listCustomerObject = customerManageApi.saveCustomerMessagePoList(customerBusinessRequests);
        AssertUtils.isResultObjectError(getLogger(), listCustomerObject);

        // 1.保存被保人、受益人信息
        List<ApplyInsuredPo> applyInsuredPos = new ArrayList<>();
        List<ApplyBeneficiaryInfoPo> beneficiaryInfoPos = new ArrayList<>();
        List<ApplyBeneficiaryPo> beneficiaryPos = new ArrayList<>();
        addApplyInsureds.forEach(applyInsuredBo -> {
            listCustomerObject.getData().stream()
                    .filter(userCustomer -> applyInsuredBo.getIdNo().equals(userCustomer.getIdNo()))
                    .findFirst().ifPresent(userCustomer -> {
                applyInsuredBo.setCustomerId(userCustomer.getCustomerId());
            });
            applyInsuredPos.add(applyInsuredBo);
            // 受益人
            if (AssertUtils.isNotEmpty(applyInsuredBo.getListBeneficiary())) {
                applyInsuredBo.getListBeneficiary().forEach(beneficiaryInfoBo -> {
                    beneficiaryInfoPos.add(beneficiaryInfoBo);
                    if (AssertUtils.isNotNull(beneficiaryInfoBo.getApplyBeneficiaryBo())) {
                        if (AssertUtils.isNotEmpty(beneficiaryInfoBo.getApplyBeneficiaryBo().getIdNo())
                                && AssertUtils.isNotNull(beneficiaryInfoBo.getApplyBeneficiaryBo().getIdType())) {
                            listCustomerObject.getData().stream()
                                    .filter(userCustomer -> beneficiaryInfoBo.getApplyBeneficiaryBo().getIdNo().equals(userCustomer.getIdNo())
                                            && beneficiaryInfoBo.getApplyBeneficiaryBo().getIdType().equals(userCustomer.getIdType()))
                                    .findFirst().ifPresent(userCustomer -> {
                                beneficiaryInfoBo.getApplyBeneficiaryBo().setCustomerId(userCustomer.getCustomerId());
                            });
                        }
                        beneficiaryPos.add(beneficiaryInfoBo.getApplyBeneficiaryBo());
                    }
                });
            }
        });
        applyInsuredBaseService.addApplyInsured(applyInsuredPos, userId);
        applyBeneficiaryBaseService.addApplyBeneficiaryInfo(beneficiaryInfoPos, userId);
        applyBeneficiaryBaseService.addApplyBeneficiary(beneficiaryPos, userId);

        // 2.保存被保人扩展信息
        List<ApplyInsuredExtendPo> applyInsuredExtendPos = new ArrayList<>();
        applyInsuredPos.forEach(applyInsuredPo -> {
            ApplyInsuredExtendPo applyInsuredExtendPo = new ApplyInsuredExtendPo();
            applyInsuredExtendPo.setInsuredId(applyInsuredPo.getInsuredId());
            applyInsuredExtendPo.setApplyId(applyInsuredPo.getApplyId());
            applyInsuredExtendPo.setAddDate(DateUtils.getCurrentTime());
            applyInsuredExtendPo.setEffectiveDate(DateUtils.getCurrentTime());
            applyInsuredExtendPo.setInsuredStatus(TerminologyConfigEnum.VALID_FLAG.effective.name().toUpperCase());
            applyInsuredExtendPos.add(applyInsuredExtendPo);
        });
        applyInsuredBaseService.saveApplyInsuredExtend(userId, applyInsuredExtendPos);

        // 3.保存被保人险种、责任及档次
        applyCoverageBaseService.addApplyCoverageBos(userId, newCoverageBos);
    }

    /**
     * 处理被保人更新
     *
     * @param updateApplyInsureds 待更新被保人
     * @param userId              用户ID
     */
    public void handleInsuredUpdate(List<ApplyInsuredBo> updateApplyInsureds, String userId) {
        ApplyAgentPo applyAgentPo = applyAgentBaseService.queryApplyAgent(updateApplyInsureds.get(0).getApplyId());
        String agentId = AssertUtils.isNotNull(applyAgentPo) ? applyAgentPo.getAgentId() : userId;

        List<CustomerBusinessRequest> customerBusinessRequests = (List<CustomerBusinessRequest>) this.converterList(
                updateApplyInsureds, new TypeToken<List<CustomerBusinessRequest>>() {
                }.getType()
        );
        customerBusinessRequests.forEach(customerBusinessRequest -> customerBusinessRequest.setUserId(agentId));
        // 保存客户信息
        ResultObject<List<UserCustomerResponse>> listCustomerObject = customerManageApi.saveCustomerMessagePoList(customerBusinessRequests);
        AssertUtils.isResultObjectError(getLogger(), listCustomerObject);
        // 1.更新被保人
        List<ApplyInsuredPo> applyInsuredPos = new ArrayList<>();
        updateApplyInsureds.forEach(applyInsuredBo -> {
            listCustomerObject.getData().stream()
                    .filter(userCustomer -> applyInsuredBo.getIdNo().equals(userCustomer.getIdNo()))
                    .findFirst().ifPresent(userCustomer -> {
                applyInsuredBo.setCustomerId(userCustomer.getCustomerId());
            });
            applyInsuredPos.add(applyInsuredBo);
        });
        // 批量更新被保人
        applyInsuredBaseService.updateApplyInsured(applyInsuredPos, userId);

        // 2.删除原有险种
        List<ApplyCoverageBo> deleteCoverageBos = new ArrayList<>();
        updateApplyInsureds.forEach(insuredBo -> {
            deleteCoverageBos.addAll(insuredBo.getListCoverage());
        });
    }

    private void deleteDutyAndLevel(List<ApplyCoverageBo> deleteCoverageBos) {
        if (AssertUtils.isNotEmpty(deleteCoverageBos)) {
            List<ApplyCoverageDutyPo> coverageDutyPos = new ArrayList<>();
            List<ApplyCoverageLevelPo> coverageLevelPos = new ArrayList<>();
            deleteCoverageBos.forEach(coverageBo -> {
                if (TerminologyConfigEnum.WHETHER.YES.name().equals(coverageBo.getDutyChooseFlag()) &&
                        AssertUtils.isNotEmpty(coverageBo.getListCoverageDuty())) {
                    coverageDutyPos.addAll(coverageBo.getListCoverageDuty());
                    coverageBo.getListCoverageDuty().forEach(coverageDutyBo -> {
                        if (AssertUtils.isNotEmpty(coverageDutyBo.getListCoverageLevel())) {
                            coverageLevelPos.addAll(coverageDutyBo.getListCoverageLevel());
                        }
                    });
                } else if (AssertUtils.isNotEmpty(coverageBo.getListCoverageLevel())) {
                    coverageLevelPos.addAll(coverageBo.getListCoverageLevel());
                }
            });
            // 删除险种原有责任、档次信息
            applyCoverageBaseService.deleteApplyCoverageDuty(coverageDutyPos);
            applyCoverageBaseService.deleteApplyCoverageLevel(coverageLevelPos);
        }
    }

    /**
     * 处理被保人删除
     *
     * @param deleteApplyInsureds 待删除被保人
     */
    public void handleInsuredDelete(List<ApplyInsuredBo> deleteApplyInsureds) {
        // 1.删除被保人、受益人
        List<ApplyInsuredPo> insuredPos = new ArrayList<>();
        List<ApplyBeneficiaryInfoPo> beneficiaryInfoPos = new ArrayList<>();
        List<ApplyBeneficiaryPo> beneficiaryPos = new ArrayList<>();
        deleteApplyInsureds.forEach(insuredBo -> {
            insuredPos.add(insuredBo);
            if (AssertUtils.isNotEmpty(insuredBo.getListBeneficiary())) {
                insuredBo.getListBeneficiary().forEach(beneficiaryInfoBo -> {
                    beneficiaryInfoPos.add(beneficiaryInfoBo);
                    if (AssertUtils.isNotNull(beneficiaryInfoBo.getApplyBeneficiaryBo())) {
                        beneficiaryPos.add(beneficiaryInfoBo.getApplyBeneficiaryBo());
                    }
                });
            }
        });
        applyInsuredBaseService.deleteApplyInsured(insuredPos);
        applyBeneficiaryBaseService.deleteApplyBeneficiaryInfo(beneficiaryInfoPos);
        applyBeneficiaryBaseService.deleteApplyBeneficiary(beneficiaryPos);


        // 2.删除被保人扩展信息
        List<String> insuredIds = deleteApplyInsureds.stream().map(ApplyInsuredBo::getInsuredId).collect(Collectors.toList());
        List<ApplyInsuredExtendPo> applyInsuredExtendPos = applyInsuredBaseService.listApplyInsuredExtend(insuredIds);
        applyInsuredBaseService.deleteApplyInsuredExtend(applyInsuredExtendPos);

        // 3.删除被保人险种
        List<ApplyCoverageBo> deleteCoverageBos = new ArrayList<>();
        deleteApplyInsureds.forEach(insuredBo -> {
            deleteCoverageBos.addAll(insuredBo.getListCoverage());
        });
        if (AssertUtils.isNotEmpty(deleteCoverageBos)) {
            List<ApplyCoveragePo> deleteCoveragePos = (List<ApplyCoveragePo>) this.converterList(
                    deleteCoverageBos, new TypeToken<List<ApplyCoveragePo>>() {
                    }.getType()
            );
            applyCoverageBaseService.deleteApplyCoverage(deleteCoveragePos);
            // 4.删除险种原有责任、档次信息
            this.deleteDutyAndLevel(deleteCoverageBos);
        }

        // 5.删除健康告知
        List<ApplyGroupHealthQuestionnaireAnswerPo> healthAnswerPos = applyBaseService.listApplyGroupHealthQuestionnaireAnswer(insuredIds);
        applyBaseService.deleteApplyGroupHealthQuestionnaireAnswer(healthAnswerPos);
    }

    /**
     * 组装险种信息
     *
     * @param applyCoveragePos 公共险种信息
     * @param applyPo          投保单
     * @param applyInsuredBo   被保人信息
     * @param productId        产品ID
     * @return ApplyCoverageBo
     */
    private ApplyCoverageBo getApplyCoverageBo(List<ApplyCoveragePo> applyCoveragePos, ApplyPo applyPo, ApplyInsuredBo applyInsuredBo, String productId) {
        ApplyCoverageBo coverageBo = new ApplyCoverageBo();
        Optional<ApplyCoveragePo> coverageOptional = applyCoveragePos.stream()
                .filter(applyCoveragePo -> productId.equals(applyCoveragePo.getProductId()))
                .findFirst();
        if (coverageOptional.isPresent()) {
            coverageOptional.ifPresent(applyCoveragePo -> {
                ClazzUtils.copyPropertiesIgnoreNull(applyCoveragePo, coverageBo);
                coverageBo.setInsuredId(applyInsuredBo.getInsuredId());
                coverageBo.setCoverageId(null);
            });
        } else {
            // 查询产品，组装公共险种信息
            // 查询代理人信息
            ApplyAgentPo applyAgentPo = applyAgentBaseService.queryApplyAgent(applyPo.getApplyId());
            ResultObject<AgentSimpleBaseResponse> agentResultObject = baseAgentApi.getUserAgents(applyAgentPo.getAgentId());
            AssertUtils.isResultObjectError(getLogger(), agentResultObject);
            AgentSimpleBaseResponse agentSimpleBaseResponse = agentResultObject.getData();

            // 查询产品信息
            ResultObject<ProductDetailedInfoResponse> resultObject =
                    productApi.getProductDetailInfo(productId, agentSimpleBaseResponse.getBranchId());
            AssertUtils.isResultObjectError(getLogger(), resultObject);
            AssertUtils.isResultObjectDataNull(getLogger(), resultObject, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_PRODUCT_INFO_IS_NOT_FOUNT_OBJECT);

            // 组装险种
            ApplyCoveragePo applyCoveragePo = new ApplyCoveragePo();
            ProductDetailedInfoResponse productDetailedInfoResponse = resultObject.getData();
            if (AssertUtils.isNotNull(productDetailedInfoResponse)) {
                applyCoveragePo.setApplyId(applyPo.getApplyId());
                applyCoveragePo.setApplyNo(applyPo.getApplyNo());
                applyCoveragePo.setProductId(productDetailedInfoResponse.getProductId());
                applyCoveragePo.setProductCode(productDetailedInfoResponse.getProductCode());
                applyCoveragePo.setProductName(productDetailedInfoResponse.getProductName());
                applyCoveragePo.setPrimaryFlag(productDetailedInfoResponse.getMainProductFlag());
                applyCoveragePo.setOriginalPremium(new BigDecimal(
                        AssertUtils.isNotEmpty(productDetailedInfoResponse.getMinPrice()) ? productDetailedInfoResponse.getMinPrice() : "0"));
                applyCoveragePo.setDutyChooseFlag(productDetailedInfoResponse.isDutyChooseFlag() ? TerminologyConfigEnum.WHETHER.YES.name() : TerminologyConfigEnum.WHETHER.NO.name());
                applyCoveragePos.add(applyCoveragePo);
            }
            ClazzUtils.copyPropertiesIgnoreNull(applyCoveragePo, coverageBo);
        }
        coverageBo.setInsuredId(applyInsuredBo.getInsuredId());
        coverageBo.setCoverageId(null);
        return coverageBo;
    }

    /**
     * 从表格获取被保人上传数据
     *
     * @param row 行数据
     */
    public ApplyInsuredUploadPo getInsuredUploadData(Row row) {
        int c = 1;
        ApplyInsuredUploadPo insuredUploadPo = new ApplyInsuredUploadPo();
        insuredUploadPo.setInsuredName(this.getCellData(row, c++));
        insuredUploadPo.setIdType(this.getCellData(row, c++));
        insuredUploadPo.setIdNo(this.getCellData(row, c++));
        insuredUploadPo.setBirthday(this.getDateCellData(row, c++));
        insuredUploadPo.setSex(this.getCellData(row, c++));
        insuredUploadPo.setMobile(this.getCellData(row, c++));
        insuredUploadPo.setHealthAnswer(this.getCellData(row, c++));
        insuredUploadPo.setOccupationCode(this.getCellData(row, c++));
        insuredUploadPo.setPremuimOnePlus(getCellData(row, c++, true));
        insuredUploadPo.setAmountOnePlus(getCellData(row, c++, true));
        insuredUploadPo.setMultOnePlusA(getCellData(row, c++));
        insuredUploadPo.setMultOnePlusB(getCellData(row, c++));
        insuredUploadPo.setMultOnePlusC(getCellData(row, c++));
        insuredUploadPo.setPremuimEleven(getCellData(row, c++, true));
        insuredUploadPo.setProductLevelEleven(getCellData(row, c++));
        insuredUploadPo.setMultEleven(getCellData(row, c++));
        insuredUploadPo.setAmountDengueFever_1(getCellData(row, c++, true));
        insuredUploadPo.setAmountDengueFever_2(getCellData(row, c++, true));
        insuredUploadPo.setAmountDengueFever_3(getCellData(row, c++, true));
        insuredUploadPo.setPremuimSevenPlus(getCellData(row, c++, true));
        insuredUploadPo.setProductLevelSevenPlus(getCellData(row, c++));
        insuredUploadPo.setMultSevenPlus(getCellData(row, c++));
        insuredUploadPo.setDailyAllowanceSevenPlus(getCellData(row, c++, true));
        insuredUploadPo.setPlanTwelveClinic(getCellData(row, c++));
        insuredUploadPo.setMultTwelveClinic(getCellData(row, c++));
        insuredUploadPo.setPremuimTwelveClinic(getCellData(row, c++, true));
        insuredUploadPo.setPlanTwelveHospital(getCellData(row, c++));
        insuredUploadPo.setMultTwelveHospital(getCellData(row, c++));
        insuredUploadPo.setPremuimTwelveHospital(getCellData(row, c++, true));
        insuredUploadPo.setPlanTwelveTransfer(getCellData(row, c++));
        insuredUploadPo.setMultTwelveTransfer(getCellData(row, c++));
        insuredUploadPo.setPremuimTwelveTransfer(getCellData(row, c++, true));
        insuredUploadPo.setTotalPremuimTwelve(getCellData(row, c++, true));
        return insuredUploadPo;
    }

    /**
     * 从表格获取受益人上传数据
     *
     * @param row              行数据
     * @param lastCellNum
     * @param insuredUploadId
     * @param beneficiaryIndex 被保人信息开始下标
     */
    public List<ApplyBeneficiaryUploadPo> getBeneficiaryUploadData(Row row, int lastCellNum, String insuredUploadId, int beneficiaryIndex) {
        List<ApplyBeneficiaryUploadPo> beneficiaryUploadPos = new ArrayList<>();
        int c = beneficiaryIndex;
        long index = 1;
        while (c <= lastCellNum) {
            ApplyBeneficiaryUploadPo beneficiaryUploadPo = new ApplyBeneficiaryUploadPo();
            beneficiaryUploadPo.setBeneficiaryNoOrder(this.getCellData(row, c++));
            beneficiaryUploadPo.setName(this.getCellData(row, c++));
            beneficiaryUploadPo.setSex(this.getCellData(row, c++));
            beneficiaryUploadPo.setBeneficiaryProportion(this.getCellData(row, c++, true));
            beneficiaryUploadPo.setBirthday(this.getDateCellData(row, c++));
            beneficiaryUploadPo.setRelationship(this.getCellData(row, c++));
            beneficiaryUploadPo.setIdType(this.getCellData(row, c++));
            beneficiaryUploadPo.setIdNo(this.getCellData(row, c++));
            beneficiaryUploadPo.setIdExpDate(this.getDateCellData(row, c++));
            if (isNotNullOfBeneficiary(beneficiaryUploadPo)) {
                beneficiaryUploadPo.setInsuredUploadId(insuredUploadId);
                beneficiaryUploadPo.setBeneficiaryIndex(index);
                beneficiaryUploadPos.add(beneficiaryUploadPo);
            }
            index++;
        }
        return beneficiaryUploadPos;
    }

    /**
     * 从表格获取受益人上传数据
     *
     * @param row              行数据
     * @param lastCellNum
     * @param insuredUploadId
     * @param beneficiaryIndex 被保人信息开始下标
     */
    public List<ApplyBeneficiaryUploadPo> getBeneficiaryUploadData29(Row row, int lastCellNum, String insuredUploadId, int beneficiaryIndex) {
        List<ApplyBeneficiaryUploadPo> beneficiaryUploadPos = new ArrayList<>();
        int c = beneficiaryIndex;
        long index = 1;
        while (c <= lastCellNum) {
            ApplyBeneficiaryUploadPo beneficiaryUploadPo = new ApplyBeneficiaryUploadPo();
            beneficiaryUploadPo.setName(this.getCellData(row, c++));
            beneficiaryUploadPo.setSex(this.getCellData(row, c++));
            beneficiaryUploadPo.setRelationship(this.getCellData(row, c++));
            beneficiaryUploadPo.setBeneficiaryProportion(this.getCellData(row, c++, true));
            if (isNotNullOfBeneficiary(beneficiaryUploadPo)) {
                beneficiaryUploadPo.setBeneficiaryNoOrder(ApplyTermEnum.BENEFICIARY_NO.ORDER_ONE.value());
                beneficiaryUploadPo.setInsuredUploadId(insuredUploadId);
                beneficiaryUploadPo.setBeneficiaryIndex(index);
                beneficiaryUploadPos.add(beneficiaryUploadPo);
            }
            index++;
        }
        return beneficiaryUploadPos;
    }

    /**
     * 从表格获取被保人上传数据
     *
     * @param sheet1 行数据
     */
    public ApplyContactUploadPo get17ContactUploadData(XSSFSheet sheet1) {
        int rowNum = 0;
        ApplyContactUploadPo applyContactUploadPo = new ApplyContactUploadPo();
        applyContactUploadPo.setAgentName(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setAgentCode(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setAgentMobile(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setCompanyName(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setCompanyIdNo(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setTaxRegistrationNo(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setCompanyAddressWhole(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setCompanyType(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setCompanyPhone(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setCompanyMobile(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setCompanyContractName(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setCompanyContractPosition(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setCompanyContractOfficeNumber(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setCompanyContractMobile(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setCompanyContractEmail(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setGcGroupCareNum(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setGmrNum(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setTotalEmployeeNum(this.getCellData(sheet1.getRow(rowNum++), 1));
        rowNum++;
        applyContactUploadPo.setMultipleOfCurrentSalary(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setRankAndPositionInTheCompany(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setFlatSumInsuredForAllEmployees(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setYearsOfServiceWithTheCompany(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setOtherCategorySpecify(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setDiscountType(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setSpecialDiscount(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setBasicFreeCoverLimit(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setCiFreeCoverLimit(this.getCellData(sheet1.getRow(rowNum++), 1));
        return applyContactUploadPo;
    }

    /**
     * 从表格获取被保人上传数据
     *
     * @param sheet1 行数据
     */
    public ApplyContactUploadPo get29ContactUploadData(XSSFSheet sheet1) {
        int rowNum = 1;
        ApplyContactUploadPo applyContactUploadPo = new ApplyContactUploadPo();
        applyContactUploadPo.setAgentName(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setAgentCode(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setAgentMobile(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setCompanyName(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setCompanyIdNo(this.getCellData(sheet1.getRow(rowNum++), 1));
        rowNum++;
        applyContactUploadPo.setCompanyAddressWhole(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setSalesPlan(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setSchoolProperties(this.getCellData(sheet1.getRow(rowNum++), 1));

        // PRE_SCHOOL幼儿园, PRIMARY小学, SECONDARY初中/高中, UNIVERSITY/大学
        List<String> schoolType = new ArrayList<>();
        if ("Yes".equals(this.getCellData(sheet1.getRow(rowNum++), 1))) {
            schoolType.add(ApplyTermEnum.SCHOOL_TYPE.PRE_SCHOOL.name());
        }
        if ("Yes".equals(this.getCellData(sheet1.getRow(rowNum++), 1))) {
            schoolType.add(ApplyTermEnum.SCHOOL_TYPE.PRIMARY.name());
        }
        if ("Yes".equals(this.getCellData(sheet1.getRow(rowNum++), 1))) {
            schoolType.add(ApplyTermEnum.SCHOOL_TYPE.SECONDARY.name());
        }
        if ("Yes".equals(this.getCellData(sheet1.getRow(rowNum++), 1))) {
            schoolType.add(ApplyTermEnum.SCHOOL_TYPE.UNIVERSITY.name());
        }
        if (AssertUtils.isNotEmpty(schoolType)) {
            applyContactUploadPo.setSchoolType(JSON.toJSONString(schoolType));
        }
        applyContactUploadPo.setPreSchoolNum(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setPrimaryNum(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setSecondaryNum(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setUniversityNum(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setPreSchoolAmount(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setPrimaryAmount(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setSecondaryAmount(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setUniversityAmount(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setParticipationNum(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setStudentsNum(this.getCellData(sheet1.getRow(rowNum++), 1));
        try {
            applyContactUploadPo.setParticipationPercentage(this.getCellData4Decimal(sheet1.getRow(rowNum++), 1, true));
        } catch (Exception e) {
            getLogger().info("百分比为空/错误");
        }
        applyContactUploadPo.setDelegateName(this.getCellData(sheet1.getRow(rowNum++), 1));
        String delegateBirthday = this.getDateCellData(sheet1.getRow(rowNum++), 1);
        if (AssertUtils.isNotEmpty(delegateBirthday) && !AssertUtils.isDateFormat(delegateBirthday, DateUtils.FORMATE18)) {
            throwsException(APPLY_PRO29_DELEGATE_BIRTHDAY_FORMAT_ERROR);
        }
        applyContactUploadPo.setDelegateBirthday(delegateBirthday);
        applyContactUploadPo.setDelegateIdType(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setDelegateIdNo(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setDelegateNationality(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setDelegateSex(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setDelegatePosition(this.getCellData(sheet1.getRow(rowNum++), 1));
        String delegateMobile = this.getCellData(sheet1.getRow(rowNum++), 1);
        if (AssertUtils.isNotEmpty(delegateMobile)) {
            AssertUtils.isNotPureDigital(this.getLogger(), delegateMobile, ApplyErrorConfigEnum.APPLY_PRO29_DELEGATE_MOBILE_FORMAT_ERROR);
        }
        applyContactUploadPo.setDelegateMobile(delegateMobile);
        String delegateEmail = this.getCellData(sheet1.getRow(rowNum++), 1);
        if (AssertUtils.isNotEmpty(delegateEmail)) {
            AssertUtils.isEmail(this.getLogger(), delegateEmail, APPLY_PRO29_DELEGATE_EMAIL_FORMAT_ERROR);
        }
        applyContactUploadPo.setDelegateEmail(delegateEmail);
        applyContactUploadPo.setDiscountType(this.getCellData(sheet1.getRow(rowNum++), 1));
        applyContactUploadPo.setSpecialDiscount(this.getCellData(sheet1.getRow(rowNum++), 1));
        return applyContactUploadPo;
    }

    /**
     * 从表格获取被保人上传数据
     *
     * @param row 行数据
     */
    public ApplyInsuredUploadPo getInsuredUploadData17(Row row) {
        int c = 1;
        ApplyInsuredUploadPo insuredUploadPo = new ApplyInsuredUploadPo();
        insuredUploadPo.setInsuredName(this.getCellData(row, c++));
        insuredUploadPo.setIdNo(this.getCellData(row, c++));
        insuredUploadPo.setIdType(this.getCellData(row, c++));
        insuredUploadPo.setSex(this.getCellData(row, c++));
        insuredUploadPo.setBirthday(this.getDateCellData(row, c++));
        insuredUploadPo.setMobile(this.getCellData(row, c++));
        insuredUploadPo.setInsuredType(this.getCellData(row, c++));
        insuredUploadPo.setHealthAnswer(this.getCellData(row, c++));
        insuredUploadPo.setOccupationDuty(this.getCellData(row, c++));
        insuredUploadPo.setOccupationCode(this.getCellData(row, c++));
        insuredUploadPo.setOccupationClass(this.getCellData(row, c++));

        insuredUploadPo.setSeventeenTlPremium(getCellData(row, c++, true));
        insuredUploadPo.setSeventeenTlAmount(getCellData(row, c++, true));
        insuredUploadPo.setSeventeenTlMult("1");
        insuredUploadPo.setSeventeenTaPremium(getCellData(row, c++, true));
        insuredUploadPo.setSeventeenTaAmount(getCellData(row, c++, true));
        insuredUploadPo.setSeventeenTaMult("1");

        insuredUploadPo.setSeventeenTcPremium(getCellData(row, c++, true));
        insuredUploadPo.setSeventeenTcAmount(getCellData(row, c++, true));
        insuredUploadPo.setSeventeenTcMult("1");

        insuredUploadPo.setSeventeenTsPremium(getCellData(row, c++, true));
        insuredUploadPo.setSeventeenTsAmount(getCellData(row, c++, true));
        insuredUploadPo.setSeventeenTsMult("1");

        insuredUploadPo.setEighteenPremium(getCellData(row, c++, true));
        insuredUploadPo.setEighteenLevel(getCellData(row, c++));
        insuredUploadPo.setEighteenMult("1");

        insuredUploadPo.setTwentysixPremium(getCellData(row, c++, true));
        insuredUploadPo.setTwentysixAmount(getCellData(row, c++, true));
        insuredUploadPo.setTwentysixMult("1");

        insuredUploadPo.setTwentysevenPremium(getCellData(row, c++, true));
        insuredUploadPo.setTwentysevenLevel(getCellData(row, c++));
        insuredUploadPo.setTwentysevenMult("1");

        insuredUploadPo.setThirtythreePremium(this.getCellData(row, c++, true));
        insuredUploadPo.setThirtythreeAmount(this.getCellData(row, c++, true));
        insuredUploadPo.setThirtythreeMult("1");
        return insuredUploadPo;
    }

    /**
     * 从表格获取被保人上传数据
     *
     * @param row 行数据
     */
    public ApplyInsuredUploadPo getInsuredUploadData29(Row row) {
        int c = 1;
        ApplyInsuredUploadPo insuredUploadPo = new ApplyInsuredUploadPo();
        insuredUploadPo.setInsuredName(this.getCellData(row, c++));
        insuredUploadPo.setIdType("STUDENT_CODE");
        insuredUploadPo.setOccupationCode("GC-1001004");
        insuredUploadPo.setIdNo(this.getCellData(row, c++));
        insuredUploadPo.setBirthday(this.getDateCellData(row, c++));
        insuredUploadPo.setAge(this.getCellData(row, c++));
        insuredUploadPo.setSex(this.getCellData(row, c++));
        insuredUploadPo.setNationality(this.getCellData(row, c++));
        insuredUploadPo.setMobile(this.getCellData(row, c++));
        insuredUploadPo.setTwentynineLevel(this.getCellData(row, c++));
        insuredUploadPo.setTwentynineAmount(this.getCellData(row, c++, true));
        insuredUploadPo.setTwentyninePremium(this.getCellData(row, c++, true));
        insuredUploadPo.setThirtythreeAmount(this.getCellData(row, c++, true));
        insuredUploadPo.setThirtythreePremium(this.getCellData(row, c++, true));
        insuredUploadPo.setHealthAnswer(this.getCellData(row, c++));
        insuredUploadPo.setHealthAnswerRemark(this.getCellData(row, c++));
        insuredUploadPo.setOccupationClass(this.getCellData(row, c++));
        return insuredUploadPo;
    }

    /**
     * 受益人信息是否为空
     *
     * @param beneficiaryUploadPo 受益人信息
     * @return
     */
    private boolean isNotNullOfBeneficiary(ApplyBeneficiaryUploadPo beneficiaryUploadPo) {
        return AssertUtils.isNotEmpty(beneficiaryUploadPo.getBeneficiaryNoOrder())
                || AssertUtils.isNotEmpty(beneficiaryUploadPo.getName())
                || AssertUtils.isNotEmpty(beneficiaryUploadPo.getSex())
                || AssertUtils.isNotEmpty(beneficiaryUploadPo.getBeneficiaryProportion())
                || AssertUtils.isNotEmpty(beneficiaryUploadPo.getBirthday())
                || AssertUtils.isNotEmpty(beneficiaryUploadPo.getRelationship())
                || AssertUtils.isNotEmpty(beneficiaryUploadPo.getIdType())
                || AssertUtils.isNotEmpty(beneficiaryUploadPo.getIdNo())
                || AssertUtils.isNotEmpty(beneficiaryUploadPo.getIdExpDate());
    }

    /**
     * 获取表格数据
     *
     * @param row   行
     * @param i     第几列
     * @param isNum 是否是数字
     * @return
     */
    private String getCellData(Row row, int i, boolean isNum) {
        if (!AssertUtils.isNotNull(row) ||
                !AssertUtils.isNotNull(row.getCell(i)) ||
                !AssertUtils.isNotEmpty(row.getCell(i).toString().trim())) {
            return null;
        }

        Cell cell = row.getCell(i);
        int cellType = cell.getCellType();

        // 处理数值或公式类型
        if (cellType == HSSFCell.CELL_TYPE_NUMERIC || cellType == HSSFCell.CELL_TYPE_FORMULA) {
            try {
                DecimalFormat df = new DecimalFormat("0");
                if (isNum) {
                    df = new DecimalFormat("0.00");
                }
                return df.format(cell.getNumericCellValue());
            } catch (IllegalStateException e) {
                // 如果无法获取数值（例如公式结果是文本），返回字符串
                return cell.toString().trim();
            }
        }
        // 处理字符串类型
        else if (cellType == HSSFCell.CELL_TYPE_STRING) {
            String cellValue = cell.getStringCellValue().trim();
            if (isNum) {
                try {
                    // 尝试将字符串解析为数字
                    double numericValue = Double.parseDouble(cellValue);
                    DecimalFormat df = new DecimalFormat("0.00");
                    return df.format(numericValue);
                } catch (NumberFormatException e) {
                    // 如果无法解析为数字，返回原字符串
                    return cellValue;
                }
            }
            return cellValue;
        }
        // 其他类型（布尔、空白等）
        return cell.toString().trim();
    }

    /**
     * 获取表格数据
     *
     * @param row   行
     * @param i     第几列
     * @param isNum 是否是数字
     * @return
     */
    private String getCellData4Decimal(Row row, int i, boolean isNum) {
        if (!AssertUtils.isNotNull(row) ||
                !AssertUtils.isNotNull(row.getCell(i)) ||
                !AssertUtils.isNotEmpty(row.getCell(i).toString().trim())) {
            return null;
        }
        if (row.getCell(i).getCellType() == HSSFCell.CELL_TYPE_NUMERIC || row.getCell(i).getCellType() == HSSFCell.CELL_TYPE_FORMULA) {
            DecimalFormat df = new DecimalFormat("0");
            if (isNum) {
                df = new DecimalFormat("0.0000");
            }
            return df.format(row.getCell(i).getNumericCellValue());
        }
        return row.getCell(i).toString().trim();
    }

    /**
     * 获取表格数据
     *
     * @param row   行
     * @param i     第几列
     * @return
     */
    private String getDateCellData(Row row, int i) {
        if (!AssertUtils.isNotNull(row) ||
                !AssertUtils.isNotNull(row.getCell(i)) ||
                !AssertUtils.isNotEmpty(row.getCell(i).toString().trim())) {
            return null;
        }
        if (row.getCell(i).getCellType() == HSSFCell.CELL_TYPE_NUMERIC) {
            // 是数字类型但非数字标识，可能是Excel数字格式的日期，需要转换成日期格式
            double val = row.getCell(i).getNumericCellValue();
            Date date = HSSFDateUtil.getJavaDate(val);
            return DateUtils.dateToString(date, DateUtils.FORMATE18);
        }
        return row.getCell(i).toString().trim();
    }

    /**
     * 获取表格数据
     *
     * @param row 行
     * @param i   第几列
     * @return
     */
    public String getCellData(Row row, int i) {
        return getCellData(row, i, false);
    }

    /**
     * 设置表头
     *
     * @param sheet             表格
     * @param maxBeneficiaryNum 最大受益人数
     * @param templateColumn    模板已有列数
     */
    public void setHeadTitle(XSSFSheet sheet, long maxBeneficiaryNum, int templateColumn) {
        for (int r = 1; r <= 2; r++) {
            for (int c = 0; c < 9 * (maxBeneficiaryNum - 3); c++) {
                Row titleRow = sheet.getRow(r);
                titleRow.createCell(c + templateColumn);
                if (r == 2 || c % 9 == 0) {
                    titleRow.getCell(c + templateColumn).setCellStyle(titleRow.getCell(c + templateColumn - 9).getCellStyle());
                }
                if (r == 1 && c % 9 == 0) {
                    String cellValue = "受益人信息 " + (c / 9 + 4) + "\nBeneficiary Info";
                    titleRow.getCell(c + templateColumn).setCellValue(cellValue);
                }
                if (r == 2) {
                    titleRow.getCell(c + templateColumn).setCellValue(titleRow.getCell(c + templateColumn - 9).getStringCellValue());
                }
            }
            if (r == 1 && maxBeneficiaryNum > 3) {
                for (int i = 3; i < maxBeneficiaryNum; i++) {
                    // 合并单元格
                    sheet.addMergedRegion(new CellRangeAddress(1, 1, templateColumn + (i - 3) * 9, templateColumn + (i - 3) * 9 + 8));
                }
            }
        }
    }

    /**
     * 设置行数据
     *
     * @param insuredUploadBo   被保人上传数据
     * @param row               行
     * @param maxBeneficiaryNum
     * @param templateColumn
     */
    public void setRowData(ApplyInsuredUploadBo insuredUploadBo, Row row, long maxBeneficiaryNum, int templateColumn) {
        int c = 1;
        for (int i = 1; i < templateColumn + (maxBeneficiaryNum - 3) * 9; i++) {
            row.createCell(i);
        }
        row.getCell(c++).setCellValue(insuredUploadBo.getInsuredName());
        row.getCell(c++).setCellValue(insuredUploadBo.getIdType());
        row.getCell(c++).setCellValue(insuredUploadBo.getIdNo());
        row.getCell(c++).setCellValue(insuredUploadBo.getBirthday());
        row.getCell(c++).setCellValue(insuredUploadBo.getSex());
        row.getCell(c++).setCellValue(insuredUploadBo.getMobile());
        row.getCell(c++).setCellValue(insuredUploadBo.getHealthAnswer());
        row.getCell(c++).setCellValue(insuredUploadBo.getOccupationCode());
        row.getCell(c++).setCellValue(insuredUploadBo.getPremuimOnePlus());
        row.getCell(c++).setCellValue(insuredUploadBo.getAmountOnePlus());
        row.getCell(c++).setCellValue(insuredUploadBo.getMultOnePlusA());
        row.getCell(c++).setCellValue(insuredUploadBo.getMultOnePlusB());
        row.getCell(c++).setCellValue(insuredUploadBo.getMultOnePlusC());
        row.getCell(c++).setCellValue(insuredUploadBo.getPremuimEleven());
        row.getCell(c++).setCellValue(insuredUploadBo.getProductLevelEleven());
        row.getCell(c++).setCellValue(insuredUploadBo.getMultEleven());
        row.getCell(c++).setCellValue(insuredUploadBo.getAmountDengueFever_1());
        row.getCell(c++).setCellValue(insuredUploadBo.getAmountDengueFever_2());
        row.getCell(c++).setCellValue(insuredUploadBo.getAmountDengueFever_3());
        row.getCell(c++).setCellValue(insuredUploadBo.getPremuimSevenPlus());
        row.getCell(c++).setCellValue(insuredUploadBo.getProductLevelSevenPlus());
        row.getCell(c++).setCellValue(insuredUploadBo.getMultSevenPlus());
        row.getCell(c++).setCellValue(insuredUploadBo.getDailyAllowanceSevenPlus());
        row.getCell(c++).setCellValue(insuredUploadBo.getPlanTwelveClinic());
        row.getCell(c++).setCellValue(insuredUploadBo.getMultTwelveClinic());
        row.getCell(c++).setCellValue(insuredUploadBo.getPremuimTwelveClinic());
        row.getCell(c++).setCellValue(insuredUploadBo.getPlanTwelveHospital());
        row.getCell(c++).setCellValue(insuredUploadBo.getMultTwelveHospital());
        row.getCell(c++).setCellValue(insuredUploadBo.getPremuimTwelveHospital());
        row.getCell(c++).setCellValue(insuredUploadBo.getPlanTwelveTransfer());
        row.getCell(c++).setCellValue(insuredUploadBo.getMultTwelveTransfer());
        row.getCell(c++).setCellValue(insuredUploadBo.getPremuimTwelveTransfer());
        row.getCell(c++).setCellValue(insuredUploadBo.getTotalPremuimTwelve());
        row.getCell(c++).setCellValue(insuredUploadBo.getCheckResultMsg());
        if (AssertUtils.isNotEmpty(insuredUploadBo.getListBeneficiaryUpload())) {
            for (ApplyBeneficiaryUploadPo beneficiaryUploadPo : insuredUploadBo.getListBeneficiaryUpload()) {
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getBeneficiaryNoOrder());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getName());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getSex());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getBeneficiaryProportion());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getBirthday());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getRelationship());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getIdType());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getIdNo());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getIdExpDate());
            }
        }
    }

    /**
     * 设置行数据
     *
     * @param applyContactUploadPo 合同上传数据
     * @param sheet                工作表
     */
    public void setContactData17(ApplyContactUploadPo applyContactUploadPo, XSSFSheet sheet) {
        int i = 0;
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getAgentName());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getAgentCode());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getAgentMobile());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCompanyName());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCompanyIdNo());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getTaxRegistrationNo());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCompanyAddressWhole());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCompanyType());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCompanyPhone());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCompanyMobile());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCompanyContractName());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCompanyContractPosition());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCompanyContractOfficeNumber());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCompanyContractMobile());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCompanyContractEmail());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getGcGroupCareNum());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getGmrNum());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getTotalEmployeeNum());
        i++;
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getMultipleOfCurrentSalary());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getRankAndPositionInTheCompany());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getFlatSumInsuredForAllEmployees());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getYearsOfServiceWithTheCompany());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getOtherCategorySpecify());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getDiscountType());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getSpecialDiscount());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getBasicFreeCoverLimit());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCiFreeCoverLimit());
    }

    /**
     * 设置行数据
     *
     * @param applyContactUploadPo 合同上传数据
     * @param sheet                工作表
     * @param workbook
     */
    public void setContactData29(ApplyContactUploadPo applyContactUploadPo, XSSFSheet sheet, XSSFWorkbook workbook) {
        int i = 1;
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getAgentName());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getAgentCode());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getAgentMobile());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCompanyName());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCompanyIdNo());
        i++;
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getCompanyAddressWhole());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getSalesPlan());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getSchoolProperties());
        sheet.getRow(i++).getCell(1).setCellValue(AssertUtils.isNotEmpty(applyContactUploadPo.getSchoolType()) ? (applyContactUploadPo.getSchoolType().contains(ApplyTermEnum.SCHOOL_TYPE.PRE_SCHOOL.name()) ? "Yes" : "No") : null);
        sheet.getRow(i++).getCell(1).setCellValue(AssertUtils.isNotEmpty(applyContactUploadPo.getSchoolType()) ? (applyContactUploadPo.getSchoolType().contains(ApplyTermEnum.SCHOOL_TYPE.PRIMARY.name()) ? "Yes" : "No") : null);
        sheet.getRow(i++).getCell(1).setCellValue(AssertUtils.isNotEmpty(applyContactUploadPo.getSchoolType()) ? (applyContactUploadPo.getSchoolType().contains(ApplyTermEnum.SCHOOL_TYPE.SECONDARY.name()) ? "Yes" : "No") : null);
        sheet.getRow(i++).getCell(1).setCellValue(AssertUtils.isNotEmpty(applyContactUploadPo.getSchoolType()) ? (applyContactUploadPo.getSchoolType().contains(ApplyTermEnum.SCHOOL_TYPE.UNIVERSITY.name()) ? "Yes" : "No") : null);
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getPreSchoolNum());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getPrimaryNum());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getSecondaryNum());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getUniversityNum());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getPreSchoolAmount());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getPrimaryAmount());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getSecondaryAmount());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getUniversityAmount());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getParticipationNum());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getStudentsNum());
        if (AssertUtils.isNotEmpty(applyContactUploadPo.getParticipationPercentage()) && applyContactUploadPo.getParticipationPercentage().contains(".")) {
            sheet.getRow(i++).getCell(1).setCellValue(new BigDecimal(applyContactUploadPo.getParticipationPercentage()).multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString() + "%");
        } else{
            int participationPercentageRowNum = i++;
        }
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getDelegateName());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getDelegateBirthday());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getDelegateIdType());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getDelegateIdNo());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getDelegateNationality());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getDelegateSex());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getDelegatePosition());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getDelegateMobile());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getDelegateEmail());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getDiscountType());
        sheet.getRow(i++).getCell(1).setCellValue(applyContactUploadPo.getSpecialDiscount());
    }

    /**
     * 设置行数据
     *
     * @param insuredUploadBo   被保人上传数据
     * @param row               行
     * @param maxBeneficiaryNum
     * @param templateColumn
     */
    public void setRowDataV2021(ApplyInsuredUploadBo insuredUploadBo, Row row, long maxBeneficiaryNum, int templateColumn) {
        int c = 1;
        for (int i = 1; i < templateColumn + (maxBeneficiaryNum - 3) * 9; i++) {
            row.createCell(i);
        }
        row.getCell(c++).setCellValue(insuredUploadBo.getInsuredName());
        row.getCell(c++).setCellValue(insuredUploadBo.getIdNo());
        row.getCell(c++).setCellValue(insuredUploadBo.getIdType());
        row.getCell(c++).setCellValue(insuredUploadBo.getSex());
        row.getCell(c++).setCellValue(insuredUploadBo.getBirthday());
        row.getCell(c++).setCellValue(insuredUploadBo.getMobile());
        row.getCell(c++).setCellValue(insuredUploadBo.getInsuredType());
        row.getCell(c++).setCellValue(insuredUploadBo.getHealthAnswer());
        row.getCell(c++).setCellValue(insuredUploadBo.getOccupationDuty());
        row.getCell(c++).setCellValue(insuredUploadBo.getOccupationCode());
        row.getCell(c++).setCellValue(insuredUploadBo.getOccupationClass());
        row.getCell(c++).setCellValue(insuredUploadBo.getSeventeenTlPremium());
        row.getCell(c++).setCellValue(insuredUploadBo.getSeventeenTlAmount());
        row.getCell(c++).setCellValue(insuredUploadBo.getSeventeenTaPremium());
        row.getCell(c++).setCellValue(insuredUploadBo.getSeventeenTaAmount());
        row.getCell(c++).setCellValue(insuredUploadBo.getSeventeenTcPremium());
        row.getCell(c++).setCellValue(insuredUploadBo.getSeventeenTcAmount());
        row.getCell(c++).setCellValue(insuredUploadBo.getSeventeenTsPremium());
        row.getCell(c++).setCellValue(insuredUploadBo.getSeventeenTsAmount());
        row.getCell(c++).setCellValue(insuredUploadBo.getEighteenPremium());
        row.getCell(c++).setCellValue(insuredUploadBo.getEighteenLevel());
        row.getCell(c++).setCellValue(insuredUploadBo.getTwentysixPremium());
        row.getCell(c++).setCellValue(insuredUploadBo.getTwentysixAmount());
        row.getCell(c++).setCellValue(insuredUploadBo.getTwentysevenPremium());
        row.getCell(c++).setCellValue(insuredUploadBo.getTwentysevenLevel());
        row.getCell(c++).setCellValue(insuredUploadBo.getThirtythreePremium());
        row.getCell(c++).setCellValue(insuredUploadBo.getThirtythreeAmount());
        row.getCell(c++).setCellValue(insuredUploadBo.getCheckResultMsg());

        if (AssertUtils.isNotEmpty(insuredUploadBo.getListBeneficiaryUpload())) {
            for (ApplyBeneficiaryUploadPo beneficiaryUploadPo : insuredUploadBo.getListBeneficiaryUpload()) {
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getBeneficiaryNoOrder());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getName());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getSex());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getBeneficiaryProportion());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getBirthday());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getRelationship());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getIdType());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getIdNo());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getIdExpDate());
            }
        }
    }

    /**
     * 设置行数据
     *
     * @param insuredUploadBo   被保人上传数据
     * @param row               行
     * @param maxBeneficiaryNum
     * @param templateColumn
     */
    public void setRowData29(ApplyInsuredUploadBo insuredUploadBo, Row row, long maxBeneficiaryNum, int templateColumn) {
        int c = 1;
//        for (int i = 1; i < templateColumn + (maxBeneficiaryNum - 3) * 9; i++) {
//            row.createCell(i);
//        }
        row.getCell(c++).setCellValue(insuredUploadBo.getInsuredName());
        row.getCell(c++).setCellValue(insuredUploadBo.getIdNo());
        row.getCell(c++).setCellValue(insuredUploadBo.getBirthday());
        row.getCell(c++).setCellValue(insuredUploadBo.getAge());
        row.getCell(c++).setCellValue(insuredUploadBo.getSex());
        row.getCell(c++).setCellValue(insuredUploadBo.getNationality());
        row.getCell(c++).setCellValue(insuredUploadBo.getMobile());
        row.getCell(c++).setCellValue(insuredUploadBo.getTwentynineLevel());
        row.getCell(c++).setCellValue(insuredUploadBo.getTwentynineAmount());
        row.getCell(c++).setCellValue(insuredUploadBo.getTwentyninePremium());
        row.getCell(c++).setCellValue(insuredUploadBo.getThirtythreeAmount());
        row.getCell(c++).setCellValue(insuredUploadBo.getThirtythreePremium());
        row.getCell(c++).setCellValue(insuredUploadBo.getHealthAnswer());
        row.getCell(c++).setCellValue(insuredUploadBo.getHealthAnswerRemark());
        row.getCell(c++).setCellValue(insuredUploadBo.getOccupationClass());
        row.getCell(c++).setCellValue(insuredUploadBo.getCheckResultMsg());

        if (AssertUtils.isNotEmpty(insuredUploadBo.getListBeneficiaryUpload())) {
            for (ApplyBeneficiaryUploadPo beneficiaryUploadPo : insuredUploadBo.getListBeneficiaryUpload()) {
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getName());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getSex());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getRelationship());
                row.getCell(c++).setCellValue(beneficiaryUploadPo.getBeneficiaryProportion());
            }
        }
    }

    /**
     * 组装被保人清单完成数据
     *
     * @param applyInsuredUploadBos 被保人上传数据
     * @param beneficiaryUploadPos
     * @return
     */
    public List<ApplyInsuredDoneBo> transApplyInsuredDone(List<ApplyInsuredUploadBo> applyInsuredUploadBos, List<ApplyBeneficiaryUploadPo> beneficiaryUploadPos) {
        List<ApplyInsuredDoneBo> insuredDoneBos = new ArrayList<>();
        applyInsuredUploadBos.stream()
                .filter(uploadBo -> TerminologyConfigEnum.WHETHER.YES.name().equals(uploadBo.getCheckResult()))
                .forEach(uploadBo -> {
                    ApplyInsuredDoneBo insuredDoneBo = new ApplyInsuredDoneBo();
                    ClazzUtils.copyPropertiesIgnoreNull(uploadBo, insuredDoneBo);
                    insuredDoneBo.setIdNo(uploadBo.getIdNo().replaceAll("\\s*", ""));
                    insuredDoneBo.setBirthday(DateUtils.stringToTime(uploadBo.getBirthday(), DateUtils.FORMATE18));
                    insuredDoneBo.setSex(uploadBo.getSex().toUpperCase());
                    if (AssertUtils.isNotEmpty(uploadBo.getInsuredType())) {
                        insuredDoneBo.setInsuredType(uploadBo.getInsuredType().toUpperCase());
                    }
                    insuredDoneBo.setPremuimOnePlus(transBigDecimal(uploadBo.getPremuimOnePlus()));
                    insuredDoneBo.setAmountOnePlus(transBigDecimal(uploadBo.getAmountOnePlus()));
                    insuredDoneBo.setPremuimEleven(transBigDecimal(uploadBo.getPremuimEleven()));
                    insuredDoneBo.setAmountDengueFever_1(transBigDecimal(uploadBo.getAmountDengueFever_1()));
                    insuredDoneBo.setAmountDengueFever_2(transBigDecimal(uploadBo.getAmountDengueFever_2()));
                    insuredDoneBo.setAmountDengueFever_3(transBigDecimal(uploadBo.getAmountDengueFever_3()));
                    insuredDoneBo.setPremuimSevenPlus(transBigDecimal(uploadBo.getPremuimSevenPlus()));
                    insuredDoneBo.setDailyAllowanceSevenPlus(transBigDecimal(uploadBo.getDailyAllowanceSevenPlus()));
                    insuredDoneBo.setPremuimTwelveClinic(transBigDecimal(uploadBo.getPremuimTwelveClinic()));
                    insuredDoneBo.setPremuimTwelveHospital(transBigDecimal(uploadBo.getPremuimTwelveHospital()));
                    insuredDoneBo.setPremuimTwelveTransfer(transBigDecimal(uploadBo.getPremuimTwelveTransfer()));
                    insuredDoneBo.setTotalPremuimTwelve(transBigDecimal(uploadBo.getTotalPremuimTwelve()));

                    insuredDoneBo.setSeventeenTlPremium(transBigDecimal(uploadBo.getSeventeenTlPremium()));
                    insuredDoneBo.setSeventeenTaPremium(transBigDecimal(uploadBo.getSeventeenTaPremium()));
                    insuredDoneBo.setSeventeenTcPremium(transBigDecimal(uploadBo.getSeventeenTcPremium()));
                    insuredDoneBo.setSeventeenTsPremium(transBigDecimal(uploadBo.getSeventeenTsPremium()));
                    insuredDoneBo.setSeventeenTlAmount(transBigDecimal(uploadBo.getSeventeenTlAmount()));
                    insuredDoneBo.setSeventeenTaAmount(transBigDecimal(uploadBo.getSeventeenTaAmount()));
                    insuredDoneBo.setSeventeenTcAmount(transBigDecimal(uploadBo.getSeventeenTcAmount()));
                    insuredDoneBo.setSeventeenTsAmount(transBigDecimal(uploadBo.getSeventeenTsAmount()));
                    insuredDoneBo.setEighteenPremium(transBigDecimal(uploadBo.getEighteenPremium()));
                    if (AssertUtils.isNotEmpty(insuredDoneBo.getEighteenLevel())) {
                        insuredDoneBo.setEighteenLevel(insuredDoneBo.getEighteenLevel().replaceAll(" ","_").toUpperCase());
                    }
                    insuredDoneBo.setTwentysixPremium(transBigDecimal(uploadBo.getTwentysixPremium()));
                    insuredDoneBo.setTwentysixAmount(transBigDecimal(uploadBo.getTwentysixAmount()));
                    insuredDoneBo.setTwentysevenPremium(transBigDecimal(uploadBo.getTwentysevenPremium()));
                    if (AssertUtils.isNotEmpty(uploadBo.getTwentysevenLevel())) {
                        insuredDoneBo.setTwentysevenLevel(uploadBo.getTwentysevenLevel().replaceAll(" ","_").toUpperCase());
                    }
                    insuredDoneBo.setTwentynineAmount(transBigDecimal(uploadBo.getTwentynineAmount()));
                    insuredDoneBo.setTwentyninePremium(transBigDecimal(uploadBo.getTwentyninePremium()));
                    insuredDoneBo.setThirtythreeAmount(transBigDecimal(uploadBo.getThirtythreeAmount()));
                    insuredDoneBo.setThirtythreePremium(transBigDecimal(uploadBo.getThirtythreePremium()));
                    if (AssertUtils.isNotEmpty(uploadBo.getTwentynineLevel())) {
                        insuredDoneBo.setTwentynineLevel(Arrays.stream(ApplyTermEnum.PRODUCT_LEVEL_29.values()).filter(name -> name.value().equals(uploadBo.getTwentynineLevel())).findFirst().get().name());
                    }
                    if (AssertUtils.isNotEmpty(uploadBo.getNationality())) {
                        insuredDoneBo.setNationality(Arrays.stream(ApplyTermEnum.NATIONALITY.values()).filter(nationality -> nationality.value().equals(uploadBo.getNationality())).findFirst().get().name());
                    }
                    insuredDoneBo.setInsuredDoneId(UUIDUtils.getUUIDShort());
                    insuredDoneBo.setForceSave(true);
                    insuredDoneBos.add(insuredDoneBo);
                    // 受益人导入数据
                    List<ApplyBeneficiaryDonePo> beneficiaryDonePos = new ArrayList<>();
                    beneficiaryUploadPos.stream()
                            .filter(beneficiaryUploadPo -> beneficiaryUploadPo.getInsuredUploadId().equals(uploadBo.getInsuredUploadId()))
                            .forEach(beneficiaryUploadPo -> {
                                ApplyBeneficiaryDonePo beneficiaryDonePo = new ApplyBeneficiaryDonePo();
                                ClazzUtils.copyPropertiesIgnoreNull(beneficiaryUploadPo, beneficiaryDonePo);
                                for (ApplyTermEnum.BENEFICIARY_NO beneficiaryNo : ApplyTermEnum.BENEFICIARY_NO.values()) {
                                    if (beneficiaryNo.value().equalsIgnoreCase(beneficiaryUploadPo.getBeneficiaryNoOrder())) {
                                        beneficiaryDonePo.setBeneficiaryNoOrder(beneficiaryNo.name());
                                    }
                                }
                                beneficiaryDonePo.setSex(beneficiaryUploadPo.getSex().toUpperCase());
                                beneficiaryDonePo.setBeneficiaryProportion(new BigDecimal(beneficiaryUploadPo.getBeneficiaryProportion()));
                                if (AssertUtils.isNotEmpty(beneficiaryUploadPo.getBirthday())) {
                                    beneficiaryDonePo.setBirthday(DateUtils.stringToTime(beneficiaryUploadPo.getBirthday(), DateUtils.FORMATE18));
                                }
                                for (ApplyTermEnum.RELATIONSHIP_WITH_THE_INSURED relationship : ApplyTermEnum.RELATIONSHIP_WITH_THE_INSURED.values()) {
                                    if (relationship.code().equalsIgnoreCase(beneficiaryUploadPo.getRelationship())) {
                                        beneficiaryDonePo.setRelationship(relationship.name());
                                    }
                                }
                                if (AssertUtils.isNotEmpty(beneficiaryUploadPo.getIdNo())) {
                                    beneficiaryDonePo.setIdNo(beneficiaryUploadPo.getIdNo().replaceAll("\\s*", ""));
                                }
                                beneficiaryDonePo.setInsuredDoneId(insuredDoneBo.getInsuredDoneId());
                                beneficiaryDonePos.add(beneficiaryDonePo);
                            });
                    insuredDoneBo.setListBeneficiaryDone(beneficiaryDonePos);
                });
        return insuredDoneBos;
    }

    /**
     * 转换BigDecimal
     *
     * @param value
     * @return
     */
    private BigDecimal transBigDecimal(String value) {
        if (AssertUtils.isNotEmpty(value)) {
            return new BigDecimal(value);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 组装投保单数据
     *
     * @param productRequest
     * @param users
     * @param agentId
     * @param agentSimpleBaseResponse
     * @param applyCoveragePos
     * @return
     */
    public ApplyPo transApplyPo(ProductRequest productRequest, Users users, String agentId,
                                AgentSimpleBaseResponse agentSimpleBaseResponse, List<ApplyCoveragePo> applyCoveragePos) {
        ApplyPo applyPo = new ApplyPo();
        applyPo.setApplyNo(productRequest.getApplyNo());
        applyPo.setApplyDate(DateUtils.getCurrentTime());
        applyPo.setApplyType(ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name());
        applyPo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL.name());
        if (!AssertUtils.isNotEmpty(applyPo.getApplySource())) {
            if (agentId.equals(users.getUserId())) {
                applyPo.setApplySource(ApplyTermEnum.APPLY_SOURCE.AGENT_INPUT.name());
            } else {
                applyPo.setApplySource(ApplyTermEnum.APPLY_SOURCE.ACCEPT_INPUT.name());
            }
        }

        // 查询机构信息
        ResultObject<BranchResponse> branchResultObject = platformBranchBaseApi.queryOneBranchById(agentSimpleBaseResponse.getBranchId());
        AssertUtils.isResultObjectError(getLogger(), branchResultObject);
        BranchResponse branchResponse = branchResultObject.getData();

        if (!AssertUtils.isNotEmpty(applyPo.getApplyNo())) {
            // 生成投保单号
            ResultObject<CertifyNumberResponse> certifyNumberRespFcResultObject = certifyApplyApi.certifyNumberGetNew(branchResponse.getChannelTypeCode()
                    , ApplyTermEnum.BUSINESS_TYPE.APPLY.name(),
                    ApplyTermEnum.APPLICANT_TYPE.GROUP.name(),
                    AssertUtils.isNotNull(branchResponse.getBranchBank()) ? branchResponse.getBranchBank().getBankAbbreviation() : null,
                    branchResponse.getBranchId(),null,null,null);
            AssertUtils.isResultObjectError(getLogger(), certifyNumberRespFcResultObject);

            applyPo.setApplyNo(certifyNumberRespFcResultObject.getData().getCertifyNumber());
        }
        applyPo.setSalesBranchId(branchResponse.getBranchId());
        applyPo.setManagerBranchId(branchResponse.getManagerBranchId());
        applyPo.setChannelTypeCode(branchResponse.getChannelTypeCode());
        // 组装投保单险种数据
        transApplyCoverage(productRequest, agentSimpleBaseResponse, applyCoveragePos, applyPo);

        return applyPo;
    }

    /**
     * 组装投保单险种数据
     *
     * @param productRequest
     * @param agentSimpleBaseResponse
     * @param applyCoveragePos
     * @param applyPo
     */
    public void transApplyCoverage(ProductRequest productRequest, AgentSimpleBaseResponse agentSimpleBaseResponse, List<ApplyCoveragePo> applyCoveragePos, ApplyPo applyPo) {
        // 调产品服务
        productRequest.getProduct().forEach(coverageBo -> {
            ResultObject<ProductDetailedInfoResponse> resultObject =
                    productApi.getProductDetailInfo(coverageBo.getProductId(), agentSimpleBaseResponse.getBranchId());
            AssertUtils.isResultObjectError(getLogger(), resultObject);
            AssertUtils.isResultObjectDataNull(getLogger(), resultObject, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_PRODUCT_INFO_IS_NOT_FOUNT_OBJECT);

            ProductDetailedInfoResponse productDetailedInfoResponse = resultObject.getData();
            if (AssertUtils.isNotNull(productDetailedInfoResponse)) {
                ApplyCoveragePo applyCoveragePo = new ApplyCoveragePo();
                applyCoveragePo.setApplyNo(coverageBo.getApplyNo());
                applyCoveragePo.setProductId(productDetailedInfoResponse.getProductId());
                applyCoveragePo.setProductCode(productDetailedInfoResponse.getProductCode());
                applyCoveragePo.setProductName(productDetailedInfoResponse.getProductName());
                applyCoveragePo.setPrimaryFlag(productDetailedInfoResponse.getMainProductFlag());
                applyCoveragePo.setMult(coverageBo.getMult());
                applyCoveragePo.setProductLevel(coverageBo.getProductLevel());
                applyCoveragePo.setOriginalPremium(new BigDecimal(
                        AssertUtils.isNotEmpty(productDetailedInfoResponse.getMinPrice()) ? productDetailedInfoResponse.getMinPrice() : "0"));
                applyCoveragePo.setDutyChooseFlag(productDetailedInfoResponse.isDutyChooseFlag() ? TerminologyConfigEnum.WHETHER.YES.name() : TerminologyConfigEnum.WHETHER.NO.name());
                applyCoveragePos.add(applyCoveragePo);

                if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(productDetailedInfoResponse.getMainProductFlag())
                        && AssertUtils.isNotNull(productDetailedInfoResponse.getProvider()) && AssertUtils.isNotNull(applyPo)) {
                    // 设置投保单供应商ID
                    applyPo.setProviderId(productDetailedInfoResponse.getProvider().getProviderId());
                    // 保存币种
                    applyPo.setCurrencyCode(productDetailedInfoResponse.getCurrencyCode());
                }
            }
        });
        Optional<ApplyCoveragePo> coverageOptional = applyCoveragePos.stream()
                .filter(applyCoveragePo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag()))
                .findAny();
        if (!coverageOptional.isPresent()) {
            throwsException(GROUP_APPLY_PARAMETER_MAIN_COVERAGE_IS_NOT_NULL);
        }
    }


    /**
     * 转换列表
     *
     * @param applyListBos   投保单列表对象
     * @param workflowStatus 工作流状态编码
     * @return List<ApplyListResponse>
     */
    public List<ApplyListResponse> transApplyList(List<ApplyListBo> applyListBos, String workflowStatus, Users currentLoginUsers) {
        if (!AssertUtils.isNotEmpty(applyListBos)) {
            return null;
        }
        List<String> allBranchIds = new ArrayList<>();
        applyListBos.forEach(applyListBo -> {
            allBranchIds.add(applyListBo.getSalesBranchId());
            allBranchIds.add(applyListBo.getManagerBranchId());
        });
        List<BranchResponse> allBranches = platformBranchApi.branchsPost(allBranchIds).getData();
        List<SyscodeRespFc> workflowStatuses = platformBaseInternationServiceApi.queryInternational(AssertUtils.isNotEmpty(workflowStatus) ? workflowStatus : "", currentLoginUsers.getLanguage()).getData();

        List<String> agentIds = applyListBos.stream().map(ApplyListBo::getAgentId).collect(Collectors.toList());

        AgentApplyQueryRequest applyAgentRequest = new AgentApplyQueryRequest();
        applyAgentRequest.setListAgentId(agentIds);

        List<AgentResponse> applyAgentRespFcs = agentApi.agentsGet(applyAgentRequest).getData();
        AssertUtils.isNotEmpty(this.getLogger(), applyAgentRespFcs, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);

        List<ApplyListResponse> applyListResponses = new ArrayList<>();
        applyListBos.forEach(applyListBo -> {

            ApplyListResponse applyListResponse = (ApplyListResponse) this.converterObject(applyListBo, ApplyListResponse.class);

            //代理人姓名
            applyAgentRespFcs.stream().filter(applyAgentRespFc -> applyListBo.getAgentId().equals(applyAgentRespFc.getAgentId())).findFirst().ifPresent((agent) -> {
                applyListResponse.setAgentName(agent.getAgentName());
                applyListResponse.setAgentMobile(agent.getMobile());
            });

            //销售机构
            applyListResponse.setSalesBranchName(LanguageUtils.getBranchName(allBranches, applyListBo.getSalesBranchId()));
            //管理机构
            applyListResponse.setManagerBranchName(LanguageUtils.getBranchName(allBranches, applyListBo.getManagerBranchId()));
            //工作流状态
            applyListResponse.setWorkflowStatusName(LanguageUtils.getCodeName(workflowStatuses, applyListBo.getWorkflowStatus()));
            applyListResponses.add(applyListResponse);
        });
        return applyListResponses;
    }

    public GroupInputResponse transGroupInput(Users users, ApplyBo applyBo) {

        GroupInputResponse groupInputResponse = (GroupInputResponse) this.converterObject(applyBo, GroupInputResponse.class);
        groupInputResponse.setApplyId(applyBo.getApplyId());
        groupInputResponse.setApplyNo(applyBo.getApplyNo());
        String mainProductId = this.getGroupMainProductId(applyBo.getApplyId());
        groupInputResponse.setMainProductId(mainProductId);
        if (ProductTermEnum.PRODUCT.PRODUCT_29.id().equals(mainProductId)) {
            groupInputResponse.setStudentFlag(TerminologyConfigEnum.WHETHER.YES.name());
        }
        //转换投保人信息以及转换投保单寄送信息
        this.transApplyApplicantResponse(users, groupInputResponse.getApplicant(), groupInputResponse.getApplyContact());
        //转换投保单附件
        groupInputResponse.setListAttachmentType(this.transApplyAttachmentType(applyBo));
        return groupInputResponse;
    }

    public GroupApplyDetailResponse transGroupApplyDetail(Users users, ApplyBo applyBo) {
        GroupApplyDetailResponse groupApplyDetailResponse = (GroupApplyDetailResponse) this.converterObject(applyBo, GroupApplyDetailResponse.class);
        String mainProductId = this.getGroupMainProductId(applyBo.getApplyId());
        groupApplyDetailResponse.setMainProductId(mainProductId);
        if (ProductTermEnum.PRODUCT.PRODUCT_29.id().equals(mainProductId)) {
            groupApplyDetailResponse.setStudentFlag(TerminologyConfigEnum.WHETHER.YES.name());
        }
        //转换投保人信息以及转换投保单寄送信息
        this.transApplyApplicantResponse(users, groupApplyDetailResponse.getApplicant(), groupApplyDetailResponse.getApplyContact());

        //转换代理人信息
        groupApplyDetailResponse.setApplyAgent(this.transApplyAgent(users, applyBo));

        //转换投保单主表对象
        groupApplyDetailResponse.setApply(this.transApply(users, applyBo, groupApplyDetailResponse.getApplyAgent()));

        //转换特别约定信息
        groupApplyDetailResponse.setListSpecialContract(this.transApplySpecialContract(users, applyBo.getApplyId()));

        //转换险种
        groupApplyDetailResponse.setListApplyCoverage(this.transApplyInputCoverageResponse(applyBo));

        //转换附件信息
        this.transferApplyAttachment(users, groupApplyDetailResponse, applyBo);
        //核保信息
        groupApplyDetailResponse.setArtiUnderwriteInfo(this.transferGroupApplyUW(applyBo.getApplyId()));

        //转换保费信息
        groupApplyDetailResponse.setApplyPremium(this.transApplyPremium(users, applyBo));

        //针对17号产品的暂予承保处理
        Optional<ApplyCoverageBo> first = applyBo.getListInsuredCoverage().stream().filter(applyCoverageBo ->
                applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())).distinct().findFirst();
        if (first.isPresent() && ProductTermEnum.PRODUCT.PRODUCT_17.id().equals(first.get().getProductId())) {
            groupApplyDetailResponse.setPreUnderwritingFlag(ApplyTermEnum.YES_NO.YES.name());
        }
        //推荐信息
        ApplyReferralInfoPo referralInfo = applyBo.getReferralInfo();
        if (AssertUtils.isNotNull(referralInfo) && AssertUtils.isNotEmpty(referralInfo.getApplyReferralInfoId())) {
            groupApplyDetailResponse.setReferralInfoFlag(TerminologyConfigEnum.WHETHER.YES.name());
            ApplyReferralInfoResponse applyReferralInfoResponse = new ApplyReferralInfoResponse();
            ClazzUtils.copyPropertiesIgnoreNull(referralInfo, applyReferralInfoResponse);
            groupApplyDetailResponse.setReferralInfo(applyReferralInfoResponse);
        }
        ResultObject<ProductDetailedInfoResponse> productDetailedInfoResponseResultObject = productApi.getProductDetailInfo(first.get().getProductId(), applyBo.getSalesBranchId());
        if (!AssertUtils.isResultObjectDataNull(productDetailedInfoResponseResultObject) && TerminologyConfigEnum.WHETHER.YES.name().equals(productDetailedInfoResponseResultObject.getData().getReferralInfoFlag())) {
            //推荐信息所需的银行分支机构ID
            ResultObject<List<BranchResponse>> branchSameLevel = platformBranchApi.getBranchSameLevel(applyBo.getSalesBranchId());
            if (!AssertUtils.isResultObjectListDataNull(branchSameLevel)) {
                groupApplyDetailResponse.setBankBranchIds((List<BranchSimpleResponse>) this.converterList(branchSameLevel.getData(), new TypeToken<List<BranchSimpleResponse>>() {
                }.getType()));
            }
        }
        return groupApplyDetailResponse;
    }

    private List<ApplyCoverageResponse> transApplyInputCoverageResponse(ApplyBo applyBo) {
        List<ApplyCoverageResponse> listApplyCoverageResponse = new ArrayList<>();
        List<ApplyCoverageBo> listApplyCoverage = applyBo.getListInsuredCoverage();
        if (!AssertUtils.isNotEmpty(listApplyCoverage)) {
            return listApplyCoverageResponse;
        }
        //获取险种字段属性
        List<Field> coverageFields = clazzBusinessService.getFiledList(ApplyCoveragePo.class);
        List<Field> dutyFields = clazzBusinessService.getFiledList(ApplyCoverageDutyPo.class);
        List<ApplyCoverageBo> applyCoverageBos = applyDataTransform.transSummaryCoverage(applyBo.getListInsuredCoverage());
        if (AssertUtils.isNotEmpty(applyCoverageBos)) {
            List<ApplyCoverageResponse> applyCoverageResponses = new ArrayList<>();
            applyCoverageBos.forEach(applyCoverageBo -> {
                ApplyCoverageResponse applyCoverageResponse = new ApplyCoverageResponse();
                applyCoverageResponse.setCoverageId(applyCoverageBo.getCoverageId());
                applyCoverageResponse.setPrimaryFlag(applyCoverageBo.getPrimaryFlag());
                applyCoverageResponse.setProductId(applyCoverageBo.getProductId());
                applyCoverageResponse.setProductCode(applyCoverageBo.getProductCode());
                applyCoverageResponse.setProductName(applyCoverageBo.getProductName());
                //录入复核投保单险种信息
                applyInputGetTransData.transferApplyCoverageList(applyBo.getSalesBranchId(), coverageFields, dutyFields, applyCoverageResponse, applyCoverageBo);
                applyCoverageResponses.add(applyCoverageResponse);
            });
            if (ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name().equals(applyBo.getApplyType())) {
                // 按产品分组
                Map<String, List<ApplyCoverageResponse>> productMap = applyCoverageResponses.stream().collect(Collectors.groupingBy(ApplyCoverageResponse::getProductId));
                for (String productId : productMap.keySet()) {
                    List<ApplyCoverageResponse> coverageResponses = productMap.get(productId);
                    ApplyCoverageResponse coverageResponse = new ApplyCoverageResponse();
                    ClazzUtils.copyPropertiesIgnoreNull(coverageResponses.get(0), coverageResponse);
                    coverageResponse.setListProductFields(coverageResponses.get(0).getListProductFields());
                    coverageResponse.setProductDetailedInfoRespFc(coverageResponses.get(0).getProductDetailedInfoRespFc());
                    // 移除险种名称字段
                    coverageResponses.forEach(coverage -> coverage.getListProductFields().removeIf(field -> "coverageName".equals(field.getFieldName())));
                    coverageResponse.setListCoverage(coverageResponses);
                    listApplyCoverageResponse.add(coverageResponse);
                    // 排序
                    listApplyCoverageResponse.sort(Comparator.comparing(ApplyCoverageResponse::getPrimaryFlag, Comparator.nullsLast(String::compareTo)).reversed()
                            .thenComparing(ApplyCoverageResponse::getProductId, Comparator.nullsLast(String::compareTo)));
                }
            } else {
                // 个险
                listApplyCoverageResponse.addAll(applyCoverageResponses);
            }
        }
        return listApplyCoverageResponse;
    }


    private void transferApplyAttachment(Users users, GroupApplyDetailResponse groupApplyDetailResponse, ApplyBo applyBo) {

        List<ApplyAttachmentBo> applyAttachmentBos = applyBo.getListAttachment();
        if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
            List<ImageManageAttachmentResponse> attachmentTypeResponseList = this.getAttachmentList(users, applyAttachmentBos);
            //设置附件类型
            groupApplyDetailResponse.setListAttachmentType(attachmentTypeResponseList);
        }
    }

    public UnderwriteInfoResponse transferGroupApplyUW(String applyId) {
        //查询人工核保数据
        ResultObject<List<WorkItemTrackResponse>> artificialUnderwritingTask = workFlowApi.businessTrackByCondition(applyId, ModelConstantEnum.WORKFLOW_STATUS.GROUP_UNDERWRITING_TASK.name());
        if (!AssertUtils.isResultObjectListDataNull(artificialUnderwritingTask)) {
            UnderwriteInfoResponse artiUnderwriteInfoResponse = new UnderwriteInfoResponse();
            WorkItemTrackResponse workItemTrackResponse = artificialUnderwritingTask.getData().get(0);
            //日期格式化
            if (AssertUtils.isNotNull(workItemTrackResponse.getFinishTime())) {
                artiUnderwriteInfoResponse.setUnderwriteDate(workItemTrackResponse.getFinishTime());
            } else {
                artiUnderwriteInfoResponse.setUnderwriteDate(workItemTrackResponse.getSignTime());
            }
            String signUser = null;
            if (AssertUtils.isNotEmpty(workItemTrackResponse.getFinishUserId())) {
                signUser = workItemTrackResponse.getFinishUserId();
            } else if (AssertUtils.isNotEmpty(workItemTrackResponse.getSignUserId())) {
                signUser = workItemTrackResponse.getSignUserId();
            }
            if (AssertUtils.isNotEmpty(signUser)) {
                EmployeResponse employeResponse = platformEmployeeApi.employeGet(signUser).getData();
                if (AssertUtils.isNotNull(employeResponse)) {
                    artiUnderwriteInfoResponse.setSignUser(employeResponse.getEmployeName());
                }
            }
            ApplyUnderwriteDecisionPo applyUnderwriteDecisionBo = applyUnderwriteBaseService.queryApplyUnderwriteDecisionPo(applyId);
            if (AssertUtils.isNotNull(applyUnderwriteDecisionBo)) {
                Arrays.stream(ApplyTermEnum.DECISION_TYPE.values()).filter(decisionType -> decisionType.id().equals(applyUnderwriteDecisionBo.getUnderwriteDecisionId()))
                        .findFirst().ifPresent(decisionType -> artiUnderwriteInfoResponse.setReviewContent(decisionType.name()));
                artiUnderwriteInfoResponse.setRemark(applyUnderwriteDecisionBo.getRemarks());
            }
            return artiUnderwriteInfoResponse;
        }
        return null;
    }

    public List<ImageManageAttachmentResponse> getAttachmentList(Users users, List<ApplyAttachmentBo> applyAttachmentBos) {
        if (!AssertUtils.isNotEmpty(applyAttachmentBos)) {
            return null;
        }
        List<ImageManageAttachmentResponse> attachmentTypeResponseList = new ArrayList<>();
        applyAttachmentBos.forEach(attachment -> {
            //过滤保单书
            if (!ApplyTermEnum.ATTACHMENT_TYPE_FLAG.POLICY_BOOK.name().equals(attachment.getAttachmentTypeCode())) {
                //附件类型
                ImageManageAttachmentResponse applyAttachmentTypeResponse = attachmentTypeResponseList.stream()
                        .filter(attachmentType -> attachmentType.getAttachmentTypeCode().equals(attachment.getAttachmentTypeCode()))
                        .findFirst().orElse(new ImageManageAttachmentResponse());

                if (!AssertUtils.isNotEmpty(applyAttachmentTypeResponse.getAttachmentTypeCode())) {
                    applyAttachmentTypeResponse.setAttachmentTypeCode(attachment.getAttachmentTypeCode());
                    applyAttachmentTypeResponse.setAttachmentSeq(attachment.getAttachmentSeq());
                    applyAttachmentTypeResponse.setTotalPage(0L);
                    attachmentTypeResponseList.add(applyAttachmentTypeResponse);
                }
                //统计总记录数
                applyAttachmentTypeResponse.setTotalPage(applyAttachmentTypeResponse.getTotalPage() + 1);
                //附件
                ApplyAttachmentResp applyAttachmentResponse = new ApplyAttachmentResp();
                applyAttachmentResponse.setAttachmentId(attachment.getAttachmentId());
                applyAttachmentResponse.setAttachmentSeq(attachment.getAttachmentSeq());
                applyAttachmentTypeResponse.getListApplyAttachment().add(applyAttachmentResponse);
            }
        });
        return attachmentTypeResponseList;
    }

    private List<ProductResponse> transProductResponse(Users users, String applyId) {
        List<ProductResponse> listProduct = new ArrayList<>();
        //国际化险种类型
        List<SyscodeRespFc> productMainProductFlagList = platformBaseInternationServiceApi.queryInternational(TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name(), users.getLanguage()).getData();
        List<SyscodeRespFc> productPremiumFrequency = platformBaseInternationServiceApi.queryInternational(TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name(), users.getLanguage()).getData();
        List<SyscodeRespFc> productPremiumPeriodUnit = platformBaseInternationServiceApi.queryInternational(TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name(), users.getLanguage()).getData();

        List<ApplyCoveragePo> applyCoveragePos = applyCoverageBaseService.listApplyCoverageOfInsured(applyId);
        if (AssertUtils.isNotEmpty(applyCoveragePos)) {
            applyCoveragePos.forEach(applyCoveragePo -> {
                ProductResponse productResponse = (ProductResponse) this.converterObject(applyCoveragePo, ProductResponse.class);
                productResponse.setPrimaryFlagName(LanguageUtils.getCodeName(productMainProductFlagList, applyCoveragePo.getPrimaryFlag()));
                productResponse.setPremiumFrequencyName(LanguageUtils.getCodeName(productPremiumFrequency, applyCoveragePo.getPremiumFrequency()));
                productResponse.setPremiumPeriodUnitName(LanguageUtils.getCodeName(productPremiumPeriodUnit, applyCoveragePo.getPremiumPeriodUnit()));
                listProduct.add(productResponse);
            });
        }
        return listProduct;
    }

    public List<ApplySpecialContractResponse> transApplySpecialContract(Users currentLoginUsers, String applyId) {
        List<ApplySpecialContractPo> applySpecialContractPos = applySpecialContractBaseService.listApplySpecialContract(applyId);
        List<ApplySpecialContractResponse> listSpecialContract = (List<ApplySpecialContractResponse>) this.converterList(applySpecialContractPos, new TypeToken<List<ApplySpecialContractResponse>>() {
        }.getType());
        if (!AssertUtils.isNotEmpty(listSpecialContract)) {
            return listSpecialContract;
        }
        List<String> userIds = listSpecialContract.stream().map(ApplySpecialContractResponse::getCreatedUserId).collect(Collectors.toList());
        ResultObject<List<UserResponse>> userResultObject = platformUsersBaseApi.queryUsersPoByIds(userIds);
        int i = 1;
        for (ApplySpecialContractResponse applySpecialContractResponse : listSpecialContract) {
            applySpecialContractResponse.setNo(this.getNo(i));
            i++;
            if (!AssertUtils.isResultObjectListDataNull(userResultObject)) {
                userResultObject.getData().stream().filter(userRespFc -> userRespFc.getUserId().equals(applySpecialContractResponse.getCreatedUserId())).findFirst().ifPresent(user -> {
                    applySpecialContractResponse.setCreatedUserName(user.getName());
                });
            }
        }
        return listSpecialContract;
    }

    /**
     * 获取去编号
     *
     * @param no
     * @return
     */
    public String getNo(int no) {
        String stringNo = no + "";
        int length = 3 - stringNo.length();
        for (int i = 0; i < length; i++) {
            stringNo = "0" + stringNo;
        }
        return stringNo;
    }


    private ApplyResponse transApply(Users users, ApplyBo applyBo, ApplyAgentResponse applyAgentResponse) {
        ApplyResponse apply = (ApplyResponse) this.converterObject(applyBo, ApplyResponse.class);
        if (AssertUtils.isNotNull(apply)) {
            if (AssertUtils.isNotEmpty(applyBo.getListInsured())) {
                apply.setInsuredTotalNum(applyBo.getListInsured().size());
            }
            apply.setChannelTypeCode(applyBo.getChannelTypeCode());
            apply.setSalesBranchName(applyAgentResponse.getBranchName());
            apply.setManagerBranchId(applyAgentResponse.getManagerBranchId());
            apply.setManagerBranchName(applyAgentResponse.getManagerBranchName());
            if(AssertUtils.isNotEmpty(applyBo.getPolicyNo())) {
                ResultObject<PolicyResponse> policyResponseResultObject = policyApi.queryOnePolicy(applyBo.getPolicyNo());
                if (!AssertUtils.isResultObjectDataNull(policyResponseResultObject)) {
                    apply.setMaturityDate(policyResponseResultObject.getData().getMaturityDate());
                }
            }
        }
        return apply;
    }

    private ApplyAgentResponse transApplyAgent(Users users, ApplyBo applyBo) {
        ApplyAgentBo applyAgentBo = applyBo.getApplyAgentBo();
        ApplyAgentResponse applyAgent = (ApplyAgentResponse) this.converterObject(applyAgentBo, ApplyAgentResponse.class);
        applyAgent.setChannelTypeCode(applyBo.getChannelTypeCode());
        if (AssertUtils.isNotNull(applyAgentBo)) {
            ResultObject<AgentRecommendBaseResponse> agentBaseResult = baseAgentApi.queryOneAgentRecommendById(applyAgentBo.getAgentId());
            AgentRecommendBaseResponse agentBaseRespFc = agentBaseResult.getData();
            if (!AssertUtils.isResultObjectDataNull(agentBaseResult)) {
                //代理人基础信息
                applyAgent.setAgentName(agentBaseRespFc.getAgentName());
                applyAgent.setBranchId(agentBaseRespFc.getBranchId());
                if (AssertUtils.isNotNull(agentBaseRespFc.getAgentLevel())) {
                    applyAgent.setAgentLevelCode(agentBaseRespFc.getAgentLevel().getAgentLevelCode());
                }
                //推荐人信息
                if (AssertUtils.isNotNull(agentBaseRespFc.getAgentRecommend())) {
                    AgentRecommendBaseResponse recommendAgent = agentBaseRespFc.getAgentRecommend();
                    applyAgent.setRecommendName(recommendAgent.getAgentName());
                    if (AssertUtils.isNotNull(recommendAgent.getAgentLevel())) {
                        applyAgent.setRecommendLevelCode(recommendAgent.getAgentLevel().getAgentLevelCode());
                    }
                    applyAgent.setRecommendAgentCode(recommendAgent.getAgentCode());
                    if (AssertUtils.isNotNull(recommendAgent.getAgentDetail())) {
                        applyAgent.setRecommendMobile(recommendAgent.getAgentDetail().getMobile());
                    }
                }
                if (AssertUtils.isNotNull(agentBaseRespFc.getAgentDetail())) {
                    applyAgent.setAgentMobile(agentBaseRespFc.getAgentDetail().getMobile());
                }
                //代理人认证信息
                if (AssertUtils.isNotNull(agentBaseRespFc.getAgentIdentity())) {
                    AgentIdentityBaseResponse agentIdentity = agentBaseRespFc.getAgentIdentity();
                    applyAgent.setIdNo(agentIdentity.getIdNo());
                    applyAgent.setIdTypeCode(agentIdentity.getIdTypeCode());
                }
                //代理人销售机构信息
                ResultObject<BranchResponse> salesBranch = platformBranchBaseApi.queryOneBranchAndManagerById(agentBaseRespFc.getBranchId());
                if (!AssertUtils.isResultObjectDataNull(salesBranch)) {
                    applyAgent.setBranchName(salesBranch.getData().getBranchName());
                    //代理人管理机构信息
                    applyAgent.setManagerBranchId(salesBranch.getData().getManagerBranchId());
                    applyAgent.setManagerBranchName(salesBranch.getData().getManagerBranchName());
                }
            }
        }
        return applyAgent;
    }

    private ApplyPremiumResponse transApplyPremium(Users users, ApplyBo applyBo) {
        ApplyPremiumBo applyPremium = applyBo.getApplyPremiumBo();
        ApplyPremiumResponse applyPremiumResponse = (ApplyPremiumResponse) this.converterObject(applyPremium, ApplyPremiumResponse.class);
        if (AssertUtils.isNotNull(applyPremium)) {
            BigDecimal hundred = new BigDecimal("100");
            applyPremiumResponse.setTotalPremium(applyPremium.getTotalPremium());
            applyPremiumResponse.setAddPremium(applyPremium.getAddPremium());
            applyPremiumResponse.setReceivablePremium(applyPremium.getReceivablePremium());
            applyPremiumResponse.setDiscountType(applyPremium.getDiscountType());
            BigDecimal specialDiscount = applyPremium.getSpecialDiscount();
            if (AssertUtils.isNotNull(specialDiscount)) {
                applyPremiumResponse.setSpecialDiscount(new BigDecimal(hundred.multiply(specialDiscount).stripTrailingZeros().toPlainString()));
            }
            BigDecimal agentDiscount = applyPremium.getAgentDiscount();
            if (AssertUtils.isNotNull(agentDiscount)) {
                applyPremiumResponse.setAgentDiscount(new BigDecimal(hundred.multiply(agentDiscount).stripTrailingZeros().toPlainString()));
            }
            BigDecimal companyDiscount = applyPremium.getCompanyDiscount();
            if (AssertUtils.isNotNull(companyDiscount)) {
                applyPremiumResponse.setCompanyDiscount(new BigDecimal(hundred.multiply(companyDiscount).stripTrailingZeros().toPlainString()));
            }
        }
        return applyPremiumResponse;
    }

    private void transApplyApplicantResponse(Users users, ApplyApplicantResponse applyApplicantResponse, ApplyContactResponse applyContactResponse) {
        if (AssertUtils.isNotNull(applyApplicantResponse.getCompanyContractIdExpDate())) {
            applyApplicantResponse.setCompanyContractIdExpDateFormat(DateUtils.dateToString(new Date(applyApplicantResponse.getCompanyContractIdExpDate())));
        }
        if (AssertUtils.isNotNull(applyApplicantResponse.getCompanyLegalPersonIdExpDate())) {
            applyApplicantResponse.setCompanyLegalPersonIdExpDateFormat(DateUtils.dateToString(new Date(applyApplicantResponse.getCompanyLegalPersonIdExpDate())));
        }
        List<String> areaCodes = new ArrayList<>();
        if (AssertUtils.isNotEmpty(applyApplicantResponse.getCompanyAreaCode())) {
            areaCodes.add(applyApplicantResponse.getCompanyAreaCode());
        }

        if (AssertUtils.isNotEmpty(applyApplicantResponse.getHomeAreaCode())) {
            areaCodes.add(applyApplicantResponse.getHomeAreaCode());
        }
        if (AssertUtils.isNotEmpty(applyApplicantResponse.getDoctorAreaCode())) {
            areaCodes.add(applyApplicantResponse.getDoctorAreaCode());
        }

        //保单寄送地址拼接
        if (AssertUtils.isNotEmpty(applyContactResponse.getSendAddrAreaCode())) {
            areaCodes.add(applyContactResponse.getSendAddrAreaCode());
        }

        if (AssertUtils.isNotEmpty(areaCodes)) {
            areaCodes = areaCodes.stream().distinct().collect(Collectors.toList());
            ResultObject<List<AreaNameResponse>> respFcResultObjectList = platformAreaApi.areaNameGetBatch(areaCodes);

            ResultObject<Map<String, List<AreaTreeResponse>>> areaResultObject = platformAreaApi.areaTreeGetBatch(areaCodes);
            if (!AssertUtils.isResultObjectListDataNull(respFcResultObjectList)) {
                respFcResultObjectList.getData().forEach(areaNameResponse -> {
                    if (AssertUtils.isNotEmpty(applyApplicantResponse.getCompanyAreaCode()) && areaNameResponse.getAreaId().equals(applyApplicantResponse.getCompanyAreaCode())) {
                        applyApplicantResponse.setCompanyAreaName(areaNameResponse.getAreaName());
                        if (!AssertUtils.isNotEmpty(applyApplicantResponse.getCompanyAddressWhole())) {
                            applyApplicantResponse.setCompanyAddressWhole(AssertUtils.isNotEmpty(areaNameResponse.getAreaName())
                                    ? areaNameResponse.getAreaName() + " " + applyApplicantResponse.getCompanyAddress() : null);
                        }
                    }
                    if (AssertUtils.isNotEmpty(applyApplicantResponse.getHomeAreaCode()) && areaNameResponse.getAreaId().equals(applyApplicantResponse.getHomeAreaCode())) {
                        applyApplicantResponse.setHomeAreaName(areaNameResponse.getAreaName());
                    }
                    if (AssertUtils.isNotEmpty(applyContactResponse.getSendAddrAreaCode()) && areaNameResponse.getAreaId().equals(applyContactResponse.getSendAddrAreaCode())) {
                        applyContactResponse.setSendAddrAreaName(areaNameResponse.getAreaName());
                    }
                });
            }

            if (!AssertUtils.isResultObjectDataNull(areaResultObject)) {
                Map<String, List<AreaTreeResponse>> map = areaResultObject.getData();
                areaCodes.forEach(areaCode -> {
                    if (AssertUtils.isNotEmpty(applyApplicantResponse.getCompanyAreaCode()) && areaCode.equals(applyApplicantResponse.getCompanyAreaCode())) {
                        applyApplicantResponse.setListCompanyAreaName(map.get(areaCode));
                    }
                    if (AssertUtils.isNotEmpty(applyApplicantResponse.getHomeAreaCode()) && areaCode.equals(applyApplicantResponse.getHomeAreaCode())) {
                        applyApplicantResponse.setListAreaName(map.get(areaCode));
                    }
                    if (AssertUtils.isNotEmpty(applyApplicantResponse.getDoctorAreaCode()) && areaCode.equals(applyApplicantResponse.getDoctorAreaCode())) {
                        applyApplicantResponse.setListDoctorAreaName(map.get(areaCode));
                    }
                    if (AssertUtils.isNotEmpty(applyContactResponse.getSendAddrAreaCode()) && areaCode.equals(applyContactResponse.getSendAddrAreaCode())) {
                        applyContactResponse.setListAreaName(map.get(areaCode));
                    }
                });
            }

        }

        List<String> careerList = new ArrayList<>();
        if (AssertUtils.isNotEmpty(applyApplicantResponse.getCompanyIndustry())) {
            careerList.add(applyApplicantResponse.getCompanyIndustry());
        }
        //投保人职业
        if (AssertUtils.isNotEmpty(applyApplicantResponse.getOccupationCode())) {
            careerList.add(applyApplicantResponse.getOccupationCode());
        }

        if (AssertUtils.isNotEmpty(careerList)) {
            careerList = careerList.stream().distinct().collect(Collectors.toList());
            ResultObject<List<CareerNameResponse>> careerNameResponseResultObject = platformCareerApi.postCareerName(careerList);
            if (!AssertUtils.isResultObjectListDataNull(careerNameResponseResultObject)) {
                careerNameResponseResultObject.getData().forEach(careerNameResponse -> {
                    if (AssertUtils.isNotEmpty(applyApplicantResponse.getCompanyIndustry()) && careerNameResponse.getCareerId().equals(applyApplicantResponse.getCompanyIndustry())) {
                        applyApplicantResponse.setCompanyIndustryName(careerNameResponse.getCareerName());
                    }
                    if (AssertUtils.isNotEmpty(applyApplicantResponse.getOccupationCode()) && careerNameResponse.getCareerId().equals(applyApplicantResponse.getOccupationCode())) {
                        applyApplicantResponse.setOccupationName(careerNameResponse.getCareerName());
                    }
                });
            }
        }
        // 学校类型
        if(AssertUtils.isNotEmpty(applyApplicantResponse.getSchoolType())) {
            applyApplicantResponse.setSchoolTypeList(JSONObject.parseArray(applyApplicantResponse.getSchoolType(), String.class));
            SyscodeRequest syscodeRequest = new SyscodeRequest();
            syscodeRequest.setCodeType("SCHOOL_TYPE");
            syscodeRequest.setCodeKeys(applyApplicantResponse.getSchoolTypeList());
            syscodeRequest.setLang(users.getLanguage());
            ResultObject<List<SyscodeResponse>> listResultObject = platformInternationalBaseApi.queryInternationalByCodeKeys(syscodeRequest);
            applyApplicantResponse.setSchoolTypeListName(listResultObject.getData().stream().map(SyscodeResponse::getCodeName).collect(Collectors.toList()));
        }
    }

    private void transApplyContactResponse(ApplyContactResponse applyContactResponse) {
        //保单寄送地址拼接
        if (AssertUtils.isNotEmpty(applyContactResponse.getSendAddrAreaCode())) {
            ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(applyContactResponse.getSendAddrAreaCode());
            if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                applyContactResponse.setSendAddrAreaName(respFcResultObject.getData().getAreaName());
            }
            ResultObject<List<AreaTreeResponse>> areaResultObject = platformAreaApi.areaTreeGet(applyContactResponse.getSendAddrAreaCode());
            if (!AssertUtils.isResultObjectDataNull(areaResultObject)) {
                applyContactResponse.setListAreaName(areaResultObject.getData());
            }
        }
    }

    private List<AttachmentTypeResp> transApplyAttachmentType(ApplyBo applyBo) {
        AssertUtils.isNotEmpty(this.getLogger(), applyBo.getListCoverage(), ApplyErrorConfigEnum.APPLY_INPUT_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT);
        List<String> listProductId = applyBo.getListCoverage().stream().map(ApplyCoverageBo::getProductId).distinct().collect(Collectors.toList());
        //查询该产品的所有基础附件
        ResultObject<List<CertifyAttachmentResponse>> productCertifyResultObject = productCertifyAttachmentBaseApi.queryProductCertify(listProductId, "BASE");
        AssertUtils.isResultObjectListDataNull(this.getLogger(), productCertifyResultObject, ApplyErrorConfigEnum.APPLY_QUERY_PRODUCT_ATTACHMENT_IS_NOT_FOUND_OBJECT);
        List<CertifyAttachmentResponse> productCertifyRespFcs = productCertifyResultObject.getData();
        List<SyscodeRespFc> attachmentType = platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.CERTIFY_ATTACHMENT_TYPE.name()).getData();

        List<AttachmentTypeResp> attachmentTypeResps = new ArrayList<>();
        for (CertifyAttachmentResponse certifyAttachmentResponse : productCertifyRespFcs) {
            AttachmentTypeResp attachmentTypeResp = new AttachmentTypeResp();
            attachmentTypeResp.setAttachmentTypeCode(certifyAttachmentResponse.getAttachmentTypeCode());
            attachmentTypeResp.setPageNumber(certifyAttachmentResponse.getRequiredPageNumber());
            attachmentTypeResp.setAttachmentSeq(certifyAttachmentResponse.getAttachmentSeq());
            attachmentTypeResp.setTotalPage(certifyAttachmentResponse.getRequiredPageNumber());

            attachmentType.forEach(syscodeRespFc -> {
                if (syscodeRespFc.getCodeKey().equals(certifyAttachmentResponse.getAttachmentTypeCode())) {
                    attachmentTypeResp.setAttachmentTypeName(syscodeRespFc.getCodeName());
                }
            });
            if (AssertUtils.isNotEmpty(applyBo.getListAttachment())) {
                List<ApplyAttachmentResp> applyAttachmentResps = new ArrayList<>();
                applyBo.getListAttachment().forEach(attachmentBo -> {
                    if (attachmentBo.getAttachmentTypeCode().equals(certifyAttachmentResponse.getAttachmentTypeCode())) {
                        ApplyAttachmentResp applyAttachmentResp = new ApplyAttachmentResp();
                        applyAttachmentResp.setAttachmentId(attachmentBo.getAttachmentId());
                        applyAttachmentResp.setAttachmentSeq(attachmentBo.getAttachmentSeq());
                        applyAttachmentResps.add(applyAttachmentResp);
                    }
                });
                attachmentTypeResp.setListAttachment(applyAttachmentResps);
            }

            attachmentTypeResps.add(attachmentTypeResp);
        }
        return attachmentTypeResps;
    }

    /**
     * 删除原有险种相关数据
     *
     * @param applyInsuredBos
     */
    public void deleteOriginalCoverageData(List<ApplyInsuredBo> applyInsuredBos) {
        List<ApplyCoveragePo> coveragePos = new ArrayList<>();
        List<ApplyCoverageDutyPo> coverageDutyPos = new ArrayList<>();
        List<ApplyCoverageLevelPo> coverageLevelPos = new ArrayList<>();

        applyInsuredBos.forEach(insuredBo -> {
            if (AssertUtils.isNotEmpty(insuredBo.getListCoverage())) {
                insuredBo.getListCoverage().forEach(coverageBo -> {
                    coveragePos.add(coverageBo);
                    if (TerminologyConfigEnum.WHETHER.YES.name().equals(coverageBo.getDutyChooseFlag()) &&
                            AssertUtils.isNotEmpty(coverageBo.getListCoverageDuty())) {
                        coverageBo.getListCoverageDuty().forEach(coverageDutyBo -> {
                            coverageDutyPos.add(coverageDutyBo);
                            if (AssertUtils.isNotEmpty(coverageDutyBo.getListCoverageLevel())) {
                                coverageLevelPos.addAll(coverageDutyBo.getListCoverageLevel());
                            }
                        });
                    } else if (AssertUtils.isNotEmpty(coverageBo.getListCoverageLevel())) {
                        coverageLevelPos.addAll(coverageBo.getListCoverageLevel());
                    }
                });
            }
        });
        // 删除险种、责任、档次
        applyCoverageBaseService.deleteApplyCoverage(coveragePos);
        applyCoverageBaseService.deleteApplyCoverageDuty(coverageDutyPos);
        applyCoverageBaseService.deleteApplyCoverageLevel(coverageLevelPos);
    }

    /**
     * 更新险种费用
     *
     * @param applyCoverageBos
     * @param finalSpecialDiscount
     */
    public void updateInsuranceFees(List<ApplyCoverageBo> applyCoverageBos, BigDecimal finalSpecialDiscount) {
        // 若存在特殊折扣 则更新险种实缴保费为折后保费
        if (AssertUtils.isNotNull(finalSpecialDiscount) && BigDecimal.ZERO.compareTo(finalSpecialDiscount) < 0) {
            applyCoverageBos.forEach(applyCoverageBo -> {
                // 计算折后保费
                BigDecimal discountedPremium = applyCoverageBo.getTotalPremium()
                        .multiply(new BigDecimal("1").subtract(finalSpecialDiscount))
                        .setScale(4, BigDecimal.ROUND_HALF_UP);
                // 实收保费设置为折后保费
                applyCoverageBo.setActualPremium(discountedPremium);
                /* 险种的保费折扣字段用于存放原始保费
                // 保费折扣
                applyCoverageBo.setPremiumDiscount(applyCoverageBo.getTotalPremium()
                        .subtract(discountedPremium)
                        .setScale(2, BigDecimal.ROUND_HALF_UP));*/

                // 根据险种责任设置实缴保费为 折后保费
                if (TerminologyConfigEnum.WHETHER.YES.name().equals(applyCoverageBo.getDutyChooseFlag())) {
                    applyCoverageBo.getListCoverageDuty().forEach(applyCoverageDutyBo ->
                            applyCoverageDutyBo.getListCoverageLevel().forEach(applyCoverageLevelPo -> {
                                applyCoverageLevelPo.setActualPremium(applyCoverageLevelPo.getTotalPremium().multiply(new BigDecimal("1").subtract(finalSpecialDiscount))
                                        .setScale(4, BigDecimal.ROUND_HALF_UP));
                            })
                    );
                } else {
                    applyCoverageBo.getListCoverageLevel().forEach(applyCoverageLevelPo -> {
                        applyCoverageLevelPo.setActualPremium(applyCoverageLevelPo.getTotalPremium().multiply(new BigDecimal("1").subtract(finalSpecialDiscount))
                                .setScale(4, BigDecimal.ROUND_HALF_UP));
                    });
                }
            });
        }
    }

    /**
     * 如果存在特殊折扣 更新投保单保费表保费信息 更新主表应收保费
     *
     * @param applyPo
     * @param totalPremium
     * @param specialDiscount
     * @param applyPremiumBo
     */
    public void updateApplyPremium(ApplyPo applyPo, BigDecimal totalPremium, BigDecimal specialDiscount, ApplyPremiumBo applyPremiumBo) {
        // 如果存在特殊折扣
        if (AssertUtils.isNotNull(specialDiscount) && BigDecimal.ZERO.compareTo(specialDiscount) < 0) {
            // 折扣前保费
            applyPremiumBo.setPremiumBeforeDiscount(totalPremium);
            // 应缴保费
            BigDecimal receivablePremium = applyPremiumBo.getTotalPremium()
                    .multiply(new BigDecimal("1").subtract(specialDiscount))
                    .setScale(2, BigDecimal.ROUND_HALF_UP);

            applyPremiumBo.setReceivablePremium(receivablePremium);// 应收保费
            applyPo.setReceivablePremium(receivablePremium);
            applyPremiumBo.setActualPremium(receivablePremium);// 实收保费
            applyPremiumBo.setTotalActualPremium(receivablePremium);// 总实缴保费
            /* 折扣保费暂不更新
            // 折扣保费
            applyPremiumBo.setDiscountPremium(applyPremiumBo.getPremiumBeforeDiscount().subtract(receivablePremium));*/
        }
    }

    /**
     * 获取团险被保人导入模板
     *
     * @param applyId 投保单ID
     * @return 被保人导入模板
     */
    public String getGroupInsuredTemplate(String applyId) {
        // 查询公共险种
        List<ApplyCoveragePo> applyCoverages = applyCoverageBaseService.listApplyCoverage(applyId);
        if (AssertUtils.isNotEmpty(applyCoverages)) {
            String productId = applyCoverages.stream().filter(applyCoveragePo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag()))
                    .map(ApplyCoveragePo::getProductId).findFirst().get();
            return Arrays.stream(ApplyTermEnum.IMPORT_EXPORT_TEMPLATE.values()).filter(a -> a.productId().equals(productId)).findFirst().get().name();
        }
        return ApplyTermEnum.IMPORT_EXPORT_TEMPLATE.GROUP_INSURED_TEMPLATE.name();
    }

    public String getGroupMainProductId(String applyId) {
        // 查询公共险种
        List<ApplyCoveragePo> applyCoverages = applyCoverageBaseService.listApplyCoverage(applyId);
        if (AssertUtils.isNotEmpty(applyCoverages)) {
            Optional<String> first = applyCoverages.stream().filter(applyCoveragePo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag()))
                    .map(ApplyCoveragePo::getProductId).findFirst();
            if (first.isPresent()) {
                return first.get();
            }
        }
        return null;
    }
}
