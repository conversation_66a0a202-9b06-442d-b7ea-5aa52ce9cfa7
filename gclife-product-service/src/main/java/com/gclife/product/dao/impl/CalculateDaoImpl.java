package com.gclife.product.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.product.core.jooq.tables.pojos.CalculateModePo;
import com.gclife.product.core.jooq.tables.pojos.ProductCheckControlPo;
import com.gclife.product.core.jooq.tables.pojos.ProductDiscountCalculateModePo;
import com.gclife.product.dao.CalculateDao;
import lombok.extern.slf4j.Slf4j;
import org.jooq.*;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.gclife.product.core.jooq.Tables.*;
import static org.jooq.impl.DSL.param;

/**
 * @Description: 算法处理Dao
 * <AUTHOR>
 * @date 2017-11-23
 * @version V1.0
 */
@Repository
@Slf4j
public class CalculateDaoImpl extends BaseDaoImpl implements CalculateDao {

    /**
     * 获取校验参数列表
     * @param productId 产品Id
     * @param controlType 控制类型
     * @return 责任列表
     */
    @Override
    public List<ProductCheckControlPo> queryProductCheckControl(String productId, String controlType,String controlPlaceMode){

        List<ProductCheckControlPo> productCheckControlPos=null;
        try{
            SelectConditionStep<Record> selectConditionStep=this.getDslContext()
                    .select(PRODUCT_CHECK_CONTROL.fields())
                    .from(PRODUCT_CHECK_CONTROL)
                    .where(PRODUCT_CHECK_CONTROL.PRODUCT_ID.eq(productId))
                    .and(PRODUCT_CHECK_CONTROL.CONTROL_TYPE.eq(controlType))
                    .and(PRODUCT_CHECK_CONTROL.CONTROL_PLACE_MODE.eq(controlPlaceMode));

            productCheckControlPos=selectConditionStep.fetchInto(ProductCheckControlPo.class);
        }catch (Exception e){
            e.printStackTrace();
        }
        return productCheckControlPos;
    }


    /**
     * 获取计算算法
     * @param calculateModeCode 算法编码
     * @return 责任列表
     */
    @Override
    public CalculateModePo getCalculateMode(String calculateModeCode){

        CalculateModePo calculateModePo=null;
        try{
            SelectConditionStep<Record> selectConditionStep=this.getDslContext()
                    .select(CALCULATE_MODE.fields())
                    .from(CALCULATE_MODE)
                    .where(CALCULATE_MODE.CALCULATE_CODE.eq(calculateModeCode));

            calculateModePo=selectConditionStep.fetchOneInto(CalculateModePo.class);


        }catch (Exception e){
            e.printStackTrace();
        }
        return calculateModePo;
    }

    @Override
    public ProductDiscountCalculateModePo getProductDiscountCalculateMode(String activityId) {
        return this.getDslContext()
                .select(PRODUCT_DISCOUNT_CALCULATE_MODE.fields())
                .from(PRODUCT_DISCOUNT_CALCULATE_MODE)
                .where(PRODUCT_DISCOUNT_CALCULATE_MODE.ACTIVITY_ID.eq(activityId)).limit(1).fetchOneInto(ProductDiscountCalculateModePo.class);
    }

    /**
     * 获取计算方法
     * @param productId 产品Id
     * @param calculateModeType 算法类别
     * @return
     */
    @Override
    public CalculateModePo getCalculateMode(String productId,String calculateModeType){

        CalculateModePo calculateModePo=null;
        try{
            SelectConditionStep<Record> selectConditionStep=this.getDslContext()
                    .select(CALCULATE_MODE.fields())
                    .from(CALCULATE_MODE)
                    .where(CALCULATE_MODE.PRODUCT_ID.eq(productId))
                    .and(CALCULATE_MODE.TYPE.eq(calculateModeType));

            calculateModePo=selectConditionStep.fetchOneInto(CalculateModePo.class);


        }catch (Exception e){
            e.printStackTrace();
        }
        return calculateModePo;
    }

    /**
     * 获取计算方法
     * @param productId 产品Id
     * @param calculateModeType 算法类别
     * @return
     */
    @Override
    public List<CalculateModePo> queryCalculateMode(String productId,String calculateModeType){

        List<CalculateModePo>  calculateModePos=null;
        try{
            SelectConditionStep<Record> selectConditionStep=this.getDslContext()
                    .select(CALCULATE_MODE.fields())
                    .from(CALCULATE_MODE)
                    .where(CALCULATE_MODE.PRODUCT_ID.eq(productId))
                    .and(CALCULATE_MODE.TYPE.eq(calculateModeType));

            calculateModePos=selectConditionStep.fetchInto(CalculateModePo.class);

        }catch (Exception e){
            e.printStackTrace();
        }
        return calculateModePos;
    }

    @Override
    public CalculateModePo queryOneCalculateMode(String productId, String calculateModeType) {
        return this.getDslContext()
                .select(CALCULATE_MODE.fields())
                .from(CALCULATE_MODE)
                .where(CALCULATE_MODE.PRODUCT_ID.eq(productId))
                .and(CALCULATE_MODE.TYPE.eq(calculateModeType)).fetchOneInto(CalculateModePo.class);
    }


    /**
     * 获取计算的值
     * @param checkSql 算法编码
     * @return 责任列表
     */
    @Override
    public Object getCalculateValue(String checkSql,List<Map<String,Object>> params){
        Object value = null;
        try{
            List<Param<Object>> paramList = new ArrayList<>();
            if(AssertUtils.isNotNull(params)){
                for(Map<String,Object> objectMap:params){
                    for(String parameName:objectMap.keySet()) {
                        paramList.add(param(parameName,objectMap.get(parameName)));
                    }
                }
            }
            ResultQuery query = this.getDslContext().resultQuery(checkSql,paramList.toArray(new Param[]{}));
            Result result = query.fetch();
            if (result.size() > 0) {
                value = result.getValue(0, 0);
                log.info("值为: {}", value);
            }
        } catch (Exception e){
            e.printStackTrace();
        }
        return value;
    }

    /**
     * 获取计算的值
     * @param checkSql 算法编码
     * @return 责任列表
     */
    @Override
    public Map getCalculateMap(String checkSql,List<Map<String,Object>> params){

        Map value=null;
        try{
            List<Param<Object>> paramList = new ArrayList<>();
            if(AssertUtils.isNotNull(params)){
                for(Map<String,Object> objectMap:params){
                    for(String parameName:objectMap.keySet()) {
                        paramList.add(param(parameName,objectMap.get(parameName)));
                    }
                }
            }

            ResultQuery query = this.getDslContext().resultQuery(checkSql,paramList.toArray(new Param[]{}));
            value = query.fetchOneMap();

        }catch (Exception e){
            e.printStackTrace();
        }
        return value;
    }


    /**
     * 获取计算的值
     * @param checkSql 算法编码
     * @return 责任列表
     */
    @Override
    public List<Object> queryCalculateValue(Class t,String checkSql, List<Map<String,Object>> params){

        List<Object> result=new ArrayList<>();
        try{
            List<Param<Object>> paramList=new ArrayList<>();
            if(AssertUtils.isNotNull(params)){
                for(Map<String,Object> objectMap:params){
                    for(String parameName:objectMap.keySet()) {
                        paramList.add(param(parameName,objectMap.get(parameName)));
                    }
                }
            }
            ResultQuery query=this.getDslContext().resultQuery(checkSql,paramList.toArray(new Param[]{}));

            System.out.println(query.toString());

            result = query.fetchInto(t);

        }catch (Exception e){
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 获取计算的值
     * @param checkSql 算法编码
     * @return 责任列表
     */
    @Override
    public Object getCalculateValue(Class tClass,String checkSql, List<Map<String,Object>> params){

        Object obj=null;
        try{
            List<Param<Object>> paramList=new ArrayList<>();
            if(AssertUtils.isNotNull(params)){
                for(Map<String,Object> objectMap:params){
                    for(String parameName:objectMap.keySet()) {
                        paramList.add(param(parameName,objectMap.get(parameName)));
                    }
                }
            }
            ResultQuery query=this.getDslContext().resultQuery(checkSql,paramList.toArray(new Param[]{}));

            System.out.println(query.toString());

            obj = query.fetchOneInto(tClass);

        }catch (Exception e){
            e.printStackTrace();
        }
        return obj;
    }




}
