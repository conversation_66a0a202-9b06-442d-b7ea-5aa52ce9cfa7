package com.gclife.product.controller;

import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.product.api.ProductApi;
import com.gclife.product.base.model.config.insurance.InsuranceProductEnum;
import com.gclife.product.form.calculate.ApplyRequest;
import com.gclife.product.vo.apply.ApplyResponse;
import com.gclife.product.vo.insurnce.policy.PolicyCashValueResponse;
import com.gclife.product.vo.manager.ProductDetailedInfoResponse;
import com.gclife.product.vo.insurnce.ProductSimpleInfoResponse;
import com.gclife.product.service.CalculateService;
import com.gclife.product.service.ProductService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 产品控制器
 * <AUTHOR>
 * @date 2017-11-23
 * @version V1.0
 */
@Api(tags = "核心业务系统(产品处理)", description = "核心业务系统(产品处理)")
@RestController
public class ProductController extends BaseController implements ProductApi {


    /**
     * 产品类别查询属性对象
     */
    @Autowired
    CalculateService caculateService;



    @Autowired
    private ProductService productService;

    @Override
    public ResultObject<List<ProductSimpleInfoResponse>> queryMainProduct(@RequestParam(value = "branchId",required = false) String branchId,
                                                                          @RequestParam(value = "keyword",required = false)String keyword,
                                                                          @RequestParam(value = "productCategoryCode",required = false)String productCategoryCode) {

        AppRequestHeads appRequestHeads=new AppRequestHeads();
        appRequestHeads.setDeviceChannel("ALL");
        return  productService.queryProduct(this.getCurrentLoginUsers(),branchId,keyword,productCategoryCode,appRequestHeads);
    }



    @Override
    public ResultObject<List<ProductSimpleInfoResponse>> queryAdditionalProduct(@RequestParam(value = "branchId",required = false)String branchId,
                                                                                @PathVariable("productId")String productId,
                                                                                @RequestParam(value = "relationCode", defaultValue = "", required = false) String relationCode,
                                                                                @RequestParam(value = "addParamFlag", defaultValue = "", required = false) String addParamFlag
                                                                                ) {

        AppRequestHeads appRequestHeads=new AppRequestHeads();
        appRequestHeads.setDeviceChannel("ALL");
        return  productService.queryAdditionalProduct(branchId,productId,relationCode,addParamFlag,appRequestHeads,this.getCurrentLoginUsers());
    }



    @Override
    public ResultObject<ProductSimpleInfoResponse> getProductSimpleInfo(String branchId,
                                                                        Long discountDate,
                                                                        @PathVariable("productId") String productId) {

        AppRequestHeads appRequestHeads=new AppRequestHeads();
        return  productService.getProductSimpleInfo(branchId,productId,discountDate,appRequestHeads,this.getCurrentLoginUsers());
    }


    @Override
    public ResultObject<ProductDetailedInfoResponse> getProductDetailInfo(@RequestParam(value = "branchId",required = false) String branchId,
                                                                          @PathVariable(value="productId",required =true)  String productId,
                                                                          @RequestParam(value = "applyDate",required = false) Long applyDate) {

        AppRequestHeads appRequestHeads=new AppRequestHeads();
        return  productService.getProductDetail(this.getCurrentLoginUsers(),branchId,productId,appRequestHeads,applyDate);
    }


    @Override
    public ResultObject<ProductDetailedInfoResponse> queryOneProductDetail(String productId) {
        AppRequestHeads appRequestHeads=new AppRequestHeads();
        return  productService.queryOneProductDetail(this.getCurrentLoginUsers(),productId,appRequestHeads);
    }


    @Override
    public ResultObject<ApplyResponse> trialCalculation(@RequestBody ApplyRequest applyRequest) {

        AppRequestHeads appRequestHeads = new AppRequestHeads();
        //调用服务处理数据
        ResultObject<ApplyResponse> resultObject = this.caculateService.calculation(null, InsuranceProductEnum.INSURANCE_APPLY_OPTION_MODE.REAL, applyRequest);

        return resultObject;
    }

    @Override
    public ResultObject<List<PolicyCashValueResponse>> getCashValue(@RequestBody ApplyRequest applyRequest) {
        return productService.getCashValue(applyRequest);
    }

}
