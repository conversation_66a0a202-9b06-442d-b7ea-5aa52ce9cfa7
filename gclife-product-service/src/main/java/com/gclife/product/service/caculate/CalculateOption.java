package com.gclife.product.service.caculate;

import com.alibaba.fastjson.JSON;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.inter.IEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.common.util.StringUtil;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.product.base.model.config.insurance.InsuranceErrorEnum;
import com.gclife.product.base.model.config.insurance.InsuranceProductEnum;
import com.gclife.product.base.service.product.ProductBaseBusinesService;
import com.gclife.product.base.service.product.cashvalue.ProductCashValueBaseService;
import com.gclife.product.core.jooq.tables.pojos.CalculateModePo;
import com.gclife.product.core.jooq.tables.pojos.DutyControlWopPo;
import com.gclife.product.core.jooq.tables.pojos.ProductAdditionalRatePo;
import com.gclife.product.core.jooq.tables.pojos.ProductCheckControlPo;
import com.gclife.product.core.jooq.tables.pojos.ProductDiscountCalculateModePo;
import com.gclife.product.dao.CalculateDao;
import com.gclife.product.model.bo.apply.Applicant;
import com.gclife.product.model.bo.apply.Apply;
import com.gclife.product.model.bo.apply.Coverage;
import com.gclife.product.model.bo.apply.CoverageAddPremium;
import com.gclife.product.model.bo.apply.CoverageLevel;
import com.gclife.product.model.bo.apply.CoveragePremiumFrequency;
import com.gclife.product.model.bo.apply.Duty;
import com.gclife.product.model.bo.apply.Insured;
import com.gclife.product.model.bo.apply.LoanContract;
import com.gclife.product.model.bo.insurance.DutyBo;
import com.gclife.product.model.bo.insurance.DutyGetBo;
import com.gclife.product.model.bo.insurance.DutyPayBo;
import com.gclife.product.model.bo.insurance.cashvalue.CalCashValueParamBo;
import com.gclife.product.model.bo.insurance.cashvalue.CalPremiumBo;
import com.gclife.product.model.bo.insurance.paramter.OtherCalculateParamBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @Description: 算法计算处理
 * @date 2017-11-23
 */
@Component
@Slf4j
public class CalculateOption {

    /**
     * 计算处理dao
     **/
    @Autowired
    private CalculateDao calculateDao;
    @Autowired
    ProductCashValueBaseService productCashValueBaseService;
    @Autowired
    ProductBaseBusinesService productBaseBusinesService;
    @Autowired
    PlatformInternationalBaseApi platformInternationalBaseApi;

    /**
     * 数据检查
     *
     * @param apply
     * @param applicant
     * @param insured
     * @param coverage
     * @return
     */
    public void producCheckData(Apply apply, Applicant applicant, Insured insured, Coverage coverage, DutyBo dutyBo, DutyPayBo dutyPayBo, DutyGetBo dutyGetBo) throws Exception {
        //查询检查算法条数
        List<ProductCheckControlPo> productCheckControls = this.calculateDao.queryProductCheckControl(coverage.getProductId(), apply.getInsuranceProductCalculationMode().name(), apply.getControlPlaceMode().name());
        //设置其他计算参数
        OtherCalculateParamBo otherCalculateParamBo = this.setParamOtherMap(apply, insured, coverage);

        if (AssertUtils.isNotNull(productCheckControls)) {
            for (ProductCheckControlPo productCheckControl : productCheckControls) {
                //多责任多档次处理
                if (AssertUtils.isNotEmpty(coverage.getListCoverageDuty()) && AssertUtils.isNotEmpty(coverage.getListCoverageDuty().get(0).getListCoverageLevel())) {
                    for (Duty duty : coverage.getListCoverageDuty()) {
                        for (CoverageLevel coverageLevel : duty.getListCoverageLevel()) {
                            Object obj = this.calculateValue(productCheckControl.getCalculateCode(), apply, applicant, insured, coverage, coverageLevel, duty.getDutyBo(), dutyPayBo, dutyGetBo, otherCalculateParamBo, null);
                            this.checkDataIsNotEmpty(productCheckControl, obj);
                        }
                    }
                } else if (AssertUtils.isNotNull(coverage.getListCoverageLevel())) {
                    for (CoverageLevel coverageLevel : coverage.getListCoverageLevel()) {
                        Object obj = this.calculateValue(productCheckControl.getCalculateCode(), apply, applicant, insured, coverage, coverageLevel, dutyBo, dutyPayBo, dutyGetBo, otherCalculateParamBo, null);
                        this.checkDataIsNotEmpty(productCheckControl, obj);
                    }
                } else {
                    Object obj = this.calculateValue(productCheckControl.getCalculateCode(), apply, applicant, insured, coverage, null, dutyBo, dutyPayBo, dutyGetBo, otherCalculateParamBo, null);
                    this.checkDataIsNotEmpty(productCheckControl, obj);
                }
            }
        }
    }

    private void checkDataIsNotEmpty(ProductCheckControlPo productCheckControl, Object obj) {
        // 针对产品校验错误的国际化可能存在失效的问题，现手动国际化错误
        String errorMessage = productCheckControl.getErrorMessage();
        if (AssertUtils.isNotEmpty(productCheckControl.getErrorCode())) {
            ResultObject<SyscodeResponse> error = platformInternationalBaseApi.queryOneInternational("ERROR", productCheckControl.getErrorCode(), null);
            if (!AssertUtils.isResultObjectDataNull(error)) {
                errorMessage = error.getData().getCodeName();
            }
        }
        //计算方法的问题
        String finalErrorMessage = errorMessage;
        IEnum iEnum = new IEnum() {
            @Override
            public String getCode() {
                return productCheckControl.getErrorCode();
            }

            @Override
            public String getValue() {
                return finalErrorMessage;
            }

            @Override
            public String getGroup() {
                return "PRODUCT";
            }

            @Override
            public String getCode(String s, String s1) {
                return null;
            }

            @Override
            public String getValue(String s, String s1) {
                return null;
            }

            @Override
            public String getName() {
                return productCheckControl.getErrorCode();
            }
        };

        AssertUtils.isNotEmpty(log, obj.toString(), iEnum);
    }

    /**
     * 设置产品额外参数
     *
     * @param apply
     * @param insured  被保人
     * @param coverage 当前险种
     * @return 额外参数
     */
    private OtherCalculateParamBo setParamOtherMap(Apply apply, Insured insured, Coverage coverage) {
        OtherCalculateParamBo otherCalculateParamBo = new OtherCalculateParamBo();
        List<String> allProductId = insured.getListCoverage().stream().map(Coverage::getProductId).distinct().collect(Collectors.toList());
        otherCalculateParamBo.setAllProductIdStr(JackSonUtils.toJson(allProductId));
        //责任档次
        if (AssertUtils.isNotEmpty(insured.getMainCoverage().getListCoverageDuty()) &&
                AssertUtils.isNotEmpty(insured.getMainCoverage().getListCoverageDuty().get(0).getListCoverageLevel())) {

            for (Duty duty : insured.getMainCoverage().getListCoverageDuty()) {
                otherCalculateParamBo.setMainCoverageMult(duty.getListCoverageLevel().stream().mapToLong(CoverageLevel::getMult).sum());
                otherCalculateParamBo.setMainCoveragePremium(this.getCoverageTotalPremium(duty.getListCoverageLevel()));
                otherCalculateParamBo.setMainCoverageAmount(otherCalculateParamBo.getMainCoverageAmount().add(this.getCoverageTotalAmount(duty.getListCoverageLevel())));
            }

        } else if (AssertUtils.isNotNull(insured.getMainCoverage().getListCoverageLevel())) {

            //被保人主险，险种多档次选择扩展表 Amount 累加值
            //设置主险份数
            otherCalculateParamBo.setMainCoverageMult(insured.getMainCoverage().getListCoverageLevel().stream().mapToLong(CoverageLevel::getMult).sum());
            //设置主险保费
            otherCalculateParamBo.setMainCoveragePremium(this.getCoverageTotalPremium(insured.getMainCoverage().getListCoverageLevel()));
            //设置主险保额
            otherCalculateParamBo.setMainCoverageAmount(otherCalculateParamBo.getMainCoverageAmount().add(this.getCoverageTotalAmount(insured.getMainCoverage().getListCoverageLevel())));

        } else {
            otherCalculateParamBo.setMainCoverageMult(insured.getMainCoverage().getMult());
            otherCalculateParamBo.setMainCoveragePremium(insured.getMainCoverage().getTotalPremium());
            if (AssertUtils.isNotNull(insured.getMainCoverage().getAmount())) {
                otherCalculateParamBo.setMainCoverageAmount(otherCalculateParamBo.getMainCoverageAmount().add(insured.getMainCoverage().getAmount()));
            }
        }
        //被保人主险和保额判空
        if (AssertUtils.isNotNull(insured.getMainCoverage()) && AssertUtils.isNotNull(insured.getMainCoverage().getAmount())) {
            BigDecimal amount = insured.getMainCoverage().getAmount();
            if (BigDecimal.ZERO.compareTo(amount) == 0) {
                log.info("被保人主险和保额都不为空、再判断保额是否等于0，若等于0则不覆盖 setMainCoverageAmount: {}", insured.getMainCoverage().getAmount());
            } else {
                otherCalculateParamBo.setMainCoverageAmount(insured.getMainCoverage().getAmount());
            }
        }
        long additionalCoverageMult = 0;
        BigDecimal additionalCoveragePremium = BigDecimal.ZERO;
        if (!InsuranceProductEnum.INSURANCE_PRODUCT_MAIN_PRODUCT_FLAG.MAIN.name().equals(coverage.getPrimaryFlag())) {
            //责任档次
            if (AssertUtils.isNotEmpty(coverage.getListCoverageDuty()) && AssertUtils.isNotEmpty(coverage.getListCoverageDuty().get(0).getListCoverageLevel())) {
                for (Duty duty : coverage.getListCoverageDuty()) {
                    additionalCoverageMult = additionalCoverageMult + duty.getListCoverageLevel().stream().mapToLong(CoverageLevel::getMult).sum();
                    additionalCoveragePremium = additionalCoveragePremium.add(this.getCoverageTotalPremium(duty.getListCoverageLevel()));
                    otherCalculateParamBo.setTotalCoverageAmount(otherCalculateParamBo.getTotalCoverageAmount().add(this.getCoverageTotalAmount(duty.getListCoverageLevel())));
                }
            } else if (AssertUtils.isNotNull(coverage.getListCoverageLevel())) {
                additionalCoverageMult = additionalCoverageMult + coverage.getListCoverageLevel().stream().mapToLong(CoverageLevel::getMult).sum();
                additionalCoveragePremium = additionalCoveragePremium.add(this.getCoverageTotalPremium(coverage.getListCoverageLevel()));
                otherCalculateParamBo.setTotalCoverageAmount(otherCalculateParamBo.getTotalCoverageAmount().add(this.getCoverageTotalAmount(coverage.getListCoverageLevel())));
            } else {
                additionalCoverageMult = additionalCoverageMult + coverage.getMult();
                if (AssertUtils.isNotNull(coverage.getTotalPremium())) {
                    additionalCoveragePremium = additionalCoveragePremium.add(coverage.getTotalPremium());
                }
                if (AssertUtils.isNotNull(coverage.getAmount())) {
                    otherCalculateParamBo.setTotalCoverageAmount(otherCalculateParamBo.getTotalCoverageAmount().add(coverage.getAmount()));
                }
            }
        }
        otherCalculateParamBo.setAdditionalCoverageMult(additionalCoverageMult);
        otherCalculateParamBo.setAdditionalCoveragePremium(additionalCoveragePremium);
        //设置总附加险
        BigDecimal allAdditionalCoveragePremium = BigDecimal.ZERO;
        for (Coverage coverage1 : insured.getListCoverage()) {
            if (!InsuranceProductEnum.INSURANCE_PRODUCT_MAIN_PRODUCT_FLAG.MAIN.name().equals(coverage1.getPrimaryFlag())) {
                //责任档次
                if (AssertUtils.isNotEmpty(coverage1.getListCoverageDuty()) && AssertUtils.isNotEmpty(coverage1.getListCoverageDuty().get(0).getListCoverageLevel())) {
                    for (Duty duty : coverage1.getListCoverageDuty()) {
                        allAdditionalCoveragePremium = allAdditionalCoveragePremium.add(this.getCoverageTotalPremium(duty.getListCoverageLevel()));
                    }
                } else if (AssertUtils.isNotNull(coverage1.getListCoverageLevel())) {
                    allAdditionalCoveragePremium = allAdditionalCoveragePremium.add(this.getCoverageTotalPremium(coverage1.getListCoverageLevel()));
                } else {
                    if (AssertUtils.isNotNull(coverage1.getTotalPremium())) {
                        allAdditionalCoveragePremium = allAdditionalCoveragePremium.add(coverage1.getTotalPremium());
                    }
                }
            }
        }
        otherCalculateParamBo.setAllAdditionalCoveragePremium(allAdditionalCoveragePremium);

//        String productId = coverage.getProductId();
//        boolean groupProductId = InsuranceProductEnum.PRODUCT.PRODUCT_1_PLUS.id().equals(productId) || InsuranceProductEnum.PRODUCT.PRODUCT_7_PLUS.id().equals(productId) || InsuranceProductEnum.PRODUCT.PRODUCT_11.id().equals(productId) || InsuranceProductEnum.PRODUCT.PRODUCT_12.id().equals(productId);
//        if (groupProductId && AssertUtils.isNotNull(apply.getApproveDate())) {
//            String endorseType = InsuranceProductEnum.GROUP_ENDORSE_PROJECT.ADD_INSURED.name();
//            otherCalculateParamBo.setPastMonth(this.calPastMonth(coverage, apply.getApproveDate(), apply.getEndorseApplyDate()));
//            otherCalculateParamBo.setNotEverMonth(this.calNotEverMonth(coverage.getMaturityDate(), apply.getEndorseApplyDate(), endorseType));
//            otherCalculateParamBo.setEnsureMonth(this.calEnsureMonth(coverage, endorseType));
//        }
        return otherCalculateParamBo;
    }

    private BigDecimal getCoverageTotalPremium(List<CoverageLevel> listCoverageLevel) {
        BigDecimal totalCoveragePremium = listCoverageLevel.stream().map(coverageLevel -> {
            if (AssertUtils.isNotNull(coverageLevel.getTotalPremium())) {
                return coverageLevel.getTotalPremium();
            } else {
                return BigDecimal.ZERO;
            }
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalCoveragePremium.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 累加 amount
     * @param listCoverageLevel
     * @return
     */
    private BigDecimal getCoverageTotalAmount(List<CoverageLevel> listCoverageLevel) {
        BigDecimal totalCoveragePremium = listCoverageLevel.stream().map(coverageLevel -> {
            if (AssertUtils.isNotNull(coverageLevel.getAmount())) {
                return coverageLevel.getAmount();
            } else {
                return BigDecimal.ZERO;
            }
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalCoveragePremium.setScale(2, RoundingMode.HALF_UP);
    }


    /**
     * @param calculateCode 算法编码
     * @param apply         投保单
     * @param insured       被保人
     * @param coverage      险种s
     * @param dutyBo        保险责任
     * @param dutyPayBo     缴费责任对象
     * @param dutyGetBo     给付责任对象
     * @param duty
     */
    public Object calculateValue(String calculateCode, Apply apply, Applicant applicant, Insured insured, Coverage coverage,
                                 CoverageLevel coverageLevel, DutyBo dutyBo, DutyPayBo dutyPayBo,
                                 DutyGetBo dutyGetBo, OtherCalculateParamBo otherCalculateParamBo, Duty duty) throws Exception {
        Object calculateValue = null;
        try {
            //获取算法数据
            CalculateModePo calculateModePo = calculateDao.getCalculateMode(calculateCode);
            AssertUtils.isNotNull(log, calculateModePo, InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_CODE_IS_NOT_NULL);
            //计算sql
            String calculateSql = calculateModePo.getCalculateSql();
            //获取所有参数集合
            List<String> parameterNames = this.getPamateterNames(calculateSql);
            Map<String, Object> objectMap = new HashMap<>();
            //贷款合同信息
            if (AssertUtils.isNotNull(apply)) {
                objectMap.put("apply", apply);
                if (AssertUtils.isNotNull(apply.getLoanContract())) {
                    objectMap.put("loanContract", apply.getLoanContract());
                }
            }
            //投保人
            if (AssertUtils.isNotNull(applicant)) {
                objectMap.put("applicant", applicant);
            }
            //设置被保人和主险
            if (AssertUtils.isNotNull(insured)) {
                objectMap.put("insured", insured);
                if (AssertUtils.isNotNull(insured.getMainCoverage())) {
                    objectMap.put("mainCoverage", insured.getMainCoverage());
                }
            }
            //投保单险种
            if (AssertUtils.isNotNull(coverage)) {
                String sql = this.getAddPremiumSql(coverage.getListAddPremium());
                coverage.setAddPremiumSql(sql);
                this.transWOP(coverage, insured);
                objectMap.put("coverage", coverage);
            }
            //保险等级
            if (AssertUtils.isNotNull(coverageLevel)) {
                objectMap.put("coverageLevel", coverageLevel);
            }
            //险种责任Bo对象
            if (AssertUtils.isNotNull(dutyBo)) {
                objectMap.put("dutyBo", dutyBo);
            }
            //险种责任
            if (AssertUtils.isNotNull(duty)) {
                objectMap.put("duty", duty);
            }
            //其他计算参数
            if (AssertUtils.isNotNull(otherCalculateParamBo)) {
                objectMap.put("other", otherCalculateParamBo);
            }

            calculateSql = calculateSql.replaceAll("\\{", ":").replaceAll("\\}", "");

            //移除字符串
            List<Map<String, Object>> params = this.replacateSql(calculateSql, objectMap, parameterNames);
            //执行sql计算保费
            calculateValue = this.calculateDao.getCalculateValue(calculateSql, params);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_OPTION_ERROR);
        }
        log.info("------------------返回计算值：------------------calculateValue：{}", calculateValue);
        return calculateValue;
    }

    private void transWOP(Coverage coverage, Insured insured) {
        List<DutyControlWopPo> dutyControlWopPos = productBaseBusinesService.queryDutyControlWop(coverage.getProductId(), coverage.getProductLevel());
        if (!AssertUtils.isNotEmpty(dutyControlWopPos)) {
            return;
        }
        List<String> relationProductIds = dutyControlWopPos.stream().map(DutyControlWopPo::getRelationProductId).distinct().collect(Collectors.toList());
//        if (AssertUtils.isNotNull(coverage.getWopAmount()) && coverage.getWopAmount().compareTo(BigDecimal.ZERO) > 0) {
//            return;
//        }
        BigDecimal wopAmount = insured.getListCoverage().stream().filter(value -> relationProductIds.contains(value.getProductId())).map(coverage1 -> {
            BigDecimal totalPremium = coverage1.getTotalPremium();
            if (AssertUtils.isNotEmpty(coverage1.getCoveragePremiumFrequency())) {
                Optional<CoveragePremiumFrequency> first = coverage1.getCoveragePremiumFrequency().stream().filter(coveragePremiumFrequency -> coveragePremiumFrequency.getPremiumFrequency().equals(coverage.getPremiumFrequency())).findFirst();
                if (first.isPresent()) {
                    totalPremium = first.get().getTotalPremium();
                }
            }
            if (AssertUtils.isNotNull(totalPremium)) {
                BigDecimal conversionFactor = BigDecimal.valueOf(InsuranceProductEnum.PREMIUM_FREQUENCY_CONVERSION_DIVIDE_MONTH.valueOf(coverage.getPremiumFrequency()).value());
                return totalPremium.multiply(conversionFactor).setScale(1, BigDecimal.ROUND_HALF_UP);
            } else {
                return BigDecimal.ZERO;
            }
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        coverage.setWopAmount(wopAmount);
    }

    /**
     * @param calculateCode 算法编码
     * @param apply         投保单
     * @param insured       被保人
     * @param coverage      险种s
     * @param dutyBo        保险责任
     * @param dutyPayBo     缴费责任对象
     * @param dutyGetBo     给付责任对象
     */
    public Object calculateValueConvertObj(Class tClass, String calculateCode, Apply apply, Applicant applicant, Insured insured, Coverage coverage, CoverageLevel coverageLevel, DutyBo dutyBo, DutyPayBo dutyPayBo, DutyGetBo dutyGetBo, OtherCalculateParamBo otherCalculateParamBo) throws Exception {
        Object obj = null;
        try {
            CalculateModePo calculateModePo = calculateDao.getCalculateMode(calculateCode);

            AssertUtils.isNotNull(log, calculateModePo, InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_CODE_IS_NOT_NULL);
            //计算sql
            String calculateSql = calculateModePo.getCalculateSql();
            //获取所有参数集合
            List<String> parameterNames = this.getPamateterNames(calculateSql);
            Map<String, Object> objectMap = new HashMap<>();
            if (AssertUtils.isNotNull(apply)) {
                objectMap.put("apply", apply);
                if (AssertUtils.isNotNull(apply.getLoanContract())) {
                    objectMap.put("loanContract", apply.getLoanContract());
                }
            }
            if (AssertUtils.isNotNull(applicant)) {
                objectMap.put("applicant", applicant);
            }
            if (AssertUtils.isNotNull(insured)) {
                objectMap.put("insured", insured);
                if (AssertUtils.isNotNull(insured.getMainCoverage())) {
                    objectMap.put("mainCoverage", insured.getMainCoverage());
                }
            }
            if (AssertUtils.isNotNull(coverage)) {
                coverage.setAddPremiumSql(this.getAddPremiumSql(coverage.getListAddPremium()));
                this.transWOP(coverage, insured);
                objectMap.put("coverage", coverage);
            }
            if (AssertUtils.isNotNull(coverageLevel)) {
                objectMap.put("coverageLevel", coverageLevel);
            }
            if (AssertUtils.isNotNull(dutyBo)) {
                objectMap.put("dutyBo", dutyBo);
            }
            if (AssertUtils.isNotNull(otherCalculateParamBo)) {
                objectMap.put("other", otherCalculateParamBo);
            }
            calculateSql = calculateSql.replaceAll("\\{", ":").replaceAll("\\}", "");

            //移除字符串
            List<Map<String, Object>> params = this.replacateSql(calculateSql, objectMap, parameterNames);
            log.info("calculateSql:{}", calculateSql);
            //执行sql计算保费
            obj = this.calculateDao.getCalculateValue(tClass, calculateSql, params);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_OPTION_ERROR);
        }
        return obj;
    }

    /**
     * @param activityId 算法编码
     * @param apply         投保单
     * @param insured       被保人
     * @param coverage      险种s
     * @param dutyBo        保险责任
     * @param dutyPayBo     缴费责任对象
     * @param dutyGetBo     给付责任对象
     */
    public String calculateProductDiscountValueConvertObj(String activityId, Apply apply, Applicant applicant, Insured insured, Coverage coverage, CoverageLevel coverageLevel, DutyBo dutyBo, DutyPayBo dutyPayBo, DutyGetBo dutyGetBo, OtherCalculateParamBo otherCalculateParamBo) {
        log.info("---------------算法编码start---------------");
        BigDecimal productDiscountValue = null;
        Object obj = null;
        ProductDiscountCalculateModePo calculateModePo = calculateDao.getProductDiscountCalculateMode(activityId);
        if (!AssertUtils.isNotNull(calculateModePo)) {
            return null;
        }
        //计算sql
        String calculateSql = calculateModePo.getCalculateSql();
        //获取所有参数集合
        List<String> parameterNames = this.getPamateterNames(calculateSql);
        Map<String, Object> objectMap = new HashMap<>();
        if (AssertUtils.isNotNull(apply)) {
            objectMap.put("apply", apply);
            if (AssertUtils.isNotNull(apply.getLoanContract())) {
                objectMap.put("loanContract", apply.getLoanContract());
            }
        }
        if (AssertUtils.isNotNull(applicant)) {
            objectMap.put("applicant", applicant);
        }
        if (AssertUtils.isNotNull(insured)) {
            objectMap.put("insured", insured);
            if (AssertUtils.isNotNull(insured.getMainCoverage())) {
                objectMap.put("mainCoverage", insured.getMainCoverage());
            }
        }
        if (AssertUtils.isNotNull(coverage)) {
            coverage.setAddPremiumSql(this.getAddPremiumSql(coverage.getListAddPremium()));
            this.transWOP(coverage, insured);
            objectMap.put("coverage", coverage);
        }
        if (AssertUtils.isNotNull(coverageLevel)) {
            objectMap.put("coverageLevel", coverageLevel);
        }
        if (AssertUtils.isNotNull(dutyBo)) {
            objectMap.put("dutyBo", dutyBo);
        }
        if (AssertUtils.isNotNull(otherCalculateParamBo)) {
            objectMap.put("other", otherCalculateParamBo);
        }
        calculateSql = calculateSql.replaceAll("\\{", ":").replaceAll("\\}", "");

        if (!calculateSql.contains(coverage.getProductId())) {
            return null;
        }
        try {
            //移除字符串
            List<Map<String, Object>> params = this.replacateSql(calculateSql, objectMap, parameterNames);
            log.info("计算sql[calculateSql]-> {}", calculateSql);
            //执行sql计算保费
            obj = this.calculateDao.getCalculateValue(calculateSql, params);
            log.info("执行sql计算保费-> {}", obj);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("---------------算法编码异常---------------{}", e.getMessage());
            throw new RequestException(InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_OPTION_ERROR);
        }
        this.checkDataIsNotEmpty(calculateModePo, obj);
        return obj.toString();
    }

    private void checkDataIsNotEmpty(ProductDiscountCalculateModePo calculateModePo, Object obj) {
        //计算方法的问题
        IEnum iEnum = new IEnum() {
            @Override
            public String getCode() {
                return calculateModePo.getErrorCode();
            }
            @Override
            public String getValue() {
                return calculateModePo.getErrorMessage();
            }
            @Override
            public String getGroup() {
                return "PRODUCT";
            }
            @Override
            public String getCode(String s, String s1) {
                return null;
            }
            @Override
            public String getValue(String s, String s1) {
                return null;
            }
            @Override
            public String getName() {
                return calculateModePo.getErrorCode();
            }
        };
        AssertUtils.isNotEmpty(log, obj.toString(), iEnum);
    }


    public String getAddPremiumSql(List<CoverageAddPremium> listAddPremium) {
        StringBuilder b = new StringBuilder("{\"");
        if (!AssertUtils.isNotEmpty(listAddPremium)) {
            CoverageAddPremium coverageAddPremium = new CoverageAddPremium();
            b.append(coverageAddPremium.toString());
        } else {
            for (int i = 0; i < listAddPremium.size(); i++) {
                CoverageAddPremium coverageAddPremium = listAddPremium.get(i);
                String sb = coverageAddPremium.toString();
                if (i + 1 != listAddPremium.size()) {
                    sb = sb + "\",\"";
                }
                b.append(sb);
            }
        }
        b.append("\"}");
        return b.toString();
    }

    /**
     * 计算总费用
     *
     * @param apply     投保对象
     * @param insured   被保人对象
     * @param coverage  险种对象
     * @param dutyBo    保险责任
     * @param dutyPayBo 缴费责任对象
     * @param dutyGetBo 给付责任对象
     */
    public CalPremiumBo calculatePremiumConvertObj(Apply apply, Insured insured, Coverage coverage, CoverageLevel coverageLevel, Duty duty, DutyBo dutyBo, DutyPayBo dutyPayBo, DutyGetBo dutyGetBo, String premiumCalculateModeCode) throws Exception {

        //总保费及保额
        CalPremiumBo calPremiumBo = new CalPremiumBo();

        if (!StringUtil.isNullString(premiumCalculateModeCode)) {
            OtherCalculateParamBo otherCalculateParamBo = null;

            //TODO 后续增加产品类型   团险增员计算保费
            String productId = coverage.getProductId();
            boolean groupProductId = Arrays.asList(InsuranceProductEnum.PRODUCT.PRODUCT_1_PLUS.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_7_PLUS.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_11.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_12.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_17.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_18.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_26.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_27.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_29.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_33.id()
            ).contains(productId);
            if (groupProductId && AssertUtils.isNotNull(apply.getApproveDate())) {
                String endorseType = InsuranceProductEnum.GROUP_ENDORSE_PROJECT.ADD_INSURED.name();
                otherCalculateParamBo = new OtherCalculateParamBo();
                otherCalculateParamBo.setPastMonth(this.calPastMonth(coverage, apply.getApproveDate(), apply.getEndorseApplyDate()));
                otherCalculateParamBo.setNotEverMonth(this.calNotEverMonth(coverage.getMaturityDate(), apply.getEndorseApplyDate(), endorseType));
                otherCalculateParamBo.setEnsureMonth(this.calEnsureMonth(coverage, endorseType));
                if (Arrays.asList(InsuranceProductEnum.PRODUCT.PRODUCT_17.id(),
                        InsuranceProductEnum.PRODUCT.PRODUCT_18.id(),
                        InsuranceProductEnum.PRODUCT.PRODUCT_26.id(),
                        InsuranceProductEnum.PRODUCT.PRODUCT_27.id(),
                        InsuranceProductEnum.PRODUCT.PRODUCT_29.id(),
                        InsuranceProductEnum.PRODUCT.PRODUCT_33.id()).contains(productId)) {
                    otherCalculateParamBo.setPremiumFrequencyEnsureDay(this.calPremiumFrequencyEnsureDay(coverage, apply.getApproveDate(), apply.getEndorseApplyDate()));
                    otherCalculateParamBo.setPremiumFrequencyPastDay(this.calPremiumFrequencyPastDay(coverage, apply.getApproveDate(), apply.getEndorseApplyDate()));
                    otherCalculateParamBo.setPremiumFrequencyNotPastDay(this.calPremiumFrequencyNotPastDay(coverage, apply.getApproveDate(), apply.getEndorseApplyDate()));
                }
            }
            //计算保费
            Object obj = this.calculateValueConvertObj(calPremiumBo.getClass(), premiumCalculateModeCode, apply, apply.getApplicant(), insured, coverage, coverageLevel, dutyBo, dutyPayBo, dutyGetBo, otherCalculateParamBo);
            if (AssertUtils.isNotNull(obj)) {
                calPremiumBo = (CalPremiumBo) obj;
            } else {
                //计算保费出错
                throw new RequestException(InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_OPTION_ERROR);
            }
        }
        return calPremiumBo;
    }

    private long calPremiumFrequencyEnsureDay(Coverage coverage, Long approveDate, Long endorseApplyDate) {
        int H = InsuranceProductEnum.PREMIUM_FREQUENCY_CONVERSION_DIVIDE_MONTH.valueOf(coverage.getPremiumFrequency()).value();
        //续保特殊处理
        if (DateUtils.intervalYear(DateUtils.timeToTimeLow(approveDate), DateUtils.timeToTimeLow(endorseApplyDate)) > 0) {
            approveDate = DateUtils.addStringYearsRT(approveDate, (int) DateUtils.intervalYear(DateUtils.timeToTimeLow(approveDate), DateUtils.timeToTimeLow(endorseApplyDate)));
        }
        int monthNum = new BigDecimal(12).divide(new BigDecimal(H), 0, BigDecimal.ROUND_HALF_DOWN).intValue();
        long prePeriodPremiumDate = DateUtils.addStringMonthRT(approveDate, (coverage.getPolicyCurrentYearPaymentNum().intValue() - 1) * monthNum);
        long nextPeriodPremiumDate = DateUtils.addStringMonthRT(approveDate, coverage.getPolicyCurrentYearPaymentNum().intValue() * monthNum);
        log.info("前一次缴费日期:[{}]" + DateUtils.timeStrToString(prePeriodPremiumDate));
        log.info("下一次缴费日期:[{}]" + DateUtils.timeStrToString(nextPeriodPremiumDate));
        log.info("保全受理申请日期:[{}]" + DateUtils.timeStrToString(endorseApplyDate));
        return DateUtils.intervalDay(DateUtils.timeToTimeLow(prePeriodPremiumDate), DateUtils.timeToTimeLow(nextPeriodPremiumDate));
    }

    private long calPremiumFrequencyNotPastDay(Coverage coverage, Long approveDate, Long endorseApplyDate) {
        int H = InsuranceProductEnum.PREMIUM_FREQUENCY_CONVERSION_DIVIDE_MONTH.valueOf(coverage.getPremiumFrequency()).value();
        //续保特殊处理
        if (DateUtils.intervalYear(DateUtils.timeToTimeLow(approveDate), DateUtils.timeToTimeLow(endorseApplyDate)) > 0) {
            approveDate = DateUtils.addStringYearsRT(approveDate, (int) DateUtils.intervalYear(DateUtils.timeToTimeLow(approveDate), DateUtils.timeToTimeLow(endorseApplyDate)));
        }
        int monthNum = new BigDecimal(12).divide(new BigDecimal(H), 0, BigDecimal.ROUND_HALF_DOWN).intValue();
        long prePeriodPremiumDate = DateUtils.addStringMonthRT(approveDate, (coverage.getPolicyCurrentYearPaymentNum().intValue() - 1) * monthNum);
        long nextPeriodPremiumDate = DateUtils.addStringMonthRT(approveDate, coverage.getPolicyCurrentYearPaymentNum().intValue() * monthNum);
        log.info("前一次缴费日期:[{}]" + DateUtils.timeStrToString(prePeriodPremiumDate));
        log.info("下一次缴费日期:[{}]" + DateUtils.timeStrToString(nextPeriodPremiumDate));
        log.info("保全受理申请日期:[{}]" + DateUtils.timeStrToString(endorseApplyDate));
        long premiumFrequencyNotPastDay = 0;
        if (DateUtils.timeToTimeLow(prePeriodPremiumDate) <= endorseApplyDate && endorseApplyDate <= nextPeriodPremiumDate) {
            premiumFrequencyNotPastDay = DateUtils.intervalDay(DateUtils.timeToTimeLow(endorseApplyDate), DateUtils.timeToTimeLow(nextPeriodPremiumDate));
        }
        return premiumFrequencyNotPastDay;
    }

    private long calPremiumFrequencyPastDay(Coverage coverage, Long approveDate, Long endorseApplyDate) {
        int H = InsuranceProductEnum.PREMIUM_FREQUENCY_CONVERSION_DIVIDE_MONTH.valueOf(coverage.getPremiumFrequency()).value();
        //续保特殊处理
        if (DateUtils.intervalYear(DateUtils.timeToTimeLow(approveDate), DateUtils.timeToTimeLow(endorseApplyDate)) > 0) {
            approveDate = DateUtils.addStringYearsRT(approveDate, (int) DateUtils.intervalYear(DateUtils.timeToTimeLow(approveDate), DateUtils.timeToTimeLow(endorseApplyDate)));
        }
        int monthNum = new BigDecimal(12).divide(new BigDecimal(H), 0, BigDecimal.ROUND_HALF_DOWN).intValue();
        long prePeriodPremiumDate = DateUtils.addStringMonthRT(approveDate, (coverage.getPolicyCurrentYearPaymentNum().intValue() - 1) * monthNum);
        long nextPeriodPremiumDate = DateUtils.addStringMonthRT(approveDate, coverage.getPolicyCurrentYearPaymentNum().intValue() * monthNum);
        log.info("前一次缴费日期:[{}]" + DateUtils.timeStrToString(prePeriodPremiumDate));
        log.info("下一次缴费日期:[{}]" + DateUtils.timeStrToString(nextPeriodPremiumDate));
        log.info("保全受理申请日期:[{}]" + DateUtils.timeStrToString(endorseApplyDate));
        long premiumFrequencyPastDay = 0;
        if (DateUtils.timeToTimeLow(prePeriodPremiumDate) <= endorseApplyDate && endorseApplyDate <= nextPeriodPremiumDate) {
            premiumFrequencyPastDay = DateUtils.intervalDay(DateUtils.timeToTimeLow(prePeriodPremiumDate), DateUtils.timeToTimeLow(endorseApplyDate));
        } else {
            premiumFrequencyPastDay = DateUtils.intervalDay(DateUtils.timeToTimeLow(prePeriodPremiumDate), nextPeriodPremiumDate);
        }
        return premiumFrequencyPastDay;
    }

//    public static void main(String[] args) {
//        Coverage coverage = new Coverage();
//        coverage.setPremiumFrequency("MONTH");
//        coverage.setPolicyCurrentYearPaymentNum(1L);
//        Long approveDate = DateUtils.stringToTime("2021-11-01");
//        Long endorseApplyDate = DateUtils.stringToTime("2021-11-24");
//        long l = calPremiumFrequencyEnsureDay(coverage, approveDate, endorseApplyDate);
//        System.out.println(l);
//    }

    /**
     * 计算总费用
     *
     * @param apply     投保对象
     * @param insured   被保人对象
     * @param coverage  险种对象
     * @param dutyBo    保险责任
     * @param dutyPayBo 缴费责任对象
     * @param dutyGetBo 给付责任对象
     */
    public BigDecimal calculateTotalFee(Apply apply, Insured insured, Coverage coverage, CoverageLevel coverageLevel, Duty duty, DutyBo dutyBo, DutyPayBo dutyPayBo, DutyGetBo dutyGetBo, String premiumCalculateModeCode) throws Exception {

        //总保费及保额
        BigDecimal totalFee = new BigDecimal("0.00");

        if (!StringUtil.isNullString(premiumCalculateModeCode)) {
            OtherCalculateParamBo otherCalculateParamBo = null;

            //TODO 后续增加产品类型   团险增员计算保费
            String productId = coverage.getProductId();
            boolean groupProductId = Arrays.asList(InsuranceProductEnum.PRODUCT.PRODUCT_1_PLUS.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_7_PLUS.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_11.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_12.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_17.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_18.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_26.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_27.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_29.id(),
                    InsuranceProductEnum.PRODUCT.PRODUCT_33.id()).contains(productId);
            if (groupProductId && AssertUtils.isNotNull(apply.getApproveDate())) {
                String endorseType = InsuranceProductEnum.GROUP_ENDORSE_PROJECT.ADD_INSURED.name();
                otherCalculateParamBo = new OtherCalculateParamBo();
                otherCalculateParamBo.setPastMonth(this.calPastMonth(coverage, apply.getApproveDate(), apply.getEndorseApplyDate()));
                otherCalculateParamBo.setNotEverMonth(this.calNotEverMonth(coverage.getMaturityDate(), apply.getEndorseApplyDate(), endorseType));
                otherCalculateParamBo.setEnsureMonth(this.calEnsureMonth(coverage, endorseType));
            }
            //计算保费
            Object obj = this.calculateValue(premiumCalculateModeCode, apply, apply.getApplicant(), insured, coverage, coverageLevel, dutyBo, dutyPayBo, dutyGetBo, otherCalculateParamBo, duty);
            if (AssertUtils.isNotNull(obj)) {
                totalFee = new BigDecimal(obj.toString());
            } else {
                //计算保费出错
                throw new RequestException(InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_OPTION_ERROR);
            }
        }
        return totalFee;
    }


    /**
     * 计算退保现金价值
     *
     * @param coverageLevel
     * @param calCashValueParamBo 算法参数对象
     * @param calculateCode       投保对象
     */
    public BigDecimal calculateCashValue(Coverage coverage, CoverageLevel coverageLevel, CalCashValueParamBo calCashValueParamBo, String calculateCode) throws Exception {
        //总保费及保额
        BigDecimal cashValue = new BigDecimal("0.00");
        Object calculateValue = null;
        try {
            CalculateModePo calculateModePo = calculateDao.getCalculateMode(calculateCode);
            AssertUtils.isNotNull(log, calculateModePo, InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_CODE_IS_NOT_NULL);
            //计算sql
            String calculateSql = calculateModePo.getCalculateSql();
            //获取所有参数集合
            List<String> parameterNames = this.getPamateterNames(calculateSql);
            Map<String, Object> objectMap = new HashMap<>();
            if (AssertUtils.isNotNull(calCashValueParamBo)) {
                objectMap.put("value", calCashValueParamBo);
            }
            if (AssertUtils.isNotNull(coverage)) {
                coverage.setAddPremiumSql(this.getAddPremiumSql(coverage.getListAddPremium()));
                objectMap.put("coverage", coverage);
            }
            if (AssertUtils.isNotNull(coverageLevel)) {
                objectMap.put("coverageLevel", coverageLevel);
            }
            calculateSql = calculateSql.replaceAll("\\{", ":").replaceAll("\\}", "");
            //移除字符串
            List<Map<String, Object>> params = this.replacateSql(calculateSql, objectMap, parameterNames);
            log.info("现金价值计算calculateSql:" + calculateSql);
            //执行sql计算保费
            calculateValue = this.calculateDao.getCalculateValue(calculateSql, params);
            if (AssertUtils.isNotNull(calculateValue)) {
                cashValue = new BigDecimal(calculateValue.toString());
            } else {
                //计算保费出错
                throw new RequestException(InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_OPTION_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_OPTION_ERROR);
        }
        return cashValue;
    }

    /**
     * 计算退保现金价值 附加参数
     *
     * @param calculateModeType   计算模式类型
     * @param calCashValueParamBo 算法参数对象
     */
    public BigDecimal calculateCashvalueParam(Coverage coverage, CalCashValueParamBo calCashValueParamBo, String calculateModeType) throws Exception {
        //总保费及保额
        BigDecimal cashValueParam = new BigDecimal("0.00");
        Object calculateValue = null;
        try {
            CalculateModePo calculateModePo = calculateDao.queryOneCalculateMode(coverage.getProductId(), calculateModeType);
            AssertUtils.isNotNull(log, calculateModePo, InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_CODE_IS_NOT_NULL);
            //计算sql
            String calculateSql = calculateModePo.getCalculateSql();
            //获取所有参数集合
            List<String> parameterNames = this.getPamateterNames(calculateSql);
            Map<String, Object> objectMap = new HashMap<>();
            if (AssertUtils.isNotNull(calCashValueParamBo)) {
                objectMap.put("value", calCashValueParamBo);
            }
            if (AssertUtils.isNotNull(coverage)) {
                coverage.setAddPremiumSql(this.getAddPremiumSql(coverage.getListAddPremium()));
                objectMap.put("coverage", coverage);
            }
            calculateSql = calculateSql.replaceAll("\\{", ":").replaceAll("\\}", "");
            //移除字符串
            List<Map<String, Object>> params = this.replacateSql(calculateSql, objectMap, parameterNames);
            log.info("现金价值计算calculateSql:" + calculateSql);
            //执行sql计算保费
            calculateValue = this.calculateDao.getCalculateValue(calculateSql, params);
            if (AssertUtils.isNotNull(calculateValue)) {
                cashValueParam = new BigDecimal(calculateValue.toString());
            } else {
                //计算保费出错
                throw new RequestException(InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_OPTION_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_OPTION_ERROR);
        }
        return cashValueParam;
    }

    /**
     * 计算对象数据,返回集合编码
     *
     * @param calculateCode 算法编码
     * @param objects       对像集合
     */
    public Map calculateValue(String calculateCode, List<Object> objects) throws Exception {
        Map map = null;
        try {
            CalculateModePo calculateModePo = calculateDao.getCalculateMode(calculateCode);

            //TODO:需判断算法是否存在
            AssertUtils.isNotNull(log, calculateModePo, InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_CODE_IS_NOT_NULL);

            //计算sql
            String calculateSql = calculateModePo.getCalculateSql();

            //获取所有参数集合
            List<String> parameterNames = this.getPamateterNames(calculateSql);

            calculateSql = calculateSql.replaceAll("\\{", ":").replaceAll("\\}", "");

            //移除字符串
            List<Map<String, Object>> params = this.replacateSql(calculateSql, objects, parameterNames);
            System.out.println("calculateSql:" + calculateSql);

            log.info("calculateSql-->:{}", calculateSql, params);

            //执行sql计算保费
            map = this.calculateDao.getCalculateMap(calculateSql, params);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_OPTION_ERROR);
        }
        return map;
    }


//    /**
//     * 数据检查
//     *
//     * @param apply
//     * @param applicant
//     * @param insured
//     * @param coverage
//     * @return
//     */
//    public List productCheckData(Class t, Apply apply, Applicant applicant, Insured insured, Coverage coverage, Duty duty, DutyBo dutyBo) throws Exception {
//        List list = new ArrayList<>();
//        //查询检查算法条数
//        List<ProductCheckControlPo> productCheckControls = this.calculateDao.queryProductCheckControl(coverage.getProductId(), InsuranceProductEnum.PRODUCT_CONTROL_TYPE.APPROVE.name());
//        if (AssertUtils.isNotNull(productCheckControls)) {
//            for (ProductCheckControlPo productCheckControl : productCheckControls) {
//                list = this.optionCalculateList(t, productCheckControl.getCalculateCode(), apply, applicant, insured, coverage, duty, dutyBo);
//                //计算方法的问题
//                IEnum iEnum = new IEnum() {
//                    @Override
//                    public String getCode() {
//                        return productCheckControl.getErrorCode();
//                    }
//
//                    @Override
//                    public String getValue() {
//                        return productCheckControl.getErrorMessage();
//                    }
//
//                    @Override
//                    public String getGroup() {
//                        return "PRODUCT";
//                    }
//
//                    @Override
//                    public String getCode(String s, String s1) {
//                        return null;
//                    }
//
//                    @Override
//                    public String getValue(String s, String s1) {
//                        return null;
//                    }
//
//                    @Override
//                    public String getName() {
//                        return productCheckControl.getErrorCode();
//                    }
//                };
//
//                AssertUtils.isNotEmpty(log, list, iEnum);
//            }
//        }
//        return list;
//    }

    /**
     * @param calculateCode 算法编码
     * @param apply         投保单
     * @param insured       被保人
     * @param coverage      险种s
     * @param duty          责任
     * @param dutyBo        责任数据对象
     * @param coverageLevel 产品档次对象
     */
    public List optionCalculateList(Class t, String calculateCode, Apply apply, Applicant applicant, Insured insured, Coverage coverage, Duty duty, DutyBo dutyBo, CoverageLevel coverageLevel) {
        List<Object> values = null;
        try {
            CalculateModePo calculateModePo = calculateDao.getCalculateMode(calculateCode);
            //TODO:需判断算法是否存在
            AssertUtils.isNotNull(log, calculateModePo, InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_CODE_IS_NOT_NULL);
            //计算sql
            String calculateSql = calculateModePo.getCalculateSql();
            //获取所有参数集合
            List<String> parameterNames = this.getPamateterNames(calculateSql);
            Map<String, Object> objectMap = new HashMap<>();
            if (AssertUtils.isNotNull(apply)) {
                objectMap.put("apply", apply);
                if (AssertUtils.isNotNull(apply.getLoanContract())) {
                    objectMap.put("loanContract", apply.getLoanContract());
                }
            }
            if (AssertUtils.isNotNull(applicant)) {
                objectMap.put("applicant", applicant);
            }
            if (AssertUtils.isNotNull(insured)) {
                objectMap.put("insured", insured);
                if (AssertUtils.isNotNull(insured.getMainCoverage())) {
                    objectMap.put("mainCoverage", insured.getMainCoverage());
                }
            }
            if (AssertUtils.isNotNull(coverage)) {
                coverage.setAddPremiumSql(this.getAddPremiumSql(coverage.getListAddPremium()));
                this.transWOP(coverage, insured);
                objectMap.put("coverage", coverage);
            }
            if (AssertUtils.isNotNull(duty)) {
                objectMap.put("duty", duty);
            }
            if (AssertUtils.isNotNull(dutyBo)) {
                objectMap.put("dutyBo", dutyBo);
            }
            if (AssertUtils.isNotNull(coverageLevel)) {
                objectMap.put("coverageLevel", coverageLevel);
            }
            /*
            List<Object> objects=new ArrayList<Object>();
            if(AssertUtils.isNotNull(apply)){
                objects.add(apply);
            }
            if(AssertUtils.isNotNull(applicant)){
                objects.add(applicant);
            }
            if(AssertUtils.isNotNull(insured)){
                objects.add(insured);
            }
            if(AssertUtils.isNotNull(coverage)){
                objects.add(coverage);
            }
            if(AssertUtils.isNotNull(duty)){
                objects.add(duty);
            }
            if(AssertUtils.isNotNull(dutyBo)){
                objects.add(dutyBo);
            }*/
            calculateSql = calculateSql.replaceAll("\\{", ":").replaceAll("\\}", "");
            //移除字符串
            List<Map<String, Object>> params = this.replacateSql(calculateSql, objectMap, parameterNames);
            System.out.println("calculateSql:" + calculateSql);
            //执行sql计算保费
            values = this.calculateDao.queryCalculateValue(t, calculateSql, params);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_OPTION_ERROR);
        }
        return values;
    }


    /**
     * @param tClass       对象类型
     * @param calculateSql 查询sql
     * @param apply        投保单
     * @param insured      被保人
     * @param coverage     险种s
     * @param duty         责任
     * @param dutyBo       责任数据对象
     */
    public Object optionCalculateObject(Class tClass, String calculateSql, Apply apply, Applicant applicant, Insured insured, Coverage coverage, Duty duty, DutyBo dutyBo) throws Exception {
        Object t2 = null;
        try {
            //获取所有参数集合
            List<String> parameterNames = this.getPamateterNames(calculateSql);
            List<Object> objects = new ArrayList<Object>();
            if (AssertUtils.isNotNull(apply)) {
                objects.add(apply);
            }
            if (AssertUtils.isNotNull(applicant)) {
                objects.add(applicant);
            }
            if (AssertUtils.isNotNull(insured)) {
                objects.add(insured);
            }
            if (AssertUtils.isNotNull(coverage)) {
                coverage.setAddPremiumSql(this.getAddPremiumSql(coverage.getListAddPremium()));
                this.transWOP(coverage, insured);
                objects.add(coverage);
            }
            if (AssertUtils.isNotNull(duty)) {
                objects.add(duty);
            }
            if (AssertUtils.isNotNull(dutyBo)) {
                objects.add(dutyBo);
            }
            calculateSql = calculateSql.replaceAll("\\{", ":").replaceAll("\\}", "");

            //移除字符串
            List<Map<String, Object>> params = this.replacateSql(calculateSql, objects, parameterNames);
            System.out.println("calculateSql:" + calculateSql);
            //执行sql计算保费
            t2 = this.calculateDao.getCalculateValue(tClass, calculateSql, params);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(InsuranceErrorEnum.PRODUCT_CALCULATE_MODE_OPTION_ERROR);
        }
        return t2;
    }


    /**
     * 　获取算法sql所有参数，格式为:":(.*?):"
     *
     * @param calSql 算法SQL
     * @return 返回参数集合
     */
    public List<String> getPamateterNames(String calSql) {
        Pattern p = Pattern.compile("\\{(.*?)\\}");
        Matcher m = p.matcher(calSql);
        List<String> parameterKeys = new ArrayList<String>();
        while (m.find()) {
            if (m.groupCount() > 0) {
                parameterKeys.add(m.group(1));
            }
        }
        return parameterKeys;
    }


    /**
     * 将模板sql对应的参数占位符替换为对应的数据值,模板sql参数占位符为:{objectName_fieldName},objectName代表传入对的map键值,fieldName代表字段名称
     *
     * @param sqlStr
     * @param objects
     * @param parameterNames
     * @return
     * @throws Exception
     */
    private List<Map<String, Object>> replacateSql(String sqlStr, Map<String, Object> objects, List<String> parameterNames) throws Exception {
        List<Map<String, Object>> params = new ArrayList<>();
        if (AssertUtils.isNotNull(parameterNames) && AssertUtils.isNotNull(objects) && objects.size() > 0) {
            for (String parameterName : parameterNames) {
                //拆分单词组
                String[] parameterFieldNames = parameterName.split("_");
                Object object = null;
                Field field = null;
                String objectName = "";
                String fieldName = "";
                Object objValue = null;
                if (null != parameterFieldNames && parameterFieldNames.length > 1) {
                    //获取对象名和字段名
                    objectName = parameterFieldNames[0];
                    fieldName = parameterFieldNames[1];
                    for (String key : objects.keySet()) {
                        if (key.toUpperCase().endsWith(objectName.toUpperCase())) {
                            log.info("key is : {} ", key);
                            object = objects.get(key);
                            break;
                        }
                    }

                    if (objects.containsKey(objectName)) {
                        object = objects.get(objectName);
                    }
                    if (AssertUtils.isNotNull(object)) {
                        try {
                            field = object.getClass().getDeclaredField(fieldName);
                        } catch (Exception e) {
                            log.info("模板sql参数占位符为报错位置fieldName:" + fieldName + " objectName" + objectName);
                            field = object.getClass().getSuperclass().getDeclaredField(fieldName);
                        }
                    }
                }
                try {
                    log.info("get object value: {} ", JSON.toJSON(object));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                log.info("object value: {}, field: {} ", objValue, field);
                //获取字段数据
                objValue = this.getParamerDataValue(object, field);
                log.info("2 object value: {} ", objValue);
                //if (AssertUtils.isNotNull(objValue) && !StringUtil.isNullString(parameterName)) {
                if (!StringUtil.isNullString(parameterName)) {
                    Map<String, Object> map = new HashMap<>();
                    map.put(parameterName, objValue);
                    params.add(map);
                }
            }
        }
        log.info("3 params: {} ", params);
        return params;
    }

    /**
     * 获取参数信息
     *
     * @param sqlStr
     * @return
     */
    private List<Map<String, Object>> replacateSql(String sqlStr, List<Object> objects, List<String> parameterNames) throws Exception {
        List<Map<String, Object>> params = new ArrayList<>();

        if (AssertUtils.isNotNull(parameterNames) && AssertUtils.isNotNull(objects)) {
            for (String parameterName : parameterNames) {
                //拆分单词组
                String[] parameterFieldNames = parameterName.split("_");
                Object object = null;
                Field field = null;
                String objectName = "";
                String fieldName = "";
                Object objValue = null;
                if (null != parameterFieldNames && parameterFieldNames.length > 1) {
                    //获取对象名和字段名
                    objectName = parameterFieldNames[0];
                    fieldName = parameterFieldNames[1];
                    for (Object objectTmp : objects) {
                        if (null != objectTmp && objectTmp.getClass().getTypeName().toUpperCase().endsWith(objectName.toUpperCase())) {
                            object = objectTmp;
                            break;
                        }
                    }
                    if (AssertUtils.isNotNull(object)) {
                        try {
                            field = object.getClass().getDeclaredField(fieldName);
                        } catch (Exception e) {
                            field = object.getClass().getSuperclass().getDeclaredField(fieldName);
                        }
                    }
                }
                //获取字段数据
                objValue = this.getParamerDataValue(object, field);

                if (AssertUtils.isNotNull(objValue) && !StringUtil.isNullString(parameterName)) {
                    Map<String, Object> map = new HashMap<>();
                    map.put(parameterName, objValue);
                    params.add(map);
                }
            }
        }
        return params;
    }

    /**
     * 获取对应对应的字段数据
     *
     * @param object
     * @param field
     * @return
     * @throws Exception
     */
    private Object getParamerDataValue(Object object, Field field) throws Exception {

        //基出数据格式
        List<String> baseDataTypeNames = new ArrayList<>();
        baseDataTypeNames.add(int.class.getName());
        baseDataTypeNames.add(double.class.getName());
        baseDataTypeNames.add(float.class.getName());
        baseDataTypeNames.add(BigDecimal.class.getName());
        baseDataTypeNames.add(Integer.class.getName());
        baseDataTypeNames.add(Double.class.getName());
        baseDataTypeNames.add(Float.class.getName());
        baseDataTypeNames.add(String.class.getName());
        baseDataTypeNames.add(long.class.getName());
        baseDataTypeNames.add(Long.class.getName());

        Object objValue = null;

        if (AssertUtils.isNotNull(field)) {
            //判断字段类型是否基础数据格式，是则规换
            //获取属性get方法
            PropertyDescriptor pd = null;
            try {
                pd = new PropertyDescriptor(field.getName(), object.getClass());
            } catch (Exception e) {
                System.out.println("replacateSql field not get or set method ,field name: " + field.getName());
            }
            if (AssertUtils.isNotNull(pd)) {
                Method getMethod = pd.getReadMethod();
                if (AssertUtils.isNotNull(getMethod)) {
                    //判断是否基础数据类型,如果是，则替换
                    boolean isBaseDataType = false;
                    for (String baseDataTypeName : baseDataTypeNames) {
                        if (baseDataTypeName.equals(field.getType().getName())) {
                            isBaseDataType = true;
                            break;
                        }
                    }
                    if (isBaseDataType) {
                        Object obj = getMethod.invoke(object);
                        if (AssertUtils.isNotNull(obj)) {
                            if (BigDecimal.class.getName().equals(field.getType().getName())) {
                                objValue = ((BigDecimal) obj).doubleValue();
                            } else {
                                objValue = obj;
                            }

                        }
                    }
                }
            }
        }
        return objValue;
    }


    /**
     * 移除sql中数据信息
     *
     * @param sqlStr
     * @return
     */
    private String replacateSql(String sqlStr, Object object, String objName) throws Exception {
        List<String> baseDataTypeNames = new ArrayList<>();
        baseDataTypeNames.add(int.class.getName());
        baseDataTypeNames.add(double.class.getName());
        baseDataTypeNames.add(float.class.getName());
        baseDataTypeNames.add(BigDecimal.class.getName());
        baseDataTypeNames.add(Integer.class.getName());
        baseDataTypeNames.add(Double.class.getName());
        baseDataTypeNames.add(Float.class.getName());
        baseDataTypeNames.add(String.class.getName());
        baseDataTypeNames.add(long.class.getName());
        baseDataTypeNames.add(Long.class.getName());
        //TODO:开发移除字符串
        if (AssertUtils.isNotNull(object)) {
            Field[] fields = object.getClass().getDeclaredFields();
            for (Field field : fields) {
                //获取属性get方法
                PropertyDescriptor pd = null;
                try {
                    pd = new PropertyDescriptor(field.getName(), object.getClass());
                } catch (Exception e) {
                    System.out.println("replacateSql field not get or set method ,field name: " + field.getName());
                }
                if (AssertUtils.isNotNull(pd)) {
                    Method getMethod = pd.getReadMethod();
                    if (AssertUtils.isNotNull(getMethod)) {
                        //判断是否基础数据类型,如果是，则替换
                        boolean isBaseDataType = false;
                        for (String baseDataTypeName : baseDataTypeNames) {
                            if (baseDataTypeName.equals(field.getType().getName())) {
                                isBaseDataType = true;
                                break;
                            }
                        }
                        if (isBaseDataType) {
                            String replaceStr = objName + "." + field.getName();
                            Object obj = getMethod.invoke(object);
                            if (AssertUtils.isNotNull(obj)) {
                                String value = "";
                                if (BigDecimal.class.getName().equals(field.getType().getName())) {
                                    value = ((BigDecimal) obj).doubleValue() + "";
                                } else {
                                    value = obj.toString();
                                }
                                sqlStr = sqlStr.replace(replaceStr, value);
                            }
                        }
                    }
                }
            }
        }
        return sqlStr;
    }

    /*************************************************************************************************************
     *
     *
     *                                      计算 短期险需要的参数
     *
     * ***********************************************************************************************************/

    /**
     * 该保险费所保障的已经过月数
     *
     * @param coverage         险种
     * @param approveDate      承保时间
     * @param endorseApplyDate 保全受理申请日期
     */
    public long calPastMonth(Coverage coverage, long approveDate, long endorseApplyDate) {
        //续保特殊处理=
        if (DateUtils.intervalYear(DateUtils.timeToTimeLow(approveDate), DateUtils.timeToTimeLow(endorseApplyDate)) > 0) {
            approveDate = DateUtils.addStringYearsRT(approveDate, (int) DateUtils.intervalYear(DateUtils.timeToTimeLow(approveDate), DateUtils.timeToTimeLow(endorseApplyDate)));
        }
        //判断是否已经过了保障
        if (AssertUtils.isNotNull(coverage.getCoveragePeriodEndDate())) {
            if (endorseApplyDate > DateUtils.timeToTimeLow(Long.parseLong(coverage.getCoveragePeriodEndDate()))) {
                return 12;
            }
        }
        //判断是否满期
        Long maturityDate = coverage.getMaturityDate();
        if (AssertUtils.isNotNull(maturityDate)) {
            //满期日期 - 1天 = 保单终止日期
            maturityDate = DateUtils.addStringDayRT(maturityDate, -1);
            if (endorseApplyDate > DateUtils.timeToTimeLow(maturityDate)) {
                return 12;
            }
        }

        return DateUtils.intervalMonth(DateUtils.timeToTimeLow(approveDate), DateUtils.timeToTimeLow(endorseApplyDate)) + 1;
    }

    /**
     * 该保险费缴费周期内保障经过的月数
     *
     * @param coverage         险种
     * @param approveDate      承保时间
     * @param endorseApplyDate 保全受理申请日期
     */
    public long calPremiumFrequencyPastMonth(Coverage coverage, long approveDate, long endorseApplyDate) {
        int H = InsuranceProductEnum.PREMIUM_FREQUENCY_CONVERSION_DIVIDE_MONTH.valueOf(coverage.getPremiumFrequency()).value();
        //续保特殊处理
        if (DateUtils.intervalYear(DateUtils.timeToTimeLow(approveDate), DateUtils.timeToTimeLow(endorseApplyDate)) > 0) {
            approveDate = DateUtils.addStringYearsRT(approveDate, (int) DateUtils.intervalYear(DateUtils.timeToTimeLow(approveDate), DateUtils.timeToTimeLow(endorseApplyDate)));
        }
        int monthNum = new BigDecimal(12).divide(new BigDecimal(H), 0, BigDecimal.ROUND_HALF_DOWN).intValue();
        long prePeriodPremiumDate = DateUtils.addStringMonthRT(approveDate, (coverage.getPolicyCurrentYearPaymentNum().intValue() - 1) * monthNum);
        long nextPeriodPremiumDate = DateUtils.addStringMonthRT(approveDate, coverage.getPolicyCurrentYearPaymentNum().intValue() * monthNum);
        log.info("前一次缴费日期:[{}]" + DateUtils.timeStrToString(prePeriodPremiumDate));
        log.info("下一次缴费日期:[{}]" + DateUtils.timeStrToString(nextPeriodPremiumDate));
        log.info("保全受理申请日期:[{}]" + DateUtils.timeStrToString(endorseApplyDate));
        long premiumFrequencyPastMonth = 0;
        if (DateUtils.timeToTimeLow(prePeriodPremiumDate) <= endorseApplyDate && endorseApplyDate <= nextPeriodPremiumDate) {
            premiumFrequencyPastMonth = DateUtils.intervalMonth(DateUtils.timeToTimeLow(prePeriodPremiumDate), DateUtils.timeToTimeLow(endorseApplyDate)) + 1;
            int value = InsuranceProductEnum.PREMIUM_FREQUENCY_CONVERSION_MONTH.valueOf(coverage.getPremiumFrequency()).value();
            if (premiumFrequencyPastMonth > value) {
                premiumFrequencyPastMonth = value;
            }
        }
        return premiumFrequencyPastMonth;
    }

    /**
     * 该保险费缴费周期内剩余保障月数
     *
     * @param coverage         险种
     * @param endorseApplyDate 字段
     */
    public long calPremiumFrequencyNotPastMonth(Coverage coverage, long endorseApplyDate) {
        long premiumFrequencyNotPastMonth = DateUtils.intervalMonth(DateUtils.timeToTimeLow(endorseApplyDate), DateUtils.timeToTimeLow(coverage.getMaturityDate()));
        return premiumFrequencyNotPastMonth <= 0 ? 1 : premiumFrequencyNotPastMonth;
    }

    /**
     * 该保险费所保障的未经过月数
     *
     * @param maturityDate     满期日期
     * @param endorseApplyDate 保全受理申请日期
     * @param endorseType      保全类型(增员/减员)
     * @return 未经过月数
     */
    public long calNotEverMonth(long maturityDate, long endorseApplyDate, String endorseType) {
        //满期日期 - 1天 = 保单终止日期
        maturityDate = DateUtils.addStringDayRT(maturityDate, -1);
        //判断是否已经过了保障
        if (AssertUtils.isNotNull(maturityDate)) {
            if (endorseApplyDate > DateUtils.timeToTimeLow(maturityDate)) {
                return 0;
            }
        }
        long intervalMonth = DateUtils.intervalMonth(DateUtils.timeToTimeLow(endorseApplyDate), DateUtils.timeToTimeLow(maturityDate));
        if (InsuranceProductEnum.GROUP_ENDORSE_PROJECT.ADD_INSURED.name().equals(endorseType)) {
            return intervalMonth + 1;
        }
        if (InsuranceProductEnum.GROUP_ENDORSE_PROJECT.SUBTRACT_INSURED.name().equals(endorseType)) {
            return intervalMonth;
        }
        return intervalMonth;
    }

    /**
     * 合同保障的月数
     *
     * @param coverage    险种
     * @param endorseType
     */
    public long calEnsureMonth(Coverage coverage, String endorseType) {
        //保障期限
        long coveragePeriod = coverage.getCoveragePeriod();
        //保障期限单位
        String coveragePeriodUnit = coverage.getCoveragePeriodUnit();

        long coveragePeriodMonth = 0;
        if (InsuranceProductEnum.INSURANCE_PRODUCT_COVERAGE_PERIOD_UNIT.MONTH.name().equals(coveragePeriodUnit)) {
            coveragePeriodMonth = coveragePeriod;
        } else if (InsuranceProductEnum.INSURANCE_PRODUCT_COVERAGE_PERIOD_UNIT.YEAR.name().equals(coveragePeriodUnit)) {
            //TODO 取生效日期与满期日期之间的相隔月数
            //满期日期 - 1天 = 保单终止日期
            long maturityDate = DateUtils.addStringDayRT(coverage.getMaturityDate(), -1);

            String productId = coverage.getProductId();
            if (InsuranceProductEnum.PRODUCT.PRODUCT_1_PLUS.id().equals(productId) ||
                    InsuranceProductEnum.PRODUCT.PRODUCT_7_PLUS.id().equals(productId) ||
                    InsuranceProductEnum.PRODUCT.PRODUCT_11.id().equals(productId) ||
                    InsuranceProductEnum.PRODUCT.PRODUCT_12.id().equals(productId)) {
                long intervalMonth = DateUtils.intervalMonth(DateUtils.timeToTimeLow(coverage.getEffectiveDate()), DateUtils.timeToTimeLow(maturityDate)) + 1;
                if (intervalMonth > 12) {
                    intervalMonth = 12;
                }
                //若是增员便按12来算
                if (endorseType.equals(InsuranceProductEnum.GROUP_ENDORSE_PROJECT.ADD_INSURED.name())) {
                    intervalMonth = 12;
                }
                coveragePeriodMonth = coveragePeriod * intervalMonth;
            } else {
                coveragePeriodMonth = coveragePeriod * 12;
            }
        }
        return coveragePeriodMonth;
    }

    /**
     * 退保时的保险金额。 additionalRate 附加费用率
     *
     * @param insured  被保人
     * @param coverage 险种
     */
    public BigDecimal calAdditionalRate(Insured insured, Coverage coverage) {
        BigDecimal additionalRate = new BigDecimal(0.00);
        ProductAdditionalRatePo productAdditionalRatePo = productCashValueBaseService.queryOneProductAdditionalRatePo(coverage.getProductId(), coverage.getPremiumPeriod(), 1, insured.getMainCoverage().getProductId());
        if (AssertUtils.isNotNull(productAdditionalRatePo)) {
            additionalRate = productAdditionalRatePo.getRateValue();
        }
        return additionalRate;
    }

    /**
     * 设置五号产品初始化值
     *
     * @param apply    投保单
     * @param coverage 险种
     */
    public void setProductLoanData(Apply apply, Coverage coverage) {
        LoanContract loanContract = apply.getLoanContract();
        if (Arrays.asList(InsuranceProductEnum.PRODUCT.PRODUCT_5.id(), InsuranceProductEnum.PRODUCT.PRODUCT_28.id()).contains(coverage.getProductId()) && AssertUtils.isNotNull(loanContract)) {
            String originProductLevel = coverage.getProductLevel();
            String originPaymentWay = loanContract.getPaymentWay();
            loanContract.setOriginPaymentWay(originPaymentWay);
            //还款方式为到期一次性还清的保额只能选择固定型
            if (InsuranceProductEnum.PAYMENT_WAY.FIXED_LEVEL.name().equals(originPaymentWay) && AssertUtils.isNotEmpty(originProductLevel) && !InsuranceProductEnum.PRODUCT_LEVEL.AMOUNT_FIXED.name().equals(originProductLevel)) {
                throw new RequestException(InsuranceErrorEnum.PRODUCT_BUSINESS_VALID_THE_PAYMENT_WAY_DOES_NOT_MATCH_THE_LEVEL);
            }
            //若档次为空,则根据还款方式选择默认档次
            if (!AssertUtils.isNotEmpty(originProductLevel)) {
                coverage.setProductLevel(InsuranceProductEnum.PAYMENT_WAY.FIXED_LEVEL.name().equals(originPaymentWay) ?
                        InsuranceProductEnum.PRODUCT_LEVEL.AMOUNT_FIXED.name() :
                        InsuranceProductEnum.PRODUCT_LEVEL.AMOUNT_DOWN.name());
            } else {
                //若档次不为空,则根据档次决定算费时的还款方式
                //1.若还款方式为固定保额:则还款方式只能是固定保额,2.不固定保额:判断其档次,固定型=固定保额,递减型=还款方式不变
                loanContract.setPaymentWay(InsuranceProductEnum.PAYMENT_WAY.FIXED_LEVEL.name().equals(originPaymentWay) ? originPaymentWay :
                        (InsuranceProductEnum.PRODUCT_LEVEL.AMOUNT_FIXED.name().equals(originProductLevel) ? InsuranceProductEnum.PAYMENT_WAY.FIXED_LEVEL.name() : originPaymentWay)
                );
            }
//                //判断保额不允许小于贷款金额
//                if (coverage.getAmount().compareTo(loanAmount) < 0) {
//                    throw new RequestException(InsuranceErrorEnum.PRODUCT_SUM_INSURED_CANNOT_BE_LESS_THAN_LOAN_AMOUNT);
//                }
            if (AssertUtils.isNotNull(coverage.getBaseAmount())) {
                loanContract.setLoanAmount(coverage.getBaseAmount());
            } else if (AssertUtils.isNotNull(coverage.getAmount())) {
                loanContract.setLoanAmount(coverage.getAmount());
            }
            //保额为空,默认设置贷款金额
            if (!AssertUtils.isNotNull(coverage.getBaseAmount())) {
                coverage.setBaseAmount(loanContract.getLoanAmount());
            }
            //保额为空,默认设置贷款金额
            if (!AssertUtils.isNotNull(coverage.getAmount())) {
                coverage.setAmount(loanContract.getLoanAmount());
            }
        }
    }

    public void calPaymentInstallments(Apply apply, Insured insured, Coverage coverage) {
        int paymentInstallments = 1;
        boolean isProductMonthlyTriple = Arrays.asList(InsuranceProductEnum.PRODUCT.PRODUCT_9.id(), InsuranceProductEnum.PRODUCT.PRODUCT_13.id(),
                InsuranceProductEnum.PRODUCT.PRODUCT_14.id(), InsuranceProductEnum.PRODUCT.PRODUCT_15.id(), InsuranceProductEnum.PRODUCT.PRODUCT_16A.id(), InsuranceProductEnum.PRODUCT.PRODUCT_16B.id(),
                InsuranceProductEnum.PRODUCT.PRODUCT_20.id(), InsuranceProductEnum.PRODUCT.PRODUCT_19.id(), InsuranceProductEnum.PRODUCT.PRODUCT_22.id(),
                InsuranceProductEnum.PRODUCT.PRODUCT_23A.id(), InsuranceProductEnum.PRODUCT.PRODUCT_23B.id(), InsuranceProductEnum.PRODUCT.PRODUCT_24.id()
        ).contains(coverage.getProductId())
                && InsuranceProductEnum.PREMIUM_FREQUENCY.MONTH.name().equals(coverage.getPremiumFrequency());
        if (isProductMonthlyTriple) {
            paymentInstallments = 3;
        }
        //TODO 8号附加16 月缴 暂时只缴一个月
        if (Arrays.asList(InsuranceProductEnum.PRODUCT.PRODUCT_16A.id(), InsuranceProductEnum.PRODUCT.PRODUCT_16B.id()).contains(coverage.getProductId())
                && InsuranceProductEnum.PREMIUM_FREQUENCY.MONTH.name().equals(coverage.getPremiumFrequency())
                && AssertUtils.isNotNull(insured.getMainCoverage()) && InsuranceProductEnum.PRODUCT.PRODUCT_8.id().equals(insured.getMainCoverage().getProductId())) {
            paymentInstallments = 1;
        }

        if (!AssertUtils.isNotNull(apply.getBackTrackDate())) {
            coverage.setPaymentInstallments(paymentInstallments);
            return;
        }
        long date = DateUtils.timeToTimeLow(DateUtils.getCurrentTime());
        if (AssertUtils.isNotNull(apply.getApplyDate())) {
            date = Long.parseLong(apply.getApplyDate());
        } else if (AssertUtils.isNotNull(apply.getPlanDate())) {
            date = Long.parseLong(apply.getPlanDate());
        }
        date = DateUtils.timeToTimeLow(date);
        if (InsuranceProductEnum.PREMIUM_FREQUENCY.YEAR.name().equals(coverage.getPremiumFrequency()) || InsuranceProductEnum.PREMIUM_FREQUENCY.SINGLE.name().equals(coverage.getPremiumFrequency())) {
            paymentInstallments = 1;
        } else {
            //月缴、季缴、半年缴算中间月份得对应的缴费期数 最多7个月
            int month = 1;
            while (DateUtils.addStringMonthRT(apply.getBackTrackDate(), month) <= date) {
                month++;
            }
            if (InsuranceProductEnum.PREMIUM_FREQUENCY.SEMIANNUAL.name().equals(coverage.getPremiumFrequency())) {
                paymentInstallments = month <= 6 ? 1 : 2;
            }
            if (InsuranceProductEnum.PREMIUM_FREQUENCY.SEASON.name().equals(coverage.getPremiumFrequency())) {
                paymentInstallments = month <= 3 ? 1 : (month <= 6 ? 2 : 3);
            }
            if (InsuranceProductEnum.PREMIUM_FREQUENCY.MONTH.name().equals(coverage.getPremiumFrequency())) {
                paymentInstallments = Math.max(month, paymentInstallments);
            }
        }
        coverage.setPaymentInstallments(paymentInstallments);
    }

    public String internationalProductName (String productId, String productName, String language) {
        String internationalProductName = productName;
        ResultObject<SyscodeResponse> PRODUCT_ID = platformInternationalBaseApi.queryOneInternational("PRODUCT_ID", productId, language);
        if (!AssertUtils.isResultObjectDataNull(PRODUCT_ID)) {
            internationalProductName = PRODUCT_ID.getData().getCodeName();
        }
        return internationalProductName;
    }
}
