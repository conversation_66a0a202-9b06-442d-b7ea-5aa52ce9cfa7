package com.gclife.product.service.caculate.option.policy;


import com.gclife.common.exception.RequestException;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.StringUtil;
import com.gclife.product.base.model.config.insurance.InsuranceProductEnum;
import com.gclife.product.dao.ProductExtDao;
import com.gclife.product.model.bo.insurance.ProductBo;
import com.gclife.product.model.bo.insurance.ProductInsuranceBo;
import com.gclife.product.model.bo.insurance.policy.Policy;
import com.gclife.product.model.bo.insurance.policy.PolicyCoverage;
import com.gclife.product.model.bo.insurance.policy.PolicyInsured;
import com.gclife.product.model.bo.insurance.policy.PolicyReceiptInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version V1.0
 * @Description: 险种数据处理
 * @date 2017-11-23
 */
@Component
@Slf4j
public class PolicyCoverageDataOption {
    /**
     * 产品处理Dao
     **/
    @Autowired
    private ProductExtDao productExtDao;


    /**
     * 处理数据
     *
     * @param policy   保单
     * @param insured  被保人
     * @param coverage 险种数据
     * @throws RequestException
     */
    public void optionCoverage(Policy policy, PolicyInsured insured, PolicyCoverage coverage) throws Exception {

        ProductBo productBo = coverage.getProductBo();

        if (AssertUtils.isNotNull(productBo)) {
            //1.设置生效日期
            this.optionEffective(policy, insured, coverage, productBo);
            //2.设置犹豫期日期
            this.optionHesitation(policy, insured, coverage, productBo);
        }
    }

    /**
     * 处理数据(保险责任)
     *
     * @param policy        保单
     * @param policyInsured 被保人
     * @throws RequestException
     */
    public void optionCoverage(Policy policy, PolicyInsured policyInsured) throws Exception {

        if (AssertUtils.isNotNull(policyInsured.getListPolicyCoverage())) {
            //设置主险信息
            Optional<PolicyCoverage> optional = policyInsured.getListPolicyCoverage().stream().filter(it -> it.getPrimaryFlag().equals(InsuranceProductEnum.INSURANCE_PRODUCT_MAIN_PRODUCT_FLAG.MAIN.name())).findFirst();
            optional.ifPresent(policyInsured::setMainCoverage);
            for (PolicyCoverage coverage : policyInsured.getListPolicyCoverage()) {
                this.optionCoverage(policy, policyInsured, coverage);
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(DateUtils.addStringYearsRT(DateUtils.timeToTimeTop(DateUtils.stringToTime("2024-01-02")), 1));
    }

    /**
     * 处理数据
     *
     * @param policy    保单
     * @param insured   被保人
     * @param coverage  险种数据
     * @param productBo 产品数据
     * @throws RequestException
     */
    public void optionEffective(Policy policy, PolicyInsured insured, PolicyCoverage coverage, ProductBo productBo) throws Exception {
        //查询险种实例
        ProductInsuranceBo productInsuranceBo = productBo.getProductInsuranceBo();

        if (!AssertUtils.isNotNull(productInsuranceBo)) {
            return;
        }
        PolicyCoverage mainPolicyCoverage = insured.getMainCoverage();
        boolean setFlag = false;
        //如果是承保
        if (policy.getInsurancePolicyOptionMode() == InsuranceProductEnum.INSURANCE_POLICY_OPTION_MODE.APPROVE) {
            Long currentTime = null;
            //系统所有产品生效日期都为当天生效．(3号，3升级,8号，１号)
            if (!AssertUtils.isNotNull(coverage.getEffectiveDate())) {
                currentTime = DateUtils.getCurrentTime();
            } else {
                currentTime = coverage.getEffectiveDate();
            }
            if (InsuranceProductEnum.INSURANCE_POLICY_EFFECTIVE_CALCULATION_MODE.SIGN_MORROW.name().equals(productInsuranceBo.getEffectiveCalculationMode())) {

                log.info("date time str1:" + currentTime);
                //设置为明天
                coverage.setEffectiveDate(currentTime);
                setFlag = true;
            } else if (InsuranceProductEnum.INSURANCE_POLICY_EFFECTIVE_CALCULATION_MODE.FORTHWITH.name().equals(productInsuranceBo.getEffectiveCalculationMode())) {
                log.info("date time str2:" + currentTime);
                //设置为立即生效
                coverage.setEffectiveDate(currentTime);
                setFlag = true;
            } else if (InsuranceProductEnum.INSURANCE_POLICY_EFFECTIVE_CALCULATION_MODE.INPUT.name().equals(productInsuranceBo.getEffectiveCalculationMode())) {
                //录入生效日期
                //重效日期
                setFlag = true;
                //录入也要计算截止日期
            } else if (InsuranceProductEnum.INSURANCE_POLICY_EFFECTIVE_CALCULATION_MODE.SAME_APPROVE.name().equals(productInsuranceBo.getEffectiveCalculationMode())) {
                Long effectiveDate = policy.getApproveDate();
                // 23-02-03 暂停自动回溯规则
                //5.8.3 Nalong 说仅对那些产品根据年龄计算保费的，团险和 主险是1号产品不用限制
//                if ("LIFE_INSURANCE_PERSONAL".equals(policy.getPolicyType()) && !InsuranceProductEnum.PRODUCT.PRODUCT_1.id().equals(mainPolicyCoverage.getProductId())) {
//                        /*
//                        新单承保后，如果提交投保单客户的年龄是X，但是承保日期定好的时候是年龄X+1(承保日期保持在年龄X+1的日期），
//                        那么系统要调生效日期/风险开始日期至客户年龄X+1的前一天，以确保保费与提交录单时的保费相同。（保单满期/续期应收都从生效日期算）
//                         */
//                    Date birthdayDate = StringUtil.getDateAll(insured.getBirthday() + "");
//                    int applyAge = DateUtils.getAgeYear(birthdayDate, StringUtil.getDateAll(policy.getApplyDate() + ""));
//                    int approveAge = DateUtils.getAgeYear(birthdayDate, StringUtil.getDateAll(policy.getApproveDate() + ""));
//                    if (applyAge + 1 == approveAge) {
//                            /*
//                            客户年龄X+1的前一天
//                            生日 2000/10/20
//                            投保时间 2022/10/10    21岁
//                            承保时间 2022/10/25    22岁
//                            那么生效日期2022/10/19   21岁
//                             */
//                        long effectiveDateZero = DateUtils.addStringDayRT(DateUtils.addStringYearsRT(insured.getBirthday(), approveAge), -1);
//                        //精确到承保时间的时分秒
//                        effectiveDate = effectiveDateZero + (policy.getApproveDate() - DateUtils.timeToTimeLow(policy.getApproveDate()));
//                        policy.setResetEffectiveDateFlag("YES");
//                        log.info("投保年龄与承保年龄不一致，重新设置生效日期：{}", DateUtils.timeStrToString(effectiveDate, DateUtils.FORMATE6));
//                    }
//                }
                coverage.setEffectiveDate(effectiveDate);
                //重效日期
                setFlag = true;
                //录入也要计算截止日期
            }

        } else if (policy.getInsurancePolicyOptionMode() == InsuranceProductEnum.INSURANCE_POLICY_OPTION_MODE.RECEIPT) {

        }
        //计算满期日期开始的生效日期:第二天的0时,满期日期为加相应的保障期限-1秒．
        if (setFlag) {
            //计算满期时间
            if (AssertUtils.isNotNull(coverage.getEffectiveDate()) && coverage.getEffectiveDate() > 0) {
                Long maturityDate = null;
                //Long tomorrowZeroTime = DateUtils.getBeginDayOfTomorrow().getTime();
//                    long tomorrowZeroTime = DateUtils.addStringDayRT(coverage.getEffectiveDate(), 1);
                long tomorrowZeroTime = coverage.getEffectiveDate();
                log.info("effectiveDate :{}", DateUtils.timeStrToString(coverage.getEffectiveDate(), DateUtils.FORMATE6));
                log.info("tomorrowZeroTime2 :{}", DateUtils.timeStrToString(tomorrowZeroTime, DateUtils.FORMATE6));
                if (AssertUtils.isNotNull(coverage.getEffectiveDate()) && coverage.getEffectiveDate() != 0) {
                    if (InsuranceProductEnum.INSURANCE_PRODUCT_COVERAGE_PERIOD_UNIT.AGE.name().equals(coverage.getCoveragePeriodUnit())) {
                        //年龄(被保人出生年月加初保人年龄满前一天23点59分59秒)
                        //被保人出生年月
                        Date insuredBirthdayDate = StringUtil.getDateAll(insured.getBirthday() + "");
                        //转换保单生效日
                        Date policyEffectiveDate = StringUtil.getDateAll(coverage.getEffectiveDate() + "");
                        //获取岁数
                        int age = DateUtils.getAgeYear(insuredBirthdayDate, policyEffectiveDate);
                        //保单截止日
                        maturityDate = DateUtils.addStringYearsRT(tomorrowZeroTime, Integer.parseInt(coverage.getCoveragePeriod()) - age) - 1;

                        //maturityDate = policyEffectiveDateEnd;// DateUtils.addStringYearsRT(insured.getBirthday(), Integer.valueOf(coverage.getCoveragePeriod()) + 1) - 1;
                        log.info("date time str１:" + maturityDate);
                    } else if (InsuranceProductEnum.INSURANCE_PRODUCT_COVERAGE_PERIOD_UNIT.DAY.name().equals(coverage.getCoveragePeriodUnit())) {
                        //天
                        maturityDate = DateUtils.addStringDayRT(tomorrowZeroTime, Integer.parseInt(coverage.getCoveragePeriod()) + 1) - 1;
                        log.info("date time str2:" + maturityDate);
                    } else if (InsuranceProductEnum.INSURANCE_PRODUCT_COVERAGE_PERIOD_UNIT.HOUR.name().equals(coverage.getCoveragePeriodUnit())) {
                        //小时
                        maturityDate = tomorrowZeroTime + ((Integer.parseInt(coverage.getCoveragePeriod()) + 1) * 24 * 60 * 60 * 1000) - 1;
                        log.info("date time str3:" + maturityDate);
                    } else if (InsuranceProductEnum.INSURANCE_PRODUCT_COVERAGE_PERIOD_UNIT.MONTH.name().equals(coverage.getCoveragePeriodUnit())) {
                        //月
                        maturityDate = DateUtils.addStringMonthRT(tomorrowZeroTime, Integer.parseInt(coverage.getCoveragePeriod()) + 1) - 1;
                        log.info("date time str4:" + maturityDate);
                    } else if (InsuranceProductEnum.INSURANCE_PRODUCT_COVERAGE_PERIOD_UNIT.YEAR.name().equals(coverage.getCoveragePeriodUnit())) {
                        //年
                        maturityDate = DateUtils.addStringYearsRT(tomorrowZeroTime, Integer.parseInt(coverage.getCoveragePeriod())) - 1;
                        //20200221调整：个险短期险满期日期改为当日23:59:59  20200904调整：团险短期险满期日期改为当日23:59:59
                        if ("1".equals(coverage.getCoveragePeriod())) {
                            //主险
                            if (InsuranceProductEnum.INSURANCE_PRODUCT_MAIN_PRODUCT_FLAG.MAIN.name().equals(coverage.getPrimaryFlag())) {
                                maturityDate = DateUtils.addStringYearsRT(DateUtils.timeToTimeTop(coverage.getEffectiveDate()), Integer.parseInt(coverage.getCoveragePeriod()));
                                log.info("短期险主险:" + maturityDate);
                            }
                            //附加险是短期险，主险是短期险
                            if ("1".equals(mainPolicyCoverage.getCoveragePeriod()) && InsuranceProductEnum.INSURANCE_PRODUCT_MAIN_PRODUCT_FLAG.ADDITIONAL.name().equals(coverage.getPrimaryFlag())) {
                                maturityDate = DateUtils.addStringYearsRT(DateUtils.timeToTimeTop(coverage.getEffectiveDate()), Integer.parseInt(coverage.getCoveragePeriod()));
                                log.info("附加险是短期险，主险是短期险:" + maturityDate);
                            }
                        }
                        log.info("date time str5:" + maturityDate);
                    }
                }
                if (AssertUtils.isNotNull(maturityDate) && maturityDate > 0) {
                    coverage.setMaturityDate(maturityDate);
                }
                if (InsuranceProductEnum.INSURANCE_PRODUCT_MAIN_PRODUCT_FLAG.MAIN.name().equals(productInsuranceBo.getMainProductFlag())) {
                    //设置生效时间
                    policy.setEffectiveDate(coverage.getEffectiveDate());
                    //设置过期时间
                    policy.setMaturityDate(maturityDate);
                }
            }
        }
    }


    /**
     * 处理犹豫期过期时间
     *
     * @param policy    保单
     * @param insured   被保人
     * @param coverage  险种数据
     * @param productBo 产品数据
     * @throws RequestException
     */
    public void optionHesitation(Policy policy, PolicyInsured insured, PolicyCoverage coverage, ProductBo productBo) throws Exception {

        //设置犹豫期
        ProductInsuranceBo productInsuranceBo = productBo.getProductInsuranceBo();

        if (AssertUtils.isNotNull(productInsuranceBo)) {
            //如果是主险

            if (InsuranceProductEnum.INSURANCE_PRODUCT_MAIN_PRODUCT_FLAG.MAIN.name().equals(productInsuranceBo.getMainProductFlag())) {
                policy.setHesitation(0L);
                if (null != productInsuranceBo.getHesitateDay() && productInsuranceBo.getHesitateDay() > -1) {
                    policy.setHesitation(productInsuranceBo.getHesitateDay());
                }
//                //产品犹豫期为０的犹豫期截止日期不存储到保单．
//                if (productInsuranceBo.getHesitateDay() == 0) {
//                    return;
//                }
                //如果是回执签收日期来处理
                if (InsuranceProductEnum.INSURANCE_POLICY_HESITATE_CALCULATION_MODE.RECEIPT_SIGN_START.name().equals(productInsuranceBo.getHesitateCalculationMode())) {
                    //判断当前模式是否是回执处理,如果是回执签收日开始计算,则处理犹豫期截止日
//                    if (InsuranceProductEnum.INSURANCE_POLICY_OPTION_MODE.RECEIPT==policy.getInsurancePolicyOptionMode()) {
                    //获取回执对象
                    PolicyReceiptInfo policyReceiptInfo = policy.getPolicyReceiptInfo();

                    if (AssertUtils.isNotNull(policyReceiptInfo) && policyReceiptInfo.getReceiptDate() > 0 && policyReceiptInfo.getReceiptDate() >= DateUtils.timeToTimeLow(policy.getApproveDate())) {
                        //计算犹豫期过期时间
                        //#5#24产品犹豫期特殊处理  保险期间为1年无犹豫期，保险期间大于1年的犹豫期为21天
                        if (Arrays.asList(InsuranceProductEnum.PRODUCT.PRODUCT_5.id(), InsuranceProductEnum.PRODUCT.PRODUCT_24.id(), InsuranceProductEnum.PRODUCT.PRODUCT_28.id()).contains(coverage.getProductId())) {
                            if ("1".equals(coverage.getCoveragePeriod()) && InsuranceProductEnum.INSURANCE_PRODUCT_COVERAGE_PERIOD_UNIT.YEAR.name().equals(coverage.getCoveragePeriodUnit())) {
                                policy.setHesitation(0L);
                                policy.setHesitationEndDate(policyReceiptInfo.getReceiptDate());
                            } else {
                                Long hesitationEndDate = DateUtils.addStringDayRT(policyReceiptInfo.getReceiptDate(), policy.getHesitation() + 1) - 1;
                                policy.setHesitationEndDate(hesitationEndDate);
                            }
                        } else if (productInsuranceBo.getHesitateDay() == 0) {
                            policy.setHesitationEndDate(policyReceiptInfo.getReceiptDate());
                        } else {
                            Long hesitationEndDate = DateUtils.addStringDayRT(policyReceiptInfo.getReceiptDate(), policy.getHesitation() + 1) - 1;
                            policy.setHesitationEndDate(hesitationEndDate);
                        }
                    }
//                    }

                } else if (InsuranceProductEnum.INSURANCE_POLICY_HESITATE_CALCULATION_MODE.APPROVE_START.name().equals(productInsuranceBo.getHesitateCalculationMode())) {
                    //承保日期
//                    if (InsuranceProductEnum.INSURANCE_POLICY_OPTION_MODE.APPROVE==policy.getInsurancePolicyOptionMode()) {
                    if (policy.getApproveDate() > 0) {
                        if (productInsuranceBo.getHesitateDay() == 0) {
                            policy.setHesitationEndDate(policy.getApproveDate());
                        } else {
//                            //计算犹豫期过期时间
                            Long hesitationEndDate = DateUtils.addStringDayRT(policy.getApproveDate(), policy.getHesitation() + 1) - 1;
                            policy.setHesitationEndDate(hesitationEndDate);
                        }
                    }
//                    }
                } else if (InsuranceProductEnum.INSURANCE_POLICY_HESITATE_CALCULATION_MODE.EFFECTIVE_START.name().equals(productInsuranceBo.getHesitateCalculationMode())) {
                    //承保日期
//                    if (InsuranceProductEnum.INSURANCE_POLICY_OPTION_MODE.APPROVE==policy.getInsurancePolicyOptionMode()) {
                    if (policy.getEffectiveDate() > 0) {
                        if (productInsuranceBo.getHesitateDay() == 0) {
                            policy.setHesitationEndDate(policy.getEffectiveDate());
                        } else {
//                            //计算犹豫期过期时间
                            Long hesitationEndDate = DateUtils.addStringDayRT(policy.getEffectiveDate(), policy.getHesitation() + 1) - 1;
                            policy.setHesitationEndDate(hesitationEndDate);
                        }
                    }
//                    }
                }
            }
        }
    }


}
