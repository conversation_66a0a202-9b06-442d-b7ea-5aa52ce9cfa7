package com.gclife.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.party.api.PartyApi;
import com.gclife.party.model.response.PromotionalCodesResponse;
import com.gclife.product.base.model.config.insurance.InsuranceErrorEnum;
import com.gclife.product.base.model.config.insurance.InsuranceProductEnum;
import com.gclife.product.base.service.product.ActivityBaseService;
import com.gclife.product.core.jooq.tables.pojos.ActivityPublishPo;
import com.gclife.product.form.calculate.ApplyRequest;
import com.gclife.product.interfaces.AgentServiceInterface;
import com.gclife.product.model.bo.apply.Apply;
import com.gclife.product.model.bo.apply.Coverage;
import com.gclife.product.model.bo.insurance.policy.Policy;
import com.gclife.product.model.feign.AgentRespFc;
import com.gclife.product.model.request.insurance.policy.PolicyRequest;
import com.gclife.product.model.response.insurnce.policy.PolicyResponse;
import com.gclife.product.service.CalculateService;
import com.gclife.product.service.caculate.CalculateOption;
import com.gclife.product.service.caculate.convert.ApplyConvert;
import com.gclife.product.service.caculate.convert.policy.PolicyConvert;
import com.gclife.product.service.caculate.option.ApplyDataOption;
import com.gclife.product.service.caculate.option.policy.PolicyDataOption;
import com.gclife.product.service.caculate.option.policy.returns.PolicyReturnDataOption;
import com.gclife.product.service.caculate.option.returns.ApplyReturnDataOption;
import com.gclife.product.service.caculate.pretreatment.ApplyDataPretreatment;
import com.gclife.product.service.caculate.query.ApplyDataQuery;
import com.gclife.product.service.caculate.query.policy.PolicyDataQuery;
import com.gclife.product.service.caculate.validate.business.ApplyBusinessValidate;
import com.gclife.product.service.caculate.validate.parameter.ApplyParameterValidate;
import com.gclife.product.service.convert.CalculateConvertService;
import com.gclife.product.vo.apply.ApplyResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @Description: 保费试算
 * @date 2017-11-23
 */
@Service
@Slf4j
public class CalculateServiceImpl extends BaseBusinessServiceImpl implements CalculateService {


    /**
     * 参数校验
     **/
    @Autowired
    ApplyParameterValidate applyParameterValidate;

    /**
     * 数据转换
     **/
    @Autowired
    ApplyConvert applyConvert;

    /**
     * 查询数据
     **/
    @Autowired
    ApplyDataQuery applyDataQuery;

    /**
     * 业务校验
     **/
    @Autowired
    ApplyBusinessValidate applyBusinessValidate;

    /**
     * 数据处理
     **/
    @Autowired
    ApplyDataOption applyDataOption;
    @Autowired
    CalculateOption calculateOption;

    /**
     * 数据返回
     **/
    @Autowired
    ApplyReturnDataOption applyReturnDataOption;

    @Autowired
    AgentServiceInterface agentServiceInterface;

    @Autowired
    CalculateConvertService calculateConvertService;

    /**
     * 保单处理
     **/

    @Autowired
    PolicyConvert policyConvert;

    @Autowired
    PolicyDataQuery policyDataQuery;

    @Autowired
    PolicyDataOption policyDataOption;

    @Autowired
    PolicyReturnDataOption policyReturnDataOption;
    @Autowired
    ActivityBaseService activityBaseService;
    @Autowired
    PartyApi partyApi;
    @Autowired
    ApplyDataPretreatment applyDataPretreatment;

    /**
     * 试算保费
     *
     * @param user         用户对象
     * @param applyRequest 保费试算对象
     * @return
     */
    @Override
    public ResultObject<ApplyResponse> calculation(Users user,
                                                   InsuranceProductEnum.INSURANCE_APPLY_OPTION_MODE insuranceProductCalculationMode,
                                                   ApplyRequest applyRequest) {

        log.info("[大中华人寿-{}] - 保费计算start", DateUtils.dateToString(new Date()));
        ResultObject<ApplyResponse> resultObject = new ResultObject<>();
        AgentRespFc agentRespFc = null;
        try {
            if (user == null) {
                AssertUtils.isNotNull(log, applyRequest.getBranchId(), InsuranceErrorEnum.PRODUCT_CALCULATE_SALES_BRANCH_ID_IS_NOT_NULL);
                applyRequest.setBranchId(applyRequest.getBranchId());
            } else {
                //查询用户机构
                agentRespFc = agentServiceInterface.queryOneSimpleAgent(user.getUserId()).getData();
                AssertUtils.isNotNull(log, agentRespFc, InsuranceErrorEnum.PRODUCT_CALCULATE_AGENT_IS_NOT_NULL);
                applyRequest.setBranchId(agentRespFc.getBranchId());
                applyRequest.setLanguage(user.getLanguage());
            }

            //如果网销优惠码字段不为空的话，则为参与优惠码活动
            if (AssertUtils.isNotEmpty(applyRequest.getPromotionalCode())) {
                ResultObject<PromotionalCodesResponse> promotionalCodes = partyApi.queryPromotionalCodes(applyRequest.getPromotionalCode(), "YES");
                if (!AssertUtils.isResultObjectDataNull(promotionalCodes)) {
                    applyRequest.setParticipationDiscountFlag(TerminologyConfigEnum.WHETHER.YES.name());
                    // 查询产品活动发布数据
                    ActivityPublishPo activityPublishPo = activityBaseService.queryActivityPublish(promotionalCodes.getData().getActivityId());
                    if (AssertUtils.isNotNull(activityPublishPo)) {
                        applyRequest.setPromotionType(activityPublishPo.getPromotionType());
                    } else {
                        applyRequest.setPromotionalCode(null);
                    }
                } else {
                    applyRequest.setPromotionalCode(null);
                }
            }

            //1.验证数据格式
            if (InsuranceProductEnum.INSURANCE_APPLY_OPTION_MODE.TRIAL == insuranceProductCalculationMode) {
                this.applyParameterValidate.calculateParameterValidateApplyRequestTrial(applyRequest);
            } else {
                //真算
                this.applyParameterValidate.calculateParameterValidateApplyRequest(applyRequest);
            }
            //2.转换成内部数据
            Apply apply = this.applyConvert.calculateConvertApply(insuranceProductCalculationMode, applyRequest);
            //保存标志
            apply.setInsuranceProductCalculationMode(insuranceProductCalculationMode);
            //3.获取数据
            this.applyDataQuery.queryApply(apply);

            // 3.5 预处理数据
            applyDataPretreatment.pretreatment(apply);
            //3.验证业务数据
            apply.setControlPlaceMode(InsuranceProductEnum.CONTROL_PLACE_MODE.CALCULATE_PREMIUM_PRE);
            this.applyBusinessValidate.businessValidateApply(apply);
            //4.处理业务数据(计算)
            applyDataOption.optionApply(apply);
            // 4.1 若有折扣后再计算折扣系数则再算一次
            if ("CALCULATE_AFTER_DISCOUNT".equals(apply.getDynamicDiscountValue())) {
                for (Coverage coverage : apply.getListInsured().get(0).getListCoverage()) {
                    String result = calculateOption.calculateProductDiscountValueConvertObj(apply.getActivityId(), apply, apply.getApplicant(), apply.getListInsured().get(0), coverage, null, null, null, null, null);
                    if (AssertUtils.isNotNull(result) && result.contains("YES")) {
                        apply.setResetFlag(true);
                        String[] split = result.split("\\|");
                        apply.setSpecialDiscount(new BigDecimal(split[0]));
                        apply.setOriginSpecialDiscount(new BigDecimal(split[2]));
                        apply.setMaxDiscountAmount(new BigDecimal(split[3]));
                    }
                }
                if (apply.isResetFlag()) {
                    applyDataOption.optionApply(apply);
                }
            }
            //3.再次验证业务数据
            apply.setControlPlaceMode(InsuranceProductEnum.CONTROL_PLACE_MODE.CALCULATE_PREMIUM_BACK);
            this.applyBusinessValidate.businessValidateApply(apply);

            applyDataOption.resetScratchCardCoverage(applyRequest.getActivationCode(), apply);

            //5.回写业务数据
            ApplyResponse applyResponse = applyReturnDataOption.optionApply(apply);
            //6.转换回写数据
            applyResponse = calculateConvertService.convertApplyResponse(applyResponse);
            log.info("[大中华人寿-{}]-保费计算end", DateUtils.dateToString(new Date()));
            //如果网销优惠码字段不为空的话，返回优惠码
            if (AssertUtils.isNotEmpty(applyRequest.getPromotionalCode())) {
                applyResponse.setPromotionalCode(applyRequest.getPromotionalCode());
            }
            resultObject.setData(applyResponse);
        } catch (Exception e) {
            log.info("[大中华人寿-{}]-保费计算error:{}", DateUtils.dateToString(new Date()), e.getMessage());
            e.printStackTrace();
            this.setResultObjectException(log, resultObject, e, InsuranceErrorEnum.PRODUCT_CALCULATE_ERROR);
        }
        return resultObject;
    }


    /**
     * 试算保费
     *
     * @param insurancePolicyOptionMode 保单处理模式
     * @param policyRequest             保费试算对象
     * @return
     */
    @Override
    public ResultObject<PolicyResponse> optionPolicyData(
            InsuranceProductEnum.INSURANCE_POLICY_OPTION_MODE insurancePolicyOptionMode,
            PolicyRequest policyRequest) {
        log.info("[大中华人寿-" + DateUtils.dateToString(new Date()) + "]-承保日期计算start:" + JSON.toJSONString(policyRequest));
        ResultObject<PolicyResponse> resultObject = new ResultObject();
        AgentRespFc agentRespFc = null;
        try {
//            //1.验证数据格式
//            if(InsuranceProductEnum.INSURANCE_PRODUCT_CALCULATION_MODE.TRIAL==insuranceProductCalculationMode) {
//                this.applyParameterValidate.calculateParameterValidateApplyRequestTrial(applyRequest);
//            }else{
//                //真算
//                this.applyParameterValidate.calculateParameterValidateApplyRequest(applyRequest);
//            }
            //2.转换成内部数据
            Policy policy = this.policyConvert.convertPolicy(policyRequest);
            //保存标志
            policy.setInsurancePolicyOptionMode(insurancePolicyOptionMode);
            //3.获取数据
            this.policyDataQuery.queryPolicy(policy);
            //3.验证业务数据
            //this.applyBusinessValidate.businessValidateApply(apply);
            //4.处理业务数据(计算)
            this.policyDataOption.optionPolicy(policy);
            //5.回写业务数据
            PolicyResponse policyResponse = policyReturnDataOption.optionPolicy(policy);
            resultObject.setData(policyResponse);
            System.out.println(JSON.toJSON(policy));
        } catch (RequestException e) {
            if (e.getiEnum() == null) {
                resultObject.setIenum(InsuranceErrorEnum.PRODUCT_CALCULATE_ERROR);
            } else {
                //错误设置
                resultObject.setIenum(e.getiEnum());
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultObject.setIenum(InsuranceErrorEnum.PRODUCT_CALCULATE_ERROR);
        }
        return resultObject;
    }


//    /**
//     * 保费计算
//     * @param user 用户对象
//     * @param applyRequest 保费计算对象
//     * @return
//     */
//    @Override
//    public ResultObject<ApplyResponse> calculation(Users user, ApplyRequest applyRequest){
//
//        ResultObject<ApplyResponse> resultObject=new ResultObject();
//        try {
//            //查询用户机构
//            AgentRespFc agentRespFc=agentServiceInterface.agentByUserIdGet(user.getUserId()).getData();
//            AssertUtils.isNotNull(log,agentRespFc, ErrorEnum.PRODUCT_AGENT_IS_NOT_NULL);
//            applyRequest.setBranchId(agentRespFc.getBranchId());
//            //1.验证数据格式
//            this.applyParameterValidate.calculateParameterValidateApplyRequest(applyRequest);
//            //2.转换成内部数据
//            Apply apply=this.applyConvert.calculateConvertApply(agentRespFc,applyRequest);
//            //3.获取数据
//            this.applyDataQuery.queryApply(apply);
//            //3.验证业务数据
//            this.applyBusinessValidate.businessValidateApply(apply);
//            //4.处理业务数据(计算)
//            applyDataOption.optionApply(apply);
//            //5.回写业务数据
//            ApplyResponse applyResponse=applyReturnDataOption.optionApply(apply);
//            //6.转换回写数据
//            applyResponse=calculateConvertService.convertApplyResponse(applyResponse);
//            resultObject.setData(applyResponse);
//        }catch (RequestException e) {
//            if (e.getiEnum() == null) {
//                resultObject.setIenum(ErrorEnum.PRODUCT_CALCULATE_ERROR);
//            } else {
//                //错误设置
//                resultObject.setIenum(e.getiEnum());
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            resultObject.setIenum(ErrorEnum.PRODUCT_CALCULATE_ERROR);
//        }
//        return  resultObject;
//    }
//
//
//    /**
//     * 保费计算
//     * @param applyRequest 保费计算对象
//     * @return
//     */
//    @Override
//    public ResultObject<ApplyResponse> calculation(ApplyRequest applyRequest){
//
//        ResultObject<ApplyResponse> resultObject=new ResultObject();
//        try {
//            AssertUtils.isNotNull(log,applyRequest.getBranchId(), ErrorEnum.PRODUCT_SALES_BRANCH_ID_IS_NOT_NULL);
//
//            //1.验证数据格式
//            this.applyParameterValidate.calculateParameterValidateApplyRequest(applyRequest);
//            //2.转换成内部数据
//            Apply apply=this.applyConvert.calculateConvertApply(applyRequest);
//            //3.获取数据
//            this.applyDataQuery.queryApply(apply);
//            //3.验证业务数据
//            this.applyBusinessValidate.businessValidateApply(apply);
//            //4.处理业务数据(计算)
//            applyDataOption.optionApply(apply);
//            //5.回写业务数据
//            ApplyResponse applyResponse=applyReturnDataOption.optionApply(apply);
//            //6.转换回写数据
//            applyResponse=calculateConvertService.convertApplyResponse(applyResponse);
//            resultObject.setData(applyResponse);
//        }catch (RequestException e) {
//            if (e.getiEnum() == null) {
//                resultObject.setIenum(ErrorEnum.PRODUCT_CALCULATE_ERROR);
//            } else {
//                //错误设置
//                resultObject.setIenum(e.getiEnum());
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            resultObject.setIenum(ErrorEnum.PRODUCT_CALCULATE_ERROR);
//        }
//        return  resultObject;
//    }
}
