package com.gclife.product.service.product.cashvalue.calculate;

import com.alibaba.fastjson.JSON;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.product.base.model.config.ProductEnum;
import com.gclife.product.base.model.config.insurance.InsuranceProductEnum;
import com.gclife.product.base.service.product.cashvalue.ProductCashValueBaseService;
import com.gclife.product.core.jooq.tables.pojos.CashValuePo;
import com.gclife.product.core.jooq.tables.pojos.CashValueRatePo;
import com.gclife.product.core.jooq.tables.pojos.ProductAdditionalRatePo;
import com.gclife.product.model.bo.apply.Apply;
import com.gclife.product.model.bo.apply.Coverage;
import com.gclife.product.model.bo.apply.CoverageLevel;
import com.gclife.product.model.bo.apply.Insured;
import com.gclife.product.model.bo.insurance.cashvalue.CalCashValueParamBo;
import com.gclife.product.service.caculate.CalculateOption;
import com.gclife.product.vo.insurnce.policy.PolicyCashValueResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * create 18-8-29
 * description: 长期险计算
 */
@Service
@Slf4j
public class CashValueLongBusinessService extends BaseBusinessServiceImpl {
    @Autowired
    private CalculateOption calculateOption;

    @Autowired
    private ProductCashValueBaseService productCashValueBaseService;

    /**
     * 计算保单险种退保现价
     * （一）计算时点在两个保单年度中间时段：
     * 1．在缴费期内
     * 2．在缴费期后
     * （二）计算时点正好在保单年度末时点上：不需计算，直接取保单年度末现金价值
     * <p>
     * 参数解释:
     * ------------------------------------------------------------------------------------------------------
     * t ：第 t 个保单年度；
     * S'(Sr) ：在该交费期间内已经过天数；
     * S ：在该保单年度内已经过天数；
     * h ：交费方式（年交， h 取值 1；半年交， h 取值 2；季交， h 取值 4；月交， h 取值 12） ；
     * v ：离下次交费日的天数；
     * tCVx ： x 岁投保的保单在第 t 保单年度末的保单现金价值（含生存给付）；
     * ptCVx :x 岁投保的保单在第 t-1 保单年度末的保单现金价值（含生存给付）；
     * SB ：第 t-1 个保单年度末生存给付；
     * NPcv ：计算现金价值采纳的评估净保费（考虑到系统上线数据中可能暂时无该数据信息，采用毛
     * 保费近似计算的方法，取值为毛保费乘以（ 1-费用率），费用率可根据渠道进行设定，如
     * 个险 20%等）；
     * SA ：基本保险金额；
     * SA'(SAr) ：退保时的保险金额。
     * 其中"'"使用r替换
     * ------------------------------------------------------------------------------------------------------
     * 13号产品所需参数：
     * SE ：评估天数（第t个保单年度已交保费保障天数）；
     * r ：公司规定利率
     * D ：对应利率的应计理算的天数
     * ------------------------------------------------------------------------------------------------------
     *
     * @param apply         保单
     * @param insured       被保人
     * @param coverage      险种
     * @param coverageLevel
     * @param cashValuePos  现价集合  @return BigDecimal
     */
    public BigDecimal calculateRetreatsCashValue(Apply apply, Insured insured, Coverage coverage, CoverageLevel coverageLevel, List<CashValuePo> cashValuePos, PolicyCashValueResponse policyCashValueResponse) throws Exception {
        //计算参数
        BigDecimal cashValue = new BigDecimal("0.00");
        log.info("投保日期:[{}]", DateUtils.timeStrToString(apply.getApplyDate()));
        log.info("申请保全日期:[{}]", DateUtils.timeStrToString(apply.getEndorseApplyDate()));
        log.info("被保人生日[{}],投保年纪[{}]岁,当前年纪[{}]:", DateUtils.timeStrToString(insured.getBirthday()), insured.getAge(), DateUtils.intervalYear(Long.parseLong(insured.getBirthday()), apply.getEndorseApplyDate()));
        //计算参数
        CalCashValueParamBo calCashValueParamBo = new CalCashValueParamBo();
        try {
            int t = this.calParamT(coverage.getPolicyYear());
            //承保时退保则不需要减去一天
            int paramInt = -1;
            if (t == 0) {
                paramInt = 0;
            }
            //前一次保单年度缴费日期=险种生效日期+年度-1天   一天是送的,真正的保单年度末应该不包括这天
            long recentlyApplyDate = DateUtils.addStringDayRT(DateUtils.addStringYearsRT(DateUtils.timeToTimeLow(coverage.getEffectiveDate()), t), paramInt);
            //参数t
            calCashValueParamBo.setT(t);
            //计算保费
            calCashValueParamBo.setActualPremium(this.calActualPremium(coverage));
            //计算保费
            calCashValueParamBo.setTotalPremium(this.calTotalPremium(coverage));
            //参数tCVx
            calCashValueParamBo.setTcvx(this.calTCVx(cashValuePos, t));
            //ptCVx
            calCashValueParamBo.setPtCVx(this.caltPtCVx(cashValuePos, t));
            //additionalRate
            calCashValueParamBo.setAdditionalRate(this.calAdditionalRate(insured, coverage, t));
            //h
            calCashValueParamBo.setH(this.calH(coverage));
            //mode
            calCashValueParamBo.setMode(this.calMode(coverage, coverage.getEffectiveDate(), apply.getEndorseApplyDate(), t, calCashValueParamBo.getH()));
            //s
            calCashValueParamBo.setS(this.calS(coverage, recentlyApplyDate, apply.getEndorseApplyDate(), t, calCashValueParamBo.getMode(), calCashValueParamBo.getH()));
            //sr
            calCashValueParamBo.setSr(this.calSr(coverage, recentlyApplyDate, apply.getEndorseApplyDate(), t, calCashValueParamBo.getMode(), calCashValueParamBo.getS(), calCashValueParamBo.getH()));
            //v
            calCashValueParamBo.setV(this.calV(coverage, recentlyApplyDate, apply.getEndorseApplyDate(), t, calCashValueParamBo.getMode(), calCashValueParamBo.getS(), calCashValueParamBo.getH()));
            //sa
            calCashValueParamBo.setSa(this.calSa(coverage));
            //sar
            calCashValueParamBo.setSar(this.calSar(coverage, calCashValueParamBo, cashValuePos));
            //sb
            calCashValueParamBo.setSb(this.calSb(insured, coverage, calCashValueParamBo, apply.getEndorseApplyDate()));
            //se
            calCashValueParamBo.setSe(this.calSE(coverage, recentlyApplyDate, apply.getEndorseApplyDate(), calCashValueParamBo.getH()));
            //rValue
            calCashValueParamBo.setR(this.calRValue(coverage, recentlyApplyDate, apply.getEndorseApplyDate(), calCashValueParamBo));
            //#21产品第一次分娩保险金 //#21产品第二次分娩保险金
            this.setSb1Sb2(coverage, recentlyApplyDate, apply.getEndorseApplyDate(), calCashValueParamBo);

            cashValue = calculateOption.calculateCashValue(coverage, coverageLevel, calCashValueParamBo, ProductEnum.CALCULATE_MODE_CODE.CAL_PRODUCT_LONG_RETREAT_CASH_VALUE.name());
            log.info("计算结果现金价值为:[{}]", cashValue);
            //两位小数处理
            cashValue = cashValue.doubleValue() > 0 ? cashValue.setScale(2, BigDecimal.ROUND_HALF_UP) : new BigDecimal(0);
            policyCashValueResponse.getFactor().put("calculateParams", calCashValueParamBo);
        } catch (Exception e) {
            policyCashValueResponse.getFactor().put("calculateParams", calCashValueParamBo);
            throw e;
        }
        return cashValue;
    }

    private void setSb1Sb2(Coverage coverage, long recentlyApplyDate, Long endorseApplyDate, CalCashValueParamBo calCashValueParamBo) {
        Long policyYear = coverage.getPolicyYear();
        /*
         * 〖SB〗^1	第一次分娩保险金：在第2 - 5个保单年度末期间，如果被保险人未领取第一次分娩保险金，则SB1=保险金额的2%，如果已领取，SB1=0。
         * 〖SB〗^2	第二次分娩保险金：在第3 - 5个保单年度末期间，如果被保险人未领取第二次分娩保险金，则SB2=保险金额的2%，如果已领取，SB2=0。
         */
        //TODO 针对21号产品的分娩保险金暂时写死，后期更改，默认客户已经领，不然到时万一发出去了，因为已经退保，收不回来
//        if (InsuranceProductEnum.PRODUCT.PRODUCT_21.id().equals(coverage.getProductId())) {
//            if (2 <= policyYear && policyYear <= 5) {
//                calCashValueParamBo.setSb1(coverage.getAmount().multiply(new BigDecimal("0.02")).setScale(2, BigDecimal.ROUND_HALF_UP));
//            }
//            if (3 <= policyYear && policyYear <= 5) {
//                calCashValueParamBo.setSb2(coverage.getAmount().multiply(new BigDecimal("0.02")).setScale(2, BigDecimal.ROUND_HALF_UP));
//            }
//        }
    }


    /*************************************************************************************************************
     *
     *
     *                                      计算 短期险需要的参数
     *
     * ***********************************************************************************************************/
    /**
     * 计算参数 保费
     *
     * @param coverage 保单年度
     */
    private BigDecimal calActualPremium(Coverage coverage) {
        BigDecimal actualPremium = new BigDecimal("0.00");
        if (!AssertUtils.isNotNull(coverage.getActualPremium()) || coverage.getActualPremium().compareTo(BigDecimal.ZERO) == 0) {
            coverage.setActualPremium(coverage.getTotalPremium());
        }
        if (InsuranceProductEnum.PREMIUM_FREQUENCY.YEAR.name().equals(coverage.getPremiumFrequency())) {
            actualPremium = coverage.getActualPremium();
        }
        //半年缴费
        else if (InsuranceProductEnum.PREMIUM_FREQUENCY.SEMIANNUAL.name().equals(coverage.getPremiumFrequency())) {
            actualPremium = coverage.getActualPremium().divide(new BigDecimal("0.52"), 2, BigDecimal.ROUND_HALF_UP);
        }
        //季度缴费
        else if (InsuranceProductEnum.PREMIUM_FREQUENCY.SEASON.name().equals(coverage.getPremiumFrequency())) {
            actualPremium = coverage.getActualPremium().divide(new BigDecimal("0.27"), 2, BigDecimal.ROUND_HALF_UP);
        }
        //月度缴费
        else if (InsuranceProductEnum.PREMIUM_FREQUENCY.MONTH.name().equals(coverage.getPremiumFrequency())) {
            actualPremium = coverage.getActualPremium().divide(new BigDecimal("0.09"), 2, BigDecimal.ROUND_HALF_UP);
        }
        return actualPremium;
    }

    /**
     * 计算参数 期缴保费
     *
     * @param coverage 保单年度
     */
    private BigDecimal calTotalPremium(Coverage coverage) {
        BigDecimal totalPremium = new BigDecimal("0.00");
        if (InsuranceProductEnum.PREMIUM_FREQUENCY.YEAR.name().equals(coverage.getPremiumFrequency())) {
            totalPremium = coverage.getTotalPremium();
        }
        //半年缴费
        else if (InsuranceProductEnum.PREMIUM_FREQUENCY.SEMIANNUAL.name().equals(coverage.getPremiumFrequency())) {
            totalPremium = coverage.getTotalPremium().divide(new BigDecimal("0.52"), 2, BigDecimal.ROUND_HALF_UP);
        }
        //季度缴费
        else if (InsuranceProductEnum.PREMIUM_FREQUENCY.SEASON.name().equals(coverage.getPremiumFrequency())) {
            totalPremium = coverage.getTotalPremium().divide(new BigDecimal("0.27"), 2, BigDecimal.ROUND_HALF_UP);
        }
        //月度缴费
        else if (InsuranceProductEnum.PREMIUM_FREQUENCY.MONTH.name().equals(coverage.getPremiumFrequency())) {
            totalPremium = coverage.getTotalPremium().divide(new BigDecimal("0.09"), 2, BigDecimal.ROUND_HALF_UP);
        }
        return totalPremium;
    }

    /**
     * 计算参数t
     *
     * @param policyYear 保单年度
     */
    private int calParamT(long policyYear) {
        return (int) policyYear - 1;
    }

    /**
     * x 岁投保的保单在第 t 保单年度末的保单现金价值（含生存给付）； t和数据库的数据policyYear相差1 .policyYear从1开始.t从0开始
     *
     * @param cashValuePos 投保日期
     * @param t            第 t 个保单年度
     */
    private BigDecimal calTCVx(List<CashValuePo> cashValuePos, long t) {
        BigDecimal tCVx = new BigDecimal("0.00");
        Optional<CashValuePo> optional = cashValuePos.stream().filter(cashValuePo -> cashValuePo.getPolicyYear() == (t + 1)).findFirst();
        if (optional.isPresent()) {
            tCVx = optional.get().getCashValue();
        }
        return tCVx;
    }

    /**
     * x 岁投保的保单在第 t-1 保单年度末的保单现金价值（含生存给付）； t和数据库的数据policyYear相差1 .policyYear从1开始.t从0开始
     *
     * @param cashValuePos 投保日期
     * @param t            第 t 个保单年度
     */
    private BigDecimal caltPtCVx(List<CashValuePo> cashValuePos, long t) {
        BigDecimal ptCVx = new BigDecimal("0.00");
        Optional<CashValuePo> optional = cashValuePos.stream().filter(cashValuePo -> cashValuePo.getPolicyYear() == t).findFirst();
        if (optional.isPresent()) {
            ptCVx = optional.get().getCashValue();
        }
        return ptCVx;
    }


    /**
     * 退保时的保险金额。 additionalRate 附加费用率
     *
     * @param insured  被保人
     * @param coverage 险种
     * @param t        保单经过的年度
     */
    private BigDecimal calAdditionalRate(Insured insured, Coverage coverage, long t) {
        BigDecimal additionalRate = new BigDecimal("0.00");
        ProductAdditionalRatePo productAdditionalRatePo = productCashValueBaseService.queryOneProductAdditionalRatePo(coverage.getProductId(), coverage.getPremiumPeriod(), t + 1, insured.getMainCoverage().getProductId());
        if (AssertUtils.isNotNull(productAdditionalRatePo)) {
            additionalRate = productAdditionalRatePo.getRateValue();
        }
        return additionalRate;
    }

    /**
     * 交费方式（年交， h 取值 1；半年交， h 取值 2；季交， h 取值 4；月交， h 取值 12） ；
     *
     * @param coverage 险种
     */
    private int calH(Coverage coverage) {
        int h = 1;
        if (InsuranceProductEnum.PREMIUM_FREQUENCY.YEAR.name().equals(coverage.getPremiumFrequency())) {
            h = 1;
        }
        //半年缴费
        else if (InsuranceProductEnum.PREMIUM_FREQUENCY.SEMIANNUAL.name().equals(coverage.getPremiumFrequency())) {
            h = 2;
        }
        //季度缴费
        else if (InsuranceProductEnum.PREMIUM_FREQUENCY.SEASON.name().equals(coverage.getPremiumFrequency())) {
            h = 4;
        }
        //月度缴费
        else if (InsuranceProductEnum.PREMIUM_FREQUENCY.MONTH.name().equals(coverage.getPremiumFrequency())) {
            h = 12;
        }
        return h;
    }

    /**
     * 计算模式 计算模式 INSIDE:在缴费期之内, BEHIND:在缴费期之后  EXACTLY:计算时点正好在保单年度末时点上：不需计算，直接取保单年度末现金价值
     *
     * @param effectiveDate    投保日期
     * @param endorseApplyDate 保全受理申请日期
     * @param t                保单年度
     * @param h                交费方式
     */
    private String calMode(Coverage coverage, long effectiveDate, long endorseApplyDate, int t, int h) {
        String mode = "null";
        //保单下年度缴费日期
        long nextPolicyEndDate = DateUtils.addStringDayRT(DateUtils.addStringYearsRT(DateUtils.timeToTimeLow(effectiveDate), t + 1), -1);
        //未缴费完成
        if (t < coverage.getPremiumPeriod()) {
            //申请时间是下次投保日期,且刚t保单年度全部缴费完成 :如月缴:缴满12期
            if (DateUtils.timeToTimeLow(nextPolicyEndDate) == DateUtils.timeToTimeLow(endorseApplyDate) && coverage.getPolicyCurrentYearPaymentNum() == h) {
                mode = InsuranceProductEnum.CASH_VALUE_CAL_MODE.EXACTLY.name();
            } else {
                mode = InsuranceProductEnum.CASH_VALUE_CAL_MODE.INSIDE.name();
            }
        } else {
            //判断是否处在缴费完成
            if (DateUtils.timeToTimeLow(nextPolicyEndDate) == DateUtils.timeToTimeLow(endorseApplyDate)) {
                mode = InsuranceProductEnum.CASH_VALUE_CAL_MODE.EXACTLY.name();
            } else {
                //缴费完成
                mode = InsuranceProductEnum.CASH_VALUE_CAL_MODE.BEHIND.name();
            }
        }
        return mode;
    }

    /**
     * 评估时间点对应的公司利率的利息值
     * (1 - rn) ^ (Dn / 365)
     *
     * @param coverage            险种
     * @param recentlyApplyDate   前一次保单年度缴费日期
     * @param endorseApplyDate    保全受理申请日期
     * @param calCashValueParamBo 现价参数
     * @return 评估时间点对应的公司利率的利息值
     */
    private BigDecimal calRValue(Coverage coverage, long recentlyApplyDate, Long endorseApplyDate, CalCashValueParamBo calCashValueParamBo) {
        int h = calCashValueParamBo.getH();
        BigDecimal rValue = BigDecimal.ONE;
        List<CashValueRatePo> cashValueRatePos = productCashValueBaseService.queryCashValueRatePo(coverage.getProductId());
        if (!AssertUtils.isNotEmpty(cashValueRatePos)) {
            return rValue;
        }
        calCashValueParamBo.setI(cashValueRatePos.get(0).getRateValue().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));

        int monthNum = new BigDecimal(12).divide(new BigDecimal(h), 0, BigDecimal.ROUND_HALF_DOWN).intValue();
        //评估时间点
        long evaluationTimePoint = DateUtils.addStringMonthRT(recentlyApplyDate, coverage.getPolicyCurrentYearPaymentNum().intValue() * monthNum);
        log.info("评估时间点:[{}]", DateUtils.timeStrToString(evaluationTimePoint));
        //评估天数 = 退保时间 - 评估时间点
        int index = 1;
        for (CashValueRatePo cashValueRatePo : cashValueRatePos) {
            Long D = null;
            //退保时间至评估时间点 在利率天数区间内
            boolean inside = cashValueRatePo.getPublicStartDate() <= evaluationTimePoint && (!AssertUtils.isNotNull(cashValueRatePo.getPublicEndDate()) || evaluationTimePoint <= cashValueRatePo.getPublicEndDate());
            boolean inside2 = cashValueRatePo.getPublicStartDate() <= endorseApplyDate && (!AssertUtils.isNotNull(cashValueRatePo.getPublicEndDate()) || endorseApplyDate <= cashValueRatePo.getPublicEndDate());
            if (inside && inside2) {
                D = evaluationTimePoint >= endorseApplyDate ? DateUtils.intervalDay(evaluationTimePoint, endorseApplyDate) : DateUtils.intervalDay(endorseApplyDate, evaluationTimePoint);
            }

            if (evaluationTimePoint <= endorseApplyDate) {
                boolean inside3 = cashValueRatePo.getPublicStartDate() <= evaluationTimePoint && (!AssertUtils.isNotNull(cashValueRatePo.getPublicEndDate()) || evaluationTimePoint <= cashValueRatePo.getPublicEndDate());
                boolean inside4 = cashValueRatePo.getPublicStartDate() <= endorseApplyDate && (AssertUtils.isNotNull(cashValueRatePo.getPublicEndDate()) && cashValueRatePo.getPublicEndDate() <= endorseApplyDate);
                if (inside3 && inside4) {
                    D = DateUtils.intervalDay(evaluationTimePoint, cashValueRatePo.getPublicEndDate()) + 1;
                }
                boolean inside5 = evaluationTimePoint <= cashValueRatePo.getPublicStartDate() && (AssertUtils.isNotNull(cashValueRatePo.getPublicEndDate()) && evaluationTimePoint <= cashValueRatePo.getPublicEndDate());
                boolean inside6 = endorseApplyDate >= cashValueRatePo.getPublicStartDate() && (AssertUtils.isNotNull(cashValueRatePo.getPublicEndDate()) && cashValueRatePo.getPublicEndDate() <= endorseApplyDate);
                if (inside5 && inside6) {
                    D = DateUtils.intervalDay(cashValueRatePo.getPublicStartDate(), cashValueRatePo.getPublicEndDate()) + 1;
                }
                boolean inside7 = evaluationTimePoint <= cashValueRatePo.getPublicStartDate() && !AssertUtils.isNotNull(cashValueRatePo.getPublicEndDate());
                boolean inside8 = cashValueRatePo.getPublicStartDate() <= endorseApplyDate && !AssertUtils.isNotNull(cashValueRatePo.getPublicEndDate());
                if (inside7 && inside8) {
                    D = DateUtils.intervalDay(cashValueRatePo.getPublicStartDate(), endorseApplyDate);
                }
            }
            if (!AssertUtils.isNotNull(D) || D == 0) {
                continue;
            }

            log.info("D{}:{},r{}:{}", index, D, index, cashValueRatePo.getRateValue());
            BigDecimal base = BigDecimal.ONE.add(cashValueRatePo.getRateValue().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
            log.info("base{}:{}", index, base);
            BigDecimal power = new BigDecimal(D).divide(new BigDecimal(365), 8, BigDecimal.ROUND_HALF_UP);
            log.info("power{}:{}", index, power);
            BigDecimal thisRValue = BigDecimal.valueOf(Math.pow(base.doubleValue(), power.doubleValue()));
            log.info("rValue{}:{}", index, thisRValue);
            rValue = rValue.multiply(thisRValue);
            index++;
        }
        log.info("rValue:{}", rValue);
        return rValue;
    }

    /**
     * 评估天数（第t个保单年度已交保费保障天数）
     *
     * @param coverage          险种
     * @param recentlyApplyDate 前一次保单年度缴费日期
     * @param endorseApplyDate  保全受理申请日期
     * @param h                 交费方式（年交， h 取值 1；半年交， h 取值 2；季交， h 取值 4；月交， h 取值 12）
     * @return 评估天数（第t个保单年度已交保费保障天数）
     */
    private long calSE(Coverage coverage, long recentlyApplyDate, Long endorseApplyDate, int h) {
        log.info("保全申请日期:[{}]", DateUtils.timeStrToString(endorseApplyDate, DateUtils.FORMATE6));
        log.info("前一次保单年度缴费日期:[{}]", DateUtils.timeStrToString(recentlyApplyDate, DateUtils.FORMATE6));
        //在缴费期内
        int monthNum = new BigDecimal(12).divide(new BigDecimal(h), 0, BigDecimal.ROUND_HALF_DOWN).intValue();
        //前一次缴费日期
        long prePeriodPremiumDate = DateUtils.addStringMonthRT(recentlyApplyDate, (coverage.getPolicyCurrentYearPaymentNum().intValue() - 1) * monthNum);
        //下一次缴费日期
        long nextPeriodPremiumDate = DateUtils.addStringMonthRT(recentlyApplyDate, coverage.getPolicyCurrentYearPaymentNum().intValue() * monthNum);

        //nextCoveragePremiumDate=DateUtils.addStringMonthRT(nextPeriodPremiumDate,2);
        log.info("前一次缴费日期:[{}]", DateUtils.timeStrToString(prePeriodPremiumDate));
        log.info("下一次缴费日期:[{}]", DateUtils.timeStrToString(nextPeriodPremiumDate));
        long se = DateUtils.intervalDay(recentlyApplyDate, nextPeriodPremiumDate);
        if (se > 365) {
            se = 365;
        }
        return se;
    }

    /**
     * 在该保单年度内已经过天数 s；
     *
     * @param coverage          险种
     * @param recentlyApplyDate 险种生效日期
     * @param endorseApplyDate  保全受理申请日期
     * @param t                 保单经过的年度
     */
    private long calS(Coverage coverage, long recentlyApplyDate, long endorseApplyDate, int t, String mode, int h) {

        //保单当前年度已过天数
        long s = 0;
        //前一次保单年度缴费日期

        log.info("保全申请日期:[{}]", DateUtils.timeStrToString(endorseApplyDate, DateUtils.FORMATE6));
        log.info("前一次保单年度缴费日期:[{}]", DateUtils.timeStrToString(recentlyApplyDate, DateUtils.FORMATE6));
        //前一次缴费日期
        long prePeriodPremiumDate = 0;
        //下一次缴费日期
        long nextPeriodPremiumDate = 0;
        //失效日期
        long nextCoveragePremiumDate = 0;
        //在缴费期内
        if (InsuranceProductEnum.CASH_VALUE_CAL_MODE.INSIDE.name().equals(mode)) {
            int monthNum = new BigDecimal(12).divide(new BigDecimal(h), 0, BigDecimal.ROUND_HALF_DOWN).intValue();
            prePeriodPremiumDate = DateUtils.addStringMonthRT(recentlyApplyDate, (coverage.getPolicyCurrentYearPaymentNum().intValue() - 1) * monthNum);
            nextPeriodPremiumDate = DateUtils.addStringMonthRT(recentlyApplyDate, coverage.getPolicyCurrentYearPaymentNum().intValue() * monthNum);
            //nextCoveragePremiumDate=DateUtils.addStringMonthRT(nextPeriodPremiumDate,2);
            log.info("前一次缴费日期:[{}]" + DateUtils.timeStrToString(prePeriodPremiumDate));
            log.info("下一次缴费日期:[{}]" + DateUtils.timeStrToString(nextPeriodPremiumDate));
            log.info("保单失效日期(宽限期):[{}]" + DateUtils.timeStrToString(nextCoveragePremiumDate));
            //申请退保日期在下次缴费期之前 --还在期缴保费保障期限之内
            if (endorseApplyDate < nextPeriodPremiumDate) {
                s = DateUtils.intervalDay(recentlyApplyDate, endorseApplyDate);
            } else {
                s = DateUtils.intervalDay(recentlyApplyDate, nextPeriodPremiumDate);
            }
        } else if (InsuranceProductEnum.CASH_VALUE_CAL_MODE.BEHIND.name().equals(mode)) {
            s = DateUtils.intervalDay(recentlyApplyDate, endorseApplyDate);
        } else if (InsuranceProductEnum.CASH_VALUE_CAL_MODE.EXACTLY.name().equals(mode)) {
            s = 365;
        }
        //保障天数不能超过365，现价计算都是基于365天来算的
        if (s > 365) {
            s = 365;
        }
        return s;
    }


    /**
     * 在该交费期间内已经过天数 S '= sr
     *
     * @param coverage          险种
     * @param recentlyApplyDate 投保日期
     * @param endorseApplyDate  保全受理申请日期
     * @param t                 保单经过的年度
     * @param mode              模式
     */
    private long calSr(Coverage coverage, long recentlyApplyDate, long endorseApplyDate, int t, String mode, long s, int h) {
        log.info("计算s'参数开始 calSr ");
        long sr = 0;
        log.info("前一次保单年度缴费日期:[{}]" + DateUtils.timeStrToString(recentlyApplyDate));
        //前一次缴费日期
        long prePeriodPremiumDate = 0;
        //下一次缴费日期
        long nextPeriodPremiumDate = 0;
        //在缴费期内
        if (InsuranceProductEnum.CASH_VALUE_CAL_MODE.INSIDE.name().equals(mode)) {
            int monthNum = new BigDecimal(12).divide(new BigDecimal(h), 0, BigDecimal.ROUND_HALF_DOWN).intValue();
            prePeriodPremiumDate = DateUtils.addStringMonthRT(recentlyApplyDate, (coverage.getPolicyCurrentYearPaymentNum().intValue() - 1) * monthNum);
            nextPeriodPremiumDate = DateUtils.addStringMonthRT(recentlyApplyDate, coverage.getPolicyCurrentYearPaymentNum().intValue() * monthNum);

            log.info("前一次缴费日期:[{}]" + DateUtils.timeStrToString(prePeriodPremiumDate));
            log.info("下一次缴费日期:[{}]" + DateUtils.timeStrToString(nextPeriodPremiumDate));

            //申请退保日期在下次缴费期之前 --还在期缴保费保障期限之内
            if (nextPeriodPremiumDate > endorseApplyDate) {
                sr = DateUtils.intervalDay(prePeriodPremiumDate, endorseApplyDate);
            } else {
                sr = DateUtils.intervalDay(prePeriodPremiumDate, nextPeriodPremiumDate);
            }
        }
        //保单年度末时点上
        else if (InsuranceProductEnum.CASH_VALUE_CAL_MODE.EXACTLY.name().equals(mode)) {
            sr = s;
        }

        //已经缴费完成
        else if (InsuranceProductEnum.CASH_VALUE_CAL_MODE.BEHIND.name().equals(mode)) {
            sr = s;
        }
        if (sr > 365) {
            sr = 365;
        }
        return sr;
    }


    /**
     * 离下次交费日的天数； v
     *
     * @param recentlyApplyDate 生效日期
     * @param endorseApplyDate  保全受理申请日期
     */
    private long calV(Coverage coverage, long recentlyApplyDate, long endorseApplyDate, int t, String mode, long s, int h) {
        long v = 0;
        //下一次缴费日期
        long nextPeriodPremiumDate = 0;
        //在缴费期内
        if (InsuranceProductEnum.CASH_VALUE_CAL_MODE.INSIDE.name().equals(mode)) {
            int monthNum = new BigDecimal(12).divide(new BigDecimal(h), 0, BigDecimal.ROUND_HALF_DOWN).intValue();
            nextPeriodPremiumDate = DateUtils.addStringMonthRT(recentlyApplyDate, coverage.getPolicyCurrentYearPaymentNum().intValue() * monthNum);
            //申请退保日期在下次缴费期之前 --还在期缴保费保障期限之内
            if (nextPeriodPremiumDate > endorseApplyDate) {
                v = DateUtils.intervalDay(endorseApplyDate, nextPeriodPremiumDate);
            } else {
                v = 0;
            }
        }
        if (v > 365) {
            v = 365;
        }
        return v;
    }

    /**
     * 基本保险金额 sa
     *
     * @param coverage 险种
     */
    private BigDecimal calSa(Coverage coverage) {
        BigDecimal sa = new BigDecimal("1.00");
//        if(!AssertUtils.isNotNull(coverage.getAmount())){
//            sa=new BigDecimal(1.00);
//        }else {
//            sa=coverage.getAmount();
//        }
        if (Arrays.asList(InsuranceProductEnum.PRODUCT.PRODUCT_5.id(), InsuranceProductEnum.PRODUCT.PRODUCT_28.id()).contains(coverage.getProductId())) {
            sa = coverage.getAmount();
        }
        return sa;
    }


    /**
     * 退保时的保险金额。 SA' = sar
     *
     * @param coverage     险种
     * @param cashValuePos
     */
    private BigDecimal calSar(Coverage coverage, CalCashValueParamBo calCashValueParamBo, List<CashValuePo> cashValuePos) throws Exception {
        BigDecimal sar = new BigDecimal("1.00");
//        if(!AssertUtils.isNotNull(coverage.getAmount())){
//            sar=new BigDecimal(1.00);
//        }else {
//            //计算保险金额
//           sar = calculateOption.calculateCashvalueParam(coverage,calCashValueParamBo,InsuranceProductEnum.CALCULATE_MODE_TYPE.RETREAT_AMOUNT.name());
//        }
        if (Arrays.asList(InsuranceProductEnum.PRODUCT.PRODUCT_5.id(), InsuranceProductEnum.PRODUCT.PRODUCT_28.id()).contains(coverage.getProductId())) {
            Optional<CashValuePo> optional = cashValuePos.stream().filter(cashValuePo -> cashValuePo.getPolicyYear().equals(coverage.getPolicyYear())).findFirst();
            if (optional.isPresent()) {
                sar = optional.get().getAmount();
            }
        }
        return sar;
    }


    /**
     * 第 t-1 个保单年度末生存给付
     *
     * @param coverage            险种
     * @param calCashValueParamBo 退保现金计算参数
     */
    private BigDecimal calSb(Insured insured, Coverage coverage, CalCashValueParamBo calCashValueParamBo, long endorseApplyDate) {
        BigDecimal sb = new BigDecimal("0.00");
        try {
            //计算当前被保人年龄
            long currentInsuredAge = DateUtils.intervalYear(DateUtils.timeToTimeLow(Long.parseLong(insured.getBirthday())), endorseApplyDate);
            calCashValueParamBo.setCurrentInsuredAge(currentInsuredAge);
            //计算保险金额
            sb = calculateOption.calculateCashvalueParam(coverage, calCashValueParamBo, InsuranceProductEnum.CALCULATE_MODE_TYPE.SURVIVE_BONUS.name());
        } catch (Exception e) {
            log.error("计算生存金出错");
            e.printStackTrace();
        }
        return sb;
    }


}
