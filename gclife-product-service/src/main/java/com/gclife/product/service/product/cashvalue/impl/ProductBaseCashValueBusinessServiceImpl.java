package com.gclife.product.service.product.cashvalue.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.BaseErrorConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.product.base.model.config.insurance.InsuranceErrorEnum;
import com.gclife.product.base.model.config.insurance.InsuranceProductEnum;
import com.gclife.product.base.service.product.cashvalue.ProductCashValueBaseService;
import com.gclife.product.core.jooq.tables.daos.CashCalculateLogDao;
import com.gclife.product.core.jooq.tables.pojos.CalculateModePo;
import com.gclife.product.core.jooq.tables.pojos.CashCalculateLogPo;
import com.gclife.product.core.jooq.tables.pojos.CashValuePo;
import com.gclife.product.dao.CalculateDao;
import com.gclife.product.form.calculate.ApplyRequest;
import com.gclife.product.model.bo.apply.*;
import com.gclife.product.service.caculate.CalculateOption;
import com.gclife.product.service.caculate.convert.ApplyConvert;
import com.gclife.product.service.caculate.validate.parameter.ApplyParameterValidate;
import com.gclife.product.service.product.cashvalue.ProductBaseCashValueBusinessService;
import com.gclife.product.service.product.cashvalue.calculate.CashValueLongBusinessService;
import com.gclife.product.service.product.cashvalue.calculate.CashValueShortBusinessService;
import com.gclife.product.vo.insurnce.policy.PolicyCashValueResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * create 18-8-29
 * description:
 */
@Service
@Slf4j
public class ProductBaseCashValueBusinessServiceImpl extends BaseBusinessServiceImpl implements ProductBaseCashValueBusinessService {

    @Autowired
    ApplyParameterValidate applyParameterValidate;
    @Autowired
    private ApplyConvert applyConvert;
    @Autowired
    private CalculateDao calculateDao;
    @Autowired
    private CalculateOption calculateOption;
    @Autowired
    private ProductCashValueBaseService productCashValueBaseService;
    @Autowired
    private CashCalculateLogDao cashCalculateLogDao;
    @Autowired
    private CashValueLongBusinessService cashValueLongBusinessService;
    @Autowired
    private CashValueShortBusinessService cashValueShortBusinessService;

    /**
     * 查询保单现金价值
     *
     * @return ResultObject<List < PolicyCashValueResponse>>
     */
    @Override
    public ResultObject<List<PolicyCashValueResponse>> queryCashValue(ApplyRequest applyRequest) {
        ResultObject<List<PolicyCashValueResponse>> resultObject = new ResultObject<>();
        List<PolicyCashValueResponse> policyCashValueResponseNew = new ArrayList<>();
        try {
            //1.数据验证
            applyParameterValidate.calculateParameterValidateApplyRequest(applyRequest);
            //2.转换成内部数据
            Apply apply = this.applyConvert.calculateConvertApply(InsuranceProductEnum.INSURANCE_APPLY_OPTION_MODE.REAL, applyRequest);
            apply.getListInsured().forEach(insured -> {
                insured.getListCoverage().forEach(coverage -> {
                    List<CoverageLevel> listCoverageLevel = coverage.getListCoverageLevel();
                    if (AssertUtils.isNotEmpty(listCoverageLevel)) {
                        listCoverageLevel.forEach(coverageLevel -> {
                            this.transPolicyCashValue(policyCashValueResponseNew, apply, insured, coverage, coverageLevel);
                        });
                    } else {
                        this.transPolicyCashValue(policyCashValueResponseNew, apply, insured, coverage, null);
                    }
                });
            });
            resultObject.setData(policyCashValueResponseNew);
        } catch (Exception e) {
            e.printStackTrace();
            this.setResultObjectException(log, resultObject, e, InsuranceErrorEnum.PRODUCT_QUERY_DETAIL_ERROR);
        }
        return resultObject;
    }

    private void transPolicyCashValue(List<PolicyCashValueResponse> policyCashValueResponseNew, Apply apply, Insured insured, Coverage coverage, CoverageLevel coverageLevel) {
        try {
            List<CalculateModePo> calculateModePos = calculateDao.queryCalculateMode(coverage.getProductId(), InsuranceProductEnum.CALCULATE_MODE_TYPE.CASH_VALUE.name());
            if (AssertUtils.isNotNull(calculateModePos)) {
                for (CalculateModePo calculateModePo : calculateModePos) {
                    List<CashValuePo> cashValuePos = (List<CashValuePo>) this.calculateOption.optionCalculateList(CashValuePo.class, calculateModePo.getCalculateCode(), null, apply.getApplicant(), insured, coverage, null, null, coverageLevel);
                    if (!AssertUtils.isNotEmpty(cashValuePos) || (!AssertUtils.isNotNull(cashValuePos.get(0).getPolicyYear()) && !AssertUtils.isNotNull(cashValuePos.get(0).getPolicyMonth()))) {
                        return;
                    }
                    List<PolicyCashValueResponse> policyCashValueResponses = new ArrayList<>();
                    cashValuePos.forEach(cashValuePo -> {
                        //传了保单年度和保单月度
                        if (AssertUtils.isNotNull(coverage.getPolicyYear()) && !AssertUtils.isNotNull(coverage.getPolicyMonth())) {
                            if (AssertUtils.isNotNull(cashValuePo.getPolicyYear()) && Objects.equals(coverage.getPolicyYear(), cashValuePo.getPolicyYear())) {
                                transferPolicyCashValuesResponse(coverage, policyCashValueResponses, cashValuePo, coverageLevel);
                            }
                        }
                        if (!AssertUtils.isNotNull(coverage.getPolicyYear()) && AssertUtils.isNotNull(coverage.getPolicyMonth())) {
                            if (AssertUtils.isNotNull(cashValuePo.getPolicyMonth()) && Objects.equals(coverage.getPolicyMonth(), cashValuePo.getPolicyMonth())) {
                                transferPolicyCashValuesResponse(coverage, policyCashValueResponses, cashValuePo, coverageLevel);
                            }
                        }
                        if (AssertUtils.isNotNull(coverage.getPolicyYear()) && AssertUtils.isNotNull(coverage.getPolicyMonth())) {
                            if (AssertUtils.isNotNull(cashValuePo.getPolicyYear()) && Objects.equals(coverage.getPolicyYear(), cashValuePo.getPolicyYear())) {
                                transferPolicyCashValuesResponse(coverage, policyCashValueResponses, cashValuePo, coverageLevel);
                            } else if (AssertUtils.isNotNull(cashValuePo.getPolicyMonth()) && Objects.equals(coverage.getPolicyMonth(), cashValuePo.getPolicyMonth())) {
                                transferPolicyCashValuesResponse(coverage, policyCashValueResponses, cashValuePo, coverageLevel);
                            }
                        } else {
                            transferPolicyCashValuesResponse(coverage, policyCashValueResponses, cashValuePo, coverageLevel);
                        }
                    });
                    policyCashValueResponseNew.addAll(policyCashValueResponses);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(InsuranceErrorEnum.PRODUCT_QUERY_DETAIL_ERROR);
        }
    }


    /**
     * 保全退保现金价值计算
     * 年缴: (1 − 𝑠) × 𝑡.𝐶𝑉 𝑥 + 𝑠 × 𝑡+1.𝐶𝑉 𝑥 + 𝐺𝑃𝑥,1 × (1 − 𝑒𝑡) × (1 − s)
     * 代表保单当年度已经过期间 s=min(保单当年度已经过的天数/365， 1)； 保单年度已经过的天数(若当年度已经缴费,且当年度过了100天则按当年度计算100/365,若当年度未缴费则按照上年度已经过天数365/365)
     * ppp:代表保险缴费年期；
     * 𝑒𝑡:代表第 t 个保单年度附加保险费率
     *
     * @param applyRequest 请求参数
     * @return 查询保单现金价值
     */
    @Override
    public ResultObject<List<PolicyCashValueResponse>> queryEndorseRetreatsCashValue(ApplyRequest applyRequest) {
        log.info("保单退保现金价值计算请求参数:" + JSON.toJSONString(applyRequest));
        CashCalculateLogPo cashCalculateLog = new CashCalculateLogPo();
        cashCalculateLog.setCashCalculateLogId(UUIDUtils.getUUIDShort());
        cashCalculateLog.setRequestJson(JSON.toJSONString(applyRequest));
        cashCalculateLog.setCreatedDate(System.currentTimeMillis());
        cashCalculateLog.setCreatedDateStr(DateUtils.timeStrToString(System.currentTimeMillis(), DateUtils.FORMATE6));
        ResultObject<List<PolicyCashValueResponse>> resultObject = new ResultObject<>();
        List<PolicyCashValueResponse> policyCashValueResponseNew = new ArrayList<>();
        try {
            //1.数据验证
            applyParameterValidate.calculateParameterValidateApplyRequestRetreatCashValue(applyRequest);
            //2.转换成内部数据
            Apply apply = this.applyConvert.calculateConvertApply(InsuranceProductEnum.INSURANCE_APPLY_OPTION_MODE.REAL, applyRequest);
            cashCalculateLog.setTransferJson(JSON.toJSONString(apply));
            apply.getListInsured().forEach(insured -> {
                insured.getListCoverage().forEach(coverage -> {
                    List<CoverageLevel> listCoverageLevel = coverage.getListCoverageLevel();
                    List<Duty> listCoverageDuty = coverage.getListCoverageDuty();
                    //责任多档次处理
                    if (AssertUtils.isNotEmpty(listCoverageDuty) && AssertUtils.isNotEmpty(listCoverageDuty.get(0).getListCoverageLevel())) {
                        listCoverageDuty.forEach(duty -> {
                            duty.getListCoverageLevel().forEach(coverageLevel -> {
                                this.transEndorseRetreatsCashValue(policyCashValueResponseNew, apply, insured, coverage, coverageLevel, duty);
                            });
                        });
                    }
                    //多档次处理
                    else if (AssertUtils.isNotEmpty(listCoverageLevel)) {
                        listCoverageLevel.forEach(coverageLevel -> {
                            this.transEndorseRetreatsCashValue(policyCashValueResponseNew, apply, insured, coverage, coverageLevel, null);
                        });
                    } else {
                        this.transEndorseRetreatsCashValue(policyCashValueResponseNew, apply, insured, coverage, null, null);
                    }

                });
            });
            cashCalculateLog.setResponseJson(JSON.toJSONString(policyCashValueResponseNew));
            //保存日志
            try {
                cashCalculateLog.setStatus(BaseErrorConfigEnum.SUCCESS.name());
                cashCalculateLogDao.insert(cashCalculateLog);
            } catch (Exception e) {
            }
            resultObject.setData(policyCashValueResponseNew);
        } catch (Exception e) {
            e.printStackTrace();
            cashCalculateLog.setStatus(BaseErrorConfigEnum.FAIL.name());
            cashCalculateLogDao.insert(cashCalculateLog);
            this.setResultObjectException(log, resultObject, e, InsuranceErrorEnum.PRODUCT_QUERY_DETAIL_ERROR);
        }
        return resultObject;
    }

    private void transEndorseRetreatsCashValue(List<PolicyCashValueResponse> policyCashValueResponseNew, Apply apply, Insured insured, Coverage coverage, CoverageLevel coverageLevel, Duty duty) {
        try {
            PolicyCashValueResponse policyCashValueResponse = new PolicyCashValueResponse();
            List<CalculateModePo> calculateModePos = calculateDao.queryCalculateMode(coverage.getProductId(), InsuranceProductEnum.CALCULATE_MODE_TYPE.CASH_VALUE.name());
            if (AssertUtils.isNotNull(calculateModePos)) {
                for (CalculateModePo calculateModePo : calculateModePos) {

                    //设置五号产品初始化值
                    calculateOption.setProductLoanData(apply, coverage);

                    List<CashValuePo> cashValuePos = (List<CashValuePo>) this.calculateOption.optionCalculateList(CashValuePo.class,
                                    calculateModePo.getCalculateCode(),
                                    apply,
                                    apply.getApplicant(),
                                    insured,
                                    coverage,
                                    duty,
                                    null,
                                    coverageLevel);
                    policyCashValueResponse.getFactor().put("cashValue", cashValuePos);
                    //计算退费现金价值
                    if (!AssertUtils.isNotEmpty(cashValuePos) || (!AssertUtils.isNotNull(cashValuePos.get(0).getPolicyYear()) && !AssertUtils.isNotNull(cashValuePos.get(0).getPolicyMonth()))) {
                        return;
                    }
                    //通用数据校验
                    applyParameterValidate.calculateParameterValidateRetreatCashValue(coverage);
                    BigDecimal cashValue = new BigDecimal(0.00);
                    //判断短期险还是长期险
                    if (coverage.getCoveragePeriod() == 1 && InsuranceProductEnum.INSURANCE_PRODUCT_COVERAGE_PERIOD_UNIT.YEAR.name().equals(coverage.getCoveragePeriodUnit())) {
                        cashValue = cashValueShortBusinessService.calculateRetreatsCashValue(apply, insured, coverage, coverageLevel, cashValuePos, policyCashValueResponse);
                    } else {
                        cashValue = cashValueLongBusinessService.calculateRetreatsCashValue(apply, insured, coverage, coverageLevel, cashValuePos, policyCashValueResponse);
                    }
                    //添加到返回数据集合
                    policyCashValueResponse.setDutyId(AssertUtils.isNotNull(duty) ? duty.getDutyId() : null);
                    policyCashValueResponse.setProductId(coverage.getProductId());
                    policyCashValueResponse.setCashValue(cashValue);
                    policyCashValueResponse.setCoverageId(coverage.getCoverageId());
                    String productLevel = AssertUtils.isNotNull(coverageLevel) ? coverageLevel.getProductLevel() : coverage.getProductLevel();
                    policyCashValueResponse.setProductLevel(productLevel);
                    policyCashValueResponseNew.add(policyCashValueResponse);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(InsuranceErrorEnum.PRODUCT_QUERY_DETAIL_ERROR);
        }
    }

    /**
     * @param coverage                 险种
     * @param policyCashValueResponses 现金价值返回列表
     * @param cashValuePo              现金价值
     * @param coverageLevel
     */
    private void transferPolicyCashValuesResponse(Coverage coverage, List<PolicyCashValueResponse> policyCashValueResponses, CashValuePo cashValuePo, CoverageLevel coverageLevel) {
        PolicyCashValueResponse policyCashValueResponse = new PolicyCashValueResponse();
        policyCashValueResponse.setProductId(coverage.getProductId());
        policyCashValueResponse.setAge(AssertUtils.isNotNull(cashValuePo.getAge()) ? cashValuePo.getAge() : 0);
        policyCashValueResponse.setPolicyYear(AssertUtils.isNotNull(cashValuePo.getPolicyYear()) ? cashValuePo.getPolicyYear() : 0);
        policyCashValueResponse.setPolicyMonth(AssertUtils.isNotNull(cashValuePo.getPolicyMonth()) ? cashValuePo.getPolicyMonth() : 0);
        policyCashValueResponse.setCashValue(cashValuePo.getCashValue());
        String productLevel = AssertUtils.isNotNull(coverageLevel) ? coverageLevel.getProductLevel() : coverage.getProductLevel();
        policyCashValueResponse.setProductLevel(productLevel);
        policyCashValueResponse.setCoverageId(coverage.getCoverageId());
        policyCashValueResponses.add(policyCashValueResponse);
    }
}
