package com.gclife.product.service.caculate.pretreatment;

import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.product.base.model.config.insurance.InsuranceErrorEnum;
import com.gclife.product.base.model.config.insurance.InsuranceProductEnum;
import com.gclife.product.core.jooq.tables.pojos.ProductAttributesPo;
import com.gclife.product.core.jooq.tables.pojos.ProductRelationPo;
import com.gclife.product.dao.ParamterExtDao;
import com.gclife.product.dao.ProductExtDao;
import com.gclife.product.model.bo.apply.Applicant;
import com.gclife.product.model.bo.apply.Apply;
import com.gclife.product.model.bo.apply.Coverage;
import com.gclife.product.model.bo.apply.Insured;
import com.gclife.product.model.bo.insurance.paramter.DutyControlBo;
import com.gclife.product.model.bo.insurance.paramter.DutyControlParamterBo;
import com.gclife.product.service.caculate.query.ApplyDataQuery;
import com.gclife.product.service.caculate.validate.business.CoverageBusinessValidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 2024/5/24 13:12
 * description:
 */
@Component
@Slf4j
public class ApplyDataPretreatment extends BaseBusinessServiceImpl {
    @Autowired
    private ParamterExtDao paramterExtDao;
    @Autowired
    private ProductExtDao productExtDao;
    @Autowired
    private ApplyDataQuery applyDataQuery;
    @Autowired
    private CoverageBusinessValidate coverageBusinessValidate;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;

    /**
     * 验证投保单参数是否正确
     *
     * @param apply                           投保单
     */
    public void pretreatment(Apply apply) {
        // FIRST 代表计划书投保选择主险的时候
        if (!"FIRST".equals(apply.getOptionType())) {
            return;
        }
        //设置初始化数据
        this.setApplyInitialData(apply);

        //处理投保人参数对象
        this.optionApplicant(apply, apply.getApplicant());

        //处理被保人参数对象
        this.optionInsured(apply, apply.getListInsured());

        //设置转换后数据
        this.setApplyLastData(apply);
    }

    private void setApplyLastData(Apply apply) {

    }

    private void optionInsured(Apply apply, List<Insured> listInsured) {
        listInsured.forEach(insured -> {
            this.optionCoverage(apply, insured, insured.getListCoverage());
            //设置被保人最后的数据
            this.setInsuredLastData(apply, insured);
        });
    }

    private void optionCoverage(Apply apply, Insured insured, List<Coverage> listCoverage) {
        //查询机构树
        List<BranchResponse> branchResponses = Optional.ofNullable(this.platformBranchBaseApi.queryBranchParentListById(apply.getBranchId()).getData()).orElse(new ArrayList<>());
        AssertUtils.isNotNull(log, branchResponses, InsuranceErrorEnum.PRODUCT_CALCULATE_QUERY_AGENT_BRANCH_IS_NOT_NULL);

        List<ProductAttributesPo> productAttributesPos = productExtDao.queryProductAttributesPo(branchResponses.stream().map(BranchResponse::getBranchId).collect(Collectors.toList()), InsuranceProductEnum.ATTRIBUTE_TYPE_CODE.DEFAULT_SELECTION_ADDITIONAL.name());
        if (!AssertUtils.isNotEmpty(productAttributesPos)) {
            return;
        }

        Coverage mainCoverage = insured.getMainCoverage();
        String mainProductId = mainCoverage.getProductId();
        List<ProductRelationPo> productRelationPos = productExtDao.queryProductRelationPo(mainProductId);
        if (!AssertUtils.isNotEmpty(productRelationPos)) {
            return;
        }
        List<String> coverageProductIds = listCoverage.stream().map(Coverage::getProductId).collect(Collectors.toList());
        List<String> pretreatmentProductIds = new ArrayList<>();
        // 找出计划书投保时默认选中该附加险 && 该主险可选此附加险 && 客户没有选中该附加险的产品ID
        productAttributesPos.forEach(productAttributesPo -> {
            productRelationPos.stream().filter(productRelationPo ->
                            productAttributesPo.getProductId().equals(productRelationPo.getRelationProductId())
                                    && !coverageProductIds.contains(productAttributesPo.getProductId())
                    )
                    .findFirst().ifPresent(productRelationPo -> pretreatmentProductIds.add(productRelationPo.getRelationProductId()));
        });
        if (!AssertUtils.isNotEmpty(pretreatmentProductIds)) {
            return;
        }
        // 需要预处理的产品
        for (String pretreatmentProductId : pretreatmentProductIds) {
            // 1.查询产品的动态属性，赋值到新算费对象
            List<DutyControlBo> dutyControlBos = paramterExtDao.queryDutyControlsByProduct(pretreatmentProductId, mainProductId);
            List<DutyControlParamterBo> dutyControlParamterBos = new ArrayList<>();
            for (DutyControlBo dutyControlBo : dutyControlBos) {
                if ("PRODUCT_LEVEL".equals(dutyControlBo.getFieldCode())) {
                    dutyControlParamterBos = dutyControlBo.getDutyControlParamterBos();
                }
            }

            boolean passFlag = false;

            Coverage coverage = new Coverage();
            coverage.setProductId(pretreatmentProductId);

            for (DutyControlParamterBo dutyControlParamterBo : dutyControlParamterBos) {
                if (!passFlag) {
                    try {
                        Apply newApply = new Apply();
                        ClazzUtils.copyPropertiesIgnoreNull(apply, newApply);

                        // 16A，当主险SI <=500k时，自动选择选项1
                        // 16A，当主险SI >500K，自动选择选项2
                        if (AssertUtils.isNotNull(mainCoverage.getAmount()) && AssertUtils.isNotNull(dutyControlParamterBo.getMinValue()) && AssertUtils.isNotNull(dutyControlParamterBo.getMaxValue())) {
                            if (!(mainCoverage.getAmount().compareTo(BigDecimal.valueOf(dutyControlParamterBo.getMinValue())) > 0 && mainCoverage.getAmount().compareTo(BigDecimal.valueOf(dutyControlParamterBo.getMaxValue())) <= 0)) {
                                continue;
                            }
                        }
                        coverage.setProductLevel(dutyControlParamterBo.getParameterValue());
                        List<Coverage> listCoverage1 = new ArrayList<>();
                        ClazzUtils.copyPropertiesIgnoreNull(newApply.getListInsured().get(0).getListCoverage(), listCoverage1);
                        listCoverage1.add(coverage);
                        Insured newInsured = new Insured();
                        ClazzUtils.copyPropertiesIgnoreNull(insured, newInsured);
                        newInsured.setListCoverage(listCoverage1);
                        newApply.setListInsured(new ArrayList<>());
                        newApply.getListInsured().add(newInsured);
                        newApply.setControlPlaceMode(InsuranceProductEnum.CONTROL_PLACE_MODE.CALCULATE_PREMIUM_PRE);
                        applyDataQuery.queryApply(newApply);
                        // 2.看新算费对象能否通过该产品自身的产品校验
                        coverageBusinessValidate.businessValidateCoverage(newApply, newApply.getListInsured().get(0), coverage);
                        // 不报错代表通过校验
                        passFlag = true;
                    } catch (Exception e) {
                    }
                }
            }
            // 3.通过校验，赋值到原有算费对象，流程结束
            if (passFlag) {
                apply.getListInsured().get(0).getListCoverage().add(coverage);
            }
        }
    }

    private void setInsuredLastData(Apply apply, Insured insured) {

    }

    private void optionApplicant(Apply apply, Applicant applicant) {

    }

    private void setApplyInitialData(Apply apply) {

    }
}
